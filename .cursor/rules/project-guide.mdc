---
description: 
globs: 
alwaysApply: false
---
# 项目开发指南

## 项目结构

### 目录结构总览
```
src/main/
├── java/
│   └── com/
│       └── xc/
│           └── boot/
│               ├── XcApplication.java     # 应用程序入口
│               ├── config/               # 配置类目录
│               ├── system/               # 系统功能
│               ├── shared/               # 共享组件
│               ├── core/                 # 核心业务逻辑
│               ├── modules/              # 功能模块
│               └── common/               # 通用工具
└── resources/
    ├── application.yml          # 主配置文件
    ├── application-dev.yml      # 开发环境配置
    ├── application-local.yml    # 本地环境配置
    ├── logback-spring.xml       # 日志配置
    ├── banner.txt              # 启动横幅
    ├── codegen.yml             # 代码生成配置
    ├── mapper/                 # MyBatis映射文件
    ├── templates/              # 模板文件
    ├── META-INF/              # 元数据配置
    └── data/                  # 数据文件
```

### 核心包结构说明

1. **入口类**
   - [XcApplication.java](mdc:src/main/java/com/xc/boot/XcApplication.java)
   - Spring Boot 应用程序的主入口类
   - 包含 `@EnableScheduling` 注解支持定时任务
   - 负责应用程序的初始化和启动

2. **配置目录 (config/)**
   - 框架配置类
     - `com.xc.boot.config.security` - Spring Security 安全配置
     - `com.xc.boot.config.web` - Web MVC 配置
     - `com.xc.boot.config.mybatis` - MyBatis 配置
     - `com.xc.boot.config.redis` - Redis 缓存配置
     - `com.xc.boot.config.swagger` - API 文档配置
     - `com.xc.boot.config.async` - 异步任务配置
   - 数据源配置
     - `com.xc.boot.config.datasource` - 主数据源配置
     - `com.xc.boot.config.datasource.dynamic` - 动态数据源配置
   - 安全配置
     - `com.xc.boot.config.security.jwt` - JWT 认证配置
     - `com.xc.boot.config.security.oauth2` - OAuth2 配置
   - 缓存配置
     - `com.xc.boot.config.cache` - 本地缓存配置
     - `com.xc.boot.config.cache.redis` - Redis 缓存配置
   - 消息队列配置
     - `com.xc.boot.config.mq` - 消息队列基础配置
     - `com.xc.boot.config.mq.rabbitmq` - RabbitMQ 配置
     - `com.xc.boot.config.mq.kafka` - Kafka 配置
   - 线程池配置
     - `com.xc.boot.config.thread` - 线程池配置
     - `com.xc.boot.config.thread.async` - 异步线程池配置

3. **系统功能目录 (system/)**
   - 用户认证与授权
     - `com.xc.boot.system.auth` - 认证相关
     - `com.xc.boot.system.permission` - 权限管理
     - `com.xc.boot.system.role` - 角色管理
   - 系统管理功能
     - `com.xc.boot.system.user` - 用户管理
     - `com.xc.boot.system.dept` - 部门管理
     - `com.xc.boot.system.menu` - 菜单管理
   - 权限控制
     - `com.xc.boot.system.security` - 安全控制
     - `com.xc.boot.system.acl` - 访问控制
   - 系统监控
     - `com.xc.boot.system.monitor` - 系统监控
     - `com.xc.boot.system.log` - 日志管理
   - 日志管理
     - `com.xc.boot.system.log.operation` - 操作日志
     - `com.xc.boot.system.log.login` - 登录日志
     - `com.xc.boot.system.log.error` - 错误日志
   - 定时任务
     - `com.xc.boot.system.schedule` - 定时任务管理
     - `com.xc.boot.system.job` - 任务调度

4. **共享组件目录 (shared/)**
   - 公共服务接口
     - `com.xc.boot.shared.service` - 基础服务接口
     - `com.xc.boot.shared.feign` - 微服务接口
   - 共享数据模型
     - `com.xc.boot.shared.model` - 基础数据模型
     - `com.xc.boot.shared.dto` - 数据传输对象
   - 业务事件定义
     - `com.xc.boot.shared.event` - 事件定义
     - `com.xc.boot.shared.listener` - 事件监听器
   - 共享工具类
     - `com.xc.boot.shared.util` - 通用工具
     - `com.xc.boot.shared.helper` - 辅助类
   - 通用验证器
     - `com.xc.boot.shared.validator` - 数据验证
     - `com.xc.boot.shared.checker` - 业务检查
   - 共享常量
     - `com.xc.boot.shared.constant` - 系统常量
     - `com.xc.boot.shared.enums` - 枚举定义

5. **核心业务目录 (core/)**
   - 核心业务逻辑实现
     - `com.xc.boot.core.service` - 核心服务
     - `com.xc.boot.core.manager` - 业务管理
   - 领域模型
     - `com.xc.boot.core.domain` - 领域对象
     - `com.xc.boot.core.aggregate` - 聚合根
   - 业务服务
     - `com.xc.boot.core.biz` - 业务逻辑
     - `com.xc.boot.core.facade` - 外观模式
   - 数据访问层
     - `com.xc.boot.core.repository` - 仓储接口
     - `com.xc.boot.core.mapper` - MyBatis 映射
   - 业务规则
     - `com.xc.boot.core.rule` - 业务规则
     - `com.xc.boot.core.policy` - 策略模式
   - 核心算法
     - `com.xc.boot.core.algorithm` - 核心算法
     - `com.xc.boot.core.calculator` - 计算器

6. **功能模块目录 (modules/)**
   ```
   modules/
   ├── module1/
   │   ├── controller/    # REST API 控制器
   │   │   ├── UserController.java
   │   │   ├── OrderController.java
   │   │   └── ProductController.java
   │   ├── service/      # 服务层
   │   │   ├── impl/     # 服务实现
   │   │   └── facade/   # 外观接口
   │   ├── repository/   # 数据访问层
   │   │   ├── entity/   # 实体类
   │   │   └── mapper/   # MyBatis 映射
   │   ├── model/        # 数据模型
   │   │   ├── dto/      # 数据传输对象
   │   │   └── po/       # 持久化对象
   │   └── dto/          # 数据传输对象
   │       ├── request/  # 请求对象
   │       └── response/ # 响应对象
   └── module2/
       └── ...
   ```

7. **通用工具目录 (common/)**
   - 工具类
     - `com.xc.boot.common.util` - 通用工具
     - `com.xc.boot.common.helper` - 辅助类
   - 常量定义
     - `com.xc.boot.common.constant` - 常量定义
     - `com.xc.boot.common.enums` - 枚举定义
   - 自定义注解
     - `com.xc.boot.common.annotation` - 注解定义
     - `com.xc.boot.common.aspect` - 切面实现
   - 异常类
     - `com.xc.boot.common.exception` - 异常定义
     - `com.xc.boot.common.handler` - 异常处理
   - 通用枚举
     - `com.xc.boot.common.enums` - 枚举定义
     - `com.xc.boot.common.constant` - 常量定义
   - 响应封装
     - `com.xc.boot.common.response` - 响应对象
     - `com.xc.boot.common.result` - 结果封装

## 配置管理

### 主要配置文件

1. **应用配置**
   - [application.yml](mdc:src/main/resources/application.yml)
     - 基础配置文件
     - 包含应用程序的基本配置
     - 环境无关的通用配置
     - 数据库连接池配置
     - 服务器配置

   - [application-dev.yml](mdc:src/main/resources/application-dev.yml)
     - 开发环境配置
     - 开发环境特定的配置项
     - 数据库连接信息
     - 日志级别设置
     - 调试选项

2. **代码生成配置**
   - [codegen.yml](mdc:src/main/resources/codegen.yml)
     - 代码生成规则
     - 模板配置
     - 生成路径设置

3. **日志配置**
   - [logback-spring.xml](mdc:src/main/resources/logback-spring.xml)
     - 日志输出格式
     - 日志文件路径
     - 日志级别控制
     - 日志滚动策略

### 配置最佳实践
1. 环境相关的配置应放在对应的 profile 配置文件中
2. 敏感信息（如密码、密钥）应使用环境变量或加密配置
3. 生产环境配置文件不应包含开发环境的调试选项
4. 配置修改后应及时更新文档

## 开发规范

### 基本约定

1. **路由与参数**
   - 项目路由一律不使用路径参数，确保路由设计简洁统一
   - 请求参数使用 POST 方法传递
   - 响应数据统一使用 Result 对象封装

2. **数据库查询**
   - 数据库操作一律按照 mybatis-flex 规范开发
   - 优先使用 APT 方式生成查询代码：
     ```java
     // 1. 静态导入 TableDef
     import static com.xc.boot.modules.goods.model.entity.table.GoodsTableDef.GOODS;
     import static com.xc.boot.modules.goods.model.entity.table.CategoryTableDef.CATEGORY;
     
     // 2. 实体类定义
     @Table("goods")
     public class Goods {
         @Id
         private Long id;
         private String name;
         private BigDecimal price;
         private Integer stockNum;    // 数据库字段为 STOCK_NUM
         private Date createTime;     // 数据库字段为 CREATE_TIME
         private Long categoryId;     // 数据库字段为 CATEGORY_ID
         // ... 其他字段
     }
     
     // 3. 构建查询，直接使用静态导入的 TableDef
     QueryWrapper query = QueryWrapper.create()
         .select(GOODS.ID, GOODS.NAME, GOODS.PRICE)
         .where(GOODS.STOCK_NUM.gt(0))
         .and(GOODS.CREATE_TIME.gt(LocalDateTime.now().minusDays(7)));
     
     // 4. 执行查询
     List<Goods> list = goodsMapper.selectListByQuery(query);
     ```
   - TableDef 静态导入规范：
     - 导入路径格式：`com.xc.boot.modules.{模块名}.model.entity.table.{实体名}TableDef.{实体名}`
     - 示例：
       ```java
       // 商品模块
       import static com.xc.boot.modules.goods.model.entity.table.GoodsTableDef.GOODS;
       import static com.xc.boot.modules.goods.model.entity.table.GoodsHasColumnsTableDef.GOODS_HAS_COLUMNS;
       
       // 订单模块
       import static com.xc.boot.modules.order.model.entity.table.OrderTableDef.ORDER;
       import static com.xc.boot.modules.order.model.entity.table.OrderItemTableDef.ORDER_ITEM;
       
       // 用户模块
       import static com.xc.boot.modules.user.model.entity.table.UserTableDef.USER;
       import static com.xc.boot.modules.user.model.entity.table.UserRoleTableDef.USER_ROLE;
       ```
     - 实际应用参考：ListFillService.java 中的静态导入示例：
       ```java
       // 系统模块
       import static com.xc.boot.system.model.entity.table.MerchantTableDef.MERCHANT;
       import static com.xc.boot.system.model.entity.table.PrintTagTableDef.PRINT_TAG;
       import static com.xc.boot.system.model.entity.table.SysRoleTableDef.SYS_ROLE;
       import static com.xc.boot.system.model.entity.table.SysUserMerchantTableDef.SYS_USER_MERCHANT;
       import static com.xc.boot.system.model.entity.table.SysUserRoleTableDef.SYS_USER_ROLE;
       import static com.xc.boot.system.model.entity.table.SysUserTableDef.SYS_USER;
       
       // 商品模块
       import static com.xc.boot.modules.goods.model.entity.table.GoodsHasColumnsTableDef.GOODS_HAS_COLUMNS;
       import static com.xc.boot.modules.goods.model.entity.table.GoodsHasImagesTableDef.GOODS_HAS_IMAGES;
       
       // 商户模块
       import static com.xc.boot.modules.merchant.model.entity.table.BrandTableDef.BRAND;
       import static com.xc.boot.modules.merchant.model.entity.table.CounterTableDef.COUNTER;
       import static com.xc.boot.modules.merchant.model.entity.table.GoodsColumnTableDef.GOODS_COLUMN;
       import static com.xc.boot.modules.merchant.model.entity.table.GoodsIncomeTemplateTableDef.GOODS_INCOME_TEMPLATE;
       import static com.xc.boot.modules.merchant.model.entity.table.JewelryTableDef.JEWELRY;
       import static com.xc.boot.modules.merchant.model.entity.table.QualityTableDef.QUALITY;
       import static com.xc.boot.modules.merchant.model.entity.table.StyleTableDef.STYLE;
       import static com.xc.boot.modules.merchant.model.entity.table.SubclassTableDef.SUBCLASS;
       import static com.xc.boot.modules.merchant.model.entity.table.SupplierTableDef.SUPPLIER;
       import static com.xc.boot.modules.merchant.model.entity.table.TechnologyTableDef.TECHNOLOGY;
       ```
     - ListFillService.java 中的查询示例：
       ```java
       // 1. 用户角色查询
       QueryWrapper.create()
           .leftJoin(SYS_ROLE).on(SYS_USER_ROLE.ROLE_ID.eq(SYS_ROLE.ID))
           .where(SYS_USER_ROLE.USER_ID.in(keys))
           .select(SYS_ROLE.NAME,
                   SYS_USER_ROLE.USER_ID,
                   SYS_ROLE.ID)
       
       // 2. 用户商店查询
       QueryWrapper.create()
           .leftJoin(MERCHANT).on(SYS_USER_MERCHANT.MERCHANT_ID.eq(MERCHANT.ID))
           .where(SYS_USER_MERCHANT.USER_ID.in(keys))
           .select(MERCHANT.NAME,
                   SYS_USER_MERCHANT.USER_ID,
                   MERCHANT.ID)
       
       // 3. 自定义字段查询
       QueryWrapper.create()
           .leftJoin(GOODS_COLUMN).on(GOODS_COLUMN.ID.eq(GOODS_HAS_COLUMNS.COLUMN_ID))
           .where(GOODS_HAS_COLUMNS.GOODS_ID.in(keys))
           .where(GOODS_COLUMN.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
           .select(GOODS_HAS_COLUMNS.COLUMN_SIGN.as("columnSign"),
                   GOODS_HAS_COLUMNS.VALUE.as("value"),
                   GOODS_COLUMN.TYPE.as("type"))
       ```
   - 使用静态导入的 TableDef 构建查询：
     ```java
     // 单表查询
     QueryWrapper query = QueryWrapper.create()
         .select(GOODS.ID, GOODS.NAME)
         .where(GOODS.STATUS.eq(1));
     
     // 关联查询
     QueryWrapper query = QueryWrapper.create()
         .select(
             GOODS.ID,
             GOODS.NAME,
             CATEGORY.NAME.as("categoryName")
         )
         .leftJoin(CATEGORY).on(GOODS.CATEGORY_ID.eq(CATEGORY.ID))
         .where(GOODS.STATUS.eq(1));
     
     // 多表关联
     QueryWrapper query = QueryWrapper.create()
         .select(
             ORDER.ID,
             ORDER.ORDER_NO,
             USER.USERNAME,
             ORDER_ITEM.GOODS_NAME
         )
         .leftJoin(USER).on(ORDER.USER_ID.eq(USER.ID))
         .leftJoin(ORDER_ITEM).on(ORDER.ID.eq(ORDER_ITEM.ORDER_ID))
         .where(ORDER.STATUS.eq(1));
     ```
   - 复杂查询示例：
     ```java
     // 1. 子查询
     QueryWrapper subQuery = QueryWrapper.create()
         .select(GOODS.ID)
         .where(GOODS.PRICE.gt(new BigDecimal("1000")));
     
     // 2. 主查询
     QueryWrapper query = QueryWrapper.create()
         .select(GOODS.allColumns())
         .where(GOODS.ID.in(subQuery));
     
     // 3. 排序和分页
     query.orderBy(GOODS.PRICE.desc())
         .limit(10);
     ```
   - 批量操作示例：
     ```java
     // 1. 批量插入
     List<Goods> goodsList = new ArrayList<>();
     // ... 填充数据
     goodsMapper.insertBatch(goodsList);
     
     // 2. 批量更新
     QueryWrapper query = QueryWrapper.create()
         .where(GOODS.ID.in(goodsIds));
     Goods updateEntity = new Goods()
         .setStatus(1);
     goodsMapper.updateByQuery(updateEntity, query);
     ```
   - 关联查询示例：
     ```java
     // 1. 定义关联表
     import static com.xc.boot.modules.goods.model.entity.table.CategoryTableDef.CATEGORY;
     
     // 2. 构建关联查询
     QueryWrapper query = QueryWrapper.create()
         .select(
             GOODS.ID,
             GOODS.NAME,
             CATEGORY.NAME.as("categoryName")
         )
         .leftJoin(CATEGORY).on(GOODS.CATEGORY_ID.eq(CATEGORY.ID))
         .where(GOODS.STATUS.eq(1));
     ```
   - APT 字段命名规范：
     - 实体类中的驼峰命名会自动转换为大写+下划线格式
     - 在 TableDef 中使用时，必须使用大写+下划线格式
     - 常见字段命名示例：
       - `id` -> `ID`
       - `name` -> `NAME`
       - `createTime` -> `CREATE_TIME`
       - `updateTime` -> `UPDATE_TIME`
       - `isDeleted` -> `IS_DELETED`
       - `userId` -> `USER_ID`
       - `orderNo` -> `ORDER_NO`
     - 避免直接使用字符串常量作为字段名
   - 获取字段名通过 TableDef 获取，避免使用字符串常量
   - 基础库表（BaseDbTables.java 中定义）禁止与业务库数据表进行 join 操作
   - 分页查询必须继承 BasePageQuery

3. **工具库使用**
   - 项目已引入 `Hutool` 工具库，优先使用 `Hutool` 提供的方法，避免重复封装
   - 使用 Hutool 的日期工具类处理日期
   - 使用 Hutool 的字符串工具类处理字符串
   - 使用 Hutool 的集合工具类处理集合

4. **类定义**
   - 定义新类前，需检查是否已存在符合需求的类，避免重复定义
   - 类名使用 PascalCase
   - 方法名和变量名使用 camelCase
   - 常量名使用大写下划线 SNAKE_CASE
   - 包名全小写

5. **列表查询**
   - 列表查询接口在无明确排序字段时，默认按 `id` 降序排序
   - 涉及分页的 `Query` 对象需继承 BasePageQuery.java
   - 导出功能使用 ExcelUtil 工具类

6. **文件状态**
   - 涉及文件字段时，保存文件 ID 到业务记录后，需调用 CommonUtils.java 中的 `updateFileStatus` 方法更新文件使用状态
   - 文件上传使用 FileService 服务
   - 文件下载使用 FileService 服务

7. **操作日志**
   - 系统操作日志通过 OpLogUtils.java 中的 `appendOpLog` 方法记录
   - 参数说明:
     - `comment`: 操作日志描述 (格式: 模块-操作)
     - `content`: 操作日志内容
     - `extra`: 额外信息 (如修改前/后数据，可传 `null`)

### 业务约定

1. **商户端功能**
   - 若数据表包含 `company_id` 字段，查询时需添加条件 `company_id = SecurityUtils.getCompanyId()`
   - 若数据表包含 `merchant_id` 字段，查询时需添加条件 `merchant_id in (SecurityUtils.getMerchantIds())`
   - 注意: 若 `SecurityUtils.isMain()` 返回 `true`，表示当前用户为主账号，无需受门店范围约束

2. **表单校验**
   - 表单中的文本字段需添加长度校验，防止数据库越界错误
   - 价格字段应该统一使用 `BigDecimal` 类型处理
   - 使用 Jakarta Validation 注解进行参数校验
   - 自定义校验注解需实现 ConstraintValidator 接口

3. **异常处理**
   - 业务异常使用 CommonUtils.abort 方法抛出
   - 系统异常使用 BusinessException 类
   - 参数校验异常使用 ValidationException 类
   - 权限异常使用 AccessDeniedException 类

4. **枚举定义**
   - 新定义的枚举需实现 IBaseEnum 接口
   - 枚举值使用 Integer 类型
   - 枚举标签使用 String 类型
   - 提供静态方法获取枚举值

5. **分页查询**
   - 涉及分页的 Query 对象需继承 BasePageQuery.java
   - 分页大小限制在 1000 条以内
   - 导出数据限制在 5000 条以内
   - 打印数据限制在 5000 条以内

6. **QueryMethods 优化**
   - 若多次使用 QueryMethods 的方法，需静态导入:
   ```java
   import static com.mybatisflex.core.query.QueryMethods.*;
   ```

7. **数据库表限制**
   - BaseDbTables.java 中的数据表位于基础库，禁止与业务库数据表进行 join 操作
   - 基础库表包括：company, company_settings, file, sys_config 等
   - 业务库表包括：goods, goods_income, goods_sale 等

8. **其他规范**
   - 价格字段应该统一使用 BigDecimal 类型处理
   - 时间字段应该统一使用 Date 类型处理
   - 状态字段应该统一使用 Integer 类型处理
   - 金额计算应该使用 BigDecimal 的 setScale 方法设置精度

## 工具类使用说明

### 常用工具类

1. **CommonUtils**
   - 业务相关工具方法
   - 文件状态更新
   - 异步任务执行
   - 业务异常抛出
   ```java
   // 更新文件状态
   CommonUtils.updateFileStatus(fileId);
   
   // 异步执行任务
   CommonUtils.asyncExecute(() -> {
       // 异步任务代码
   });
   
   // 抛出业务异常
   CommonUtils.abort("错误信息");
   CommonUtils.abortIf(condition, "错误信息");
   ```

2. **PriceUtil**
   - 价格单位转换
   - 金额格式化
   ```java
   // 分转元
   BigDecimal yuan = PriceUtil.fen2yuan(fen);
   
   // 元转分
   BigDecimal fen = PriceUtil.yuan2fen(yuan);
   
   // 格式化金额
   BigDecimal formatted = PriceUtil.formatTwoDecimal(amount);
   ```

3. **StockUtils**
   - 库存统一处理
   - 批量更新库存
   ```java
   // 批量更新库存
   List<StockNumChangeBO> changes = new ArrayList<>();
   changes.add(new StockNumChangeBO()
       .setGoodsId(goodsId)
       .setNum(changeNum)
       .setComment("入库"));
   StockUtils.updateStocks(changes);
   ```

4. **OpLogUtils**
   - 系统操作日志
   ```java
   // 记录操作日志
   OpLogUtils.appendOpLog(
       "模块-操作",  // comment
       "操作内容",   // content
       "额外信息"    // extra
   );
   ```

5. **SnUtils**
   - 条码/单号生成
   ```java
   // 生成入库单号
   String incomeCode = SnUtils.generateIncomeCode();
   
   // 生成货品条码
   String goodsSn = SnUtils.generateGoodsSn();
   ```

6. **SecurityUtils**
   - 获取登录用户信息
   ```java
   // 获取用户ID
   Long userId = SecurityUtils.getUserId();
   
   // 获取商户ID
   Long companyId = SecurityUtils.getCompanyId();
   
   // 获取门店ID列表
   List<Long> merchantIds = SecurityUtils.getMerchantIds();
   ```

7. **ListFillUtil**
   - 列表关联数据填充
   ```java
   // 填充关联数据
   ListFillUtil.of(records)
       .build(listFillService::getUserRoleVosByUserId, ids, "id", "roles")
       .build(listFillService::getUserMerchantVosByUserId, ids, "id", "merchants")
       .handle();
   ```

### 工具类使用规范

1. **优先使用 Hutool**
   - 项目已引入 Hutool 工具库，优先使用其提供的方法
   - 避免重复封装已有功能

2. **工具类命名规范**
   - 工具类名以 Utils 结尾
   - 方法名应清晰表达功能
   - 参数名应具有描述性

3. **工具类方法规范**
   - 方法应该是静态的
   - 方法应该是无状态的
   - 方法应该是线程安全的
   - 方法应该有适当的参数校验

4. **工具类文档规范**
   - 类级别文档说明用途
   - 方法级别文档说明功能
   - 参数说明
   - 返回值说明
   - 异常说明

## 模块依赖关系

```
common <── shared <── core <── modules
            ↑          ↑
            └── system ┘
```

- common: 基础依赖，不依赖其他模块
- shared: 依赖 common
- system: 依赖 common, shared
- core: 依赖 common, shared, system
- modules: 依赖 core 及其他必要模块

## 项目依赖

### 基础环境要求
- JDK 版本：Java 21
- Spring Boot 版本：3.3.6
- Maven 构建工具

### 核心依赖说明
1. **Spring Boot 相关**
   - spring-boot-starter-web：Web 应用开发
   - spring-boot-starter-security：安全框架
   - spring-boot-starter-data-redis：Redis 支持
   - spring-boot-starter-cache：缓存支持
   - spring-boot-starter-aop：面向切面编程
   - spring-boot-starter-validation：参数校验
   - spring-boot-starter-websocket：WebSocket 支持
   - spring-boot-starter-mail：邮件服务

2. **数据库相关**
   - mybatis-flex-spring-boot3-starter：MyBatis Flex ORM 框架
   - mysql-connector-j：MySQL 驱动
   - HikariCP：数据库连接池

3. **工具类库**
   - lombok：简化 Java 代码
   - hutool-all：工具集
   - mapstruct：对象映射
   - knife4j：API 文档
   - easyexcel：Excel 处理
   - velocity：模板引擎

4. **存储服务**
   - minio：对象存储
   - aliyun-sdk-oss：阿里云对象存储

5. **消息和缓存**
   - redisson：分布式锁
   - spring-boot-starter-data-redis：Redis 支持

6. **第三方服务集成**
   - xxl-job-core：分布式任务调度
   - weixin-java-miniapp：微信小程序
   - aliyun-java-sdk-dysmsapi：阿里云短信服务
   - ip2region：IP 地址库

### 版本管理
项目使用 Maven 进行依赖管理，主要版本信息如下：
```xml
<properties>
    <java.version>21</java.version>
    <hutool.version>5.8.27</hutool.version>
    <mysql-connector-j.version>9.1.0</mysql-connector-j.version>
    <hikaricp.version>5.0.1</hikaricp.version>
    <knife4j.version>4.5.0</knife4j.version>
    <mapstruct.version>1.5.5.Final</mapstruct.version>
    <mybatis-flex.version>1.10.5</mybatis-flex.version>
    <redisson.version>3.30.0</redisson.version>
    <weixin-java.version>4.5.5.B</weixin-java.version>
</properties>
```

### 依赖分类说明
1. **编译时依赖**
   - lombok
   - mapstruct
   - mybatis-flex-processor

2. **运行时依赖**
   - spring-boot-starter-*
   - mybatis-flex-spring-boot3-starter
   - mysql-connector-j
   - 其他业务相关依赖

3. **测试依赖**
   - spring-boot-starter-test

4. **可选依赖**
   - minio/aliyun-sdk-oss：根据存储需求选择
   - weixin-java-miniapp：根据微信集成需求选择
   - aliyun-java-sdk-dysmsapi：根据短信服务需求选择

## 导出与打印功能

### 功能概述

系统提供了统一的导出和打印功能实现，基于 `ExcelUtil` 工具类封装。该功能支持:
- 单表数据导出
- 自定义导出配置
- 异步导出处理
- 导出任务管理

### 使用方式

#### 基础导出

```java
// 1. 创建导出工具实例
ExcelUtil.of(mapper, wrapper, clazz, sign, fileName)
    // 2. 设置数据获取方法
    .getData((mapper, wrapper) -> {
        // 返回导出数据列表
        return dataList;
    })
    // 3. 执行导出
    .doExport();
```

#### 打印功能

打印功能复用导出实现，通过 `BasePageQuery` 中的 `print` 标记控制:

```java
if (queryParams.getPrint().equals(1)) {
    // 设置打印参数
    pageNum = 1;
    pageSize = CommonUtils.getMaxPrintSize();
    // 校验数据量
    long count = this.mapper.selectCountByQuery(query);
    Assert.isTrue(count <= CommonUtils.getMaxPrintSize(), 
        String.format("打印数据条数超出最大限制%d条", CommonUtils.getMaxPrintSize()));

    // 返回列表数据
}
```

### 导出配置

#### 基础配置

通过 `ExcelExportConfig` 配置导出参数:

```java
.modifyConfig(config -> {
    // 设置标题
    config.setTitle("导出标题");
    // 设置sheet名称
    config.setSheetName("Sheet1");
    // 设置列宽
    config.setWidth(15);
})
```

#### 表头配置

表头通过 `HeadingColumns` 配置:

```java
List<HeadingColumns> columns = List.of(
    new HeadingColumns()
        .setLabel("列标题")
        .setProp("fieldName")
        .setShow(true)
);
```

### 特殊功能

#### 字典转换

通过 `@Excel` 注解配置字典转换:

```java
@Excel(
    type = 3,
    key = {"0", "1"},
    value = {"禁用", "启用"}
)
private Integer status;
```

#### 集合字段

支持集合类型字段导出:

```java
@Excel(
    type = 4,
    subFieldName = "name"
)
private List<Role> roles;
```

### 注意事项

1. 导出数据量限制:
   - 打印最大限制: 5000条
   - 导出最大限制: 5000条

2. 文件处理:
   - 导出文件临时保存在 `{env}/download/` 目录
   - 导出完成后自动上传到OSS并删除本地文件
   - 文件状态通过 `CommonUtils.updateFileStatus()` 更新

3. 异步处理:
   - 导出任务异步执行
   - 支持任务状态跟踪
   - 支持导出进度查询

4. 性能优化:
   - 使用 `SXSSFWorkbook` 处理大数据量
   - 批量数据处理

### 最佳实践

1. 数据查询优化:
   ```java
   // 只查询导出需要的字段
   query.select(
       QueryMethods.column(Entity::getId),
       QueryMethods.column(Entity::getName)
   );
   ```

2. 关联数据处理:
   ```java
   // 使用ListFillUtil处理关联数据
   ListFillUtil.of(records)
       .build(listFillService::getUserRoleVosByUserId, ids, "id", "roles")
       .handle();
   ```

3. 导出配置示例:
   ```java
   com.xc.boot.common.util.excel.ExcelUtil.of(mapper, wrapper, UserPageVO.class, "user_list", "用户列表")
       .getData((mapper, wrapper) -> {
           List<UserPageVO> list = mapper.selectListByQueryAs(wrapper, UserPageVO.class);
           // 处理关联数据
           if (!list.isEmpty()) {
               Set<Long> ids = list.stream()
                   .map(UserPageVO::getId)
                   .collect(Collectors.toSet());
               ListFillUtil.of(list)
                   .build(listFillService::getUserRoleVosByUserId, ids, "id", "roles")
                   .handle();
           }
           return list;
       })
       .modifyConfig(config -> {
           config.setTitle("用户列表");
           config.setWidth(15);
       })
       .doExport();
   ```

## 数据库批量更新开发示例

### 1. 基本语法

批量更新支持两种实现方式：
1. 使用 `Db.updateBySql` 执行原生 SQL
2. 使用 MyBatis-Flex 的 `QueryWrapper` 构建批量更新

### 2. 实现示例

#### 2.1 使用原生 SQL 实现

```java
// 构建 UNION ALL 子查询
StringBuilder unionSql = new StringBuilder();
for (StockNumChangeBO change : changes) {
    if (unionSql.length() > 0) {
        unionSql.append(" UNION ALL ");
    }
    unionSql.append("SELECT ")
            .append(change.getGoodsId()).append(" as id, ")
            .append(nums.get("num")).append(" as num, ")
            .append(nums.get("stock_num")).append(" as stock_num");
}

// 构建完整的更新 SQL
String sql = "UPDATE goods g " +
        "JOIN (" + unionSql + ") updates ON g.id = updates.id " +
        "SET g.num = updates.num, " +
        "g.stock_num = updates.stock_num " +
        "WHERE g.company_id = " + SecurityUtils.getCompanyId();

// 执行更新
int updatedRows = Db.updateBySql(sql);
```

#### 2.2 使用 QueryWrapper 实现

```java
// 构建批量更新条件
QueryWrapper queryWrapper = QueryWrapper.create()
    .where(GoodsEntity::getId).in(goodsIds)
    .and(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId());

// 构建更新字段
GoodsEntity updateEntity = new GoodsEntity()
    .setNum(newNum)
    .setStockNum(newStockNum);

// 执行更新
int updatedRows = goodsMapper.updateByQuery(updateEntity, queryWrapper);
```

### 3. 最佳实践

1. **性能优化**
   - 批量更新时，建议每批次控制在 1000 条以内
   - 对于大量数据，考虑分批处理
   - 使用事务确保数据一致性

2. **代码规范**
   - 必须添加公司ID条件，确保数据安全
   - 更新后记录操作日志

3. **错误处理**
   - 检查更新结果，确保更新成功
   - 记录更新失败的日志
   - 必要时进行数据回滚

4. **注意事项**
   - 批量更新必须在事务中执行
   - 更新前检查数据权限
   - 更新后同步相关缓存
   - 考虑并发情况下的数据一致性
   - 添加适当的索引提升性能
