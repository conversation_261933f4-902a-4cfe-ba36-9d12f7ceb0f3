package com.xc.boot.common.enums;

import com.xc.boot.common.base.IBaseEnum;
import lombok.Getter;

/**
 * 环境枚举
 *
 * <AUTHOR>
 * @since 4.0.0
 */
@Getter
public enum EnvEnum implements IBaseEnum<String> {

    LOCAL("local", "本地环境"),
    DEV("dev", "开发环境"),
    TEST("test", "测试环境"),
    PROD("prod", "生产环境");

    private final String value;

    private final String label;

    EnvEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }
}
