package com.xc.boot.common.enums;

import lombok.Getter;

/**
 * 关联字段枚举
 */
@Getter
public enum JoinColumEnum {
    COUNTER_ID("counter_id", "counterId"),
    SUPPLIER_ID("supplier_id", "supplierId"),
    CATEGORY_ID("category_id", "categoryId"),
    SUBCLASS_ID("subclass_id", "subclassId"),
    BRAND_ID("brand_id", "brandId"),
    STYLE_ID("style_id", "styleId"),
    QUALITY_ID("quality_id", "qualityId"),
    TECHNOLOGY_ID("technology_id", "technologyId"),
    MAIN_STONE_ID("main_stone_id", "mainStoneId"),
    SUB_STONE_ID("sub_stone_id", "subStoneId"),
    COMPANY_ID("company_id", "companyId"),
    MERCHANT_ID("merchant_id", "merchantId");

    private final String sign;
    private final String labelField;

    JoinColumEnum(String sign, String labelField) {
        this.sign = sign;
        this.labelField = labelField;
    }

    /**
     * 判断是否是关联字段
     */
    public static boolean isJoinColumn(String sign) {
        for (JoinColumEnum value : values()) {
            if (value.sign.equals(sign)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 如果是 xxx_id join字段 则返回xxx,否则返回原值
     */
    public static String getClearColumnName(String sign) {
        if (isJoinColumn(sign)) {
            for (JoinColumEnum value : values()) {
                if (value.sign.equals(sign)) {
                    return value.labelField;
                }
            }
        }
        return sign;
    }
}