package com.xc.boot.common.enums;

import lombok.Getter;

/**
 * 数量字段枚举
 */
@Getter
public enum NumColumEnum {
    NUM("num", "总数"),
    STOCK_NUM("stockNum", "库存"),
    RETURN_NUM("returnNum", "采购退"),
    SOLD_NUM("soldNum", "售出"),
    TRANSFER_NUM("transferNum", "调拨中"),
    FROZEN_NUM("frozenNum", "冻结"),
    NUM_DETAIL("numDetail", "数量情况");

    private final String sign;
    private final String label;

    NumColumEnum(String sign, String label) {
        this.sign = sign;
        this.label = label;
    }

    /**
     * 判断是否是数量字段
     */
    public static boolean isNumColumn(String sign) {
        for (NumColumEnum value : values()) {
            if (value.sign.equals(sign)) {
                return true;
            }
        }
        return false;
    }
}