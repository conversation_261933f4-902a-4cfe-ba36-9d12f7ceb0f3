package com.xc.boot.common.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONNull;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.annotation.Flatten;
import com.xc.boot.common.annotation.GoodsColumn;
import com.xc.boot.common.base.CustomColumnItemDTO;
import com.xc.boot.common.enums.JoinColumEnum;
import com.xc.boot.common.model.Option;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.model.entity.GoodsHasColumnsEntity;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import com.xc.boot.modules.income.model.entity.GoodsIncomeHasColumnsEntity;
import com.xc.boot.modules.merchant.mapper.GoodsColumnMapper;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateDetailEntity;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnSecretLevelEnum;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnTypeEnum;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName ColumnEncyeUtil
 * @Date: 2025/6/12 10:29
 * @Description: 货品字段加密工具类
 */
@Slf4j
@Component
public class ColumnEncryptUtil {
    @SuppressWarnings("unused")
    private static ApplicationContext applicationContext;
    private static GoodsColumnMapper goodsColumnMapper;

    public ColumnEncryptUtil(ApplicationContext applicationContext) {
        ColumnEncryptUtil.applicationContext = applicationContext;
        goodsColumnMapper = applicationContext.getBean(GoodsColumnMapper.class);
    }

    /**
     * 根据当前登录用户，对obj内的对象进行加密,并转为JsonObject
     * @param page 待处理的分页
     */
    public static Page<JSONObject> encrypt(Page<?> page, Class<?> clazz, String columnFieldName) {
        Page<JSONObject> jsonPage = new Page<>();
        BeanUtil.copyProperties(page, jsonPage);
        jsonPage.setRecords(encrypt(page.getRecords(), clazz, columnFieldName));
        return jsonPage;
    }

    /**
     * 根据当前登录用户，对obj内的对象进行加密,并转为JsonObject
     * 如果addList为false,则只会根据sign对注解字段加密，不会添加sign字段
     * eg1:
     * @GoodsColumn(value = "cost_price")
     * costPrice = 123;
     * 加密后
     * costPrice = "***3"
     * cost_price = "***3"
     * 返回的json对象中会同时存在field和sign，两个字段的值完全相同，一起加密或者都不加密
     * ---------------------------------------------------------------------
     * eg2:
     * @GoodsColumn(value = "subclass_id")
     * subclass = "黄金手镯";
     * subclassId = 3;
     * 加密后
     * subclass = "***镯"
     * subclassId = 3;
     * subclass_id = 3;
     * id类型的字段，只会对字面量做加密，会添加一个sign字段，值和xxxId字段相同
     * @param list 待处理的对象集合
     */
    public static List<JSONObject> encrypt(List<?> list, Class<?> clazz, String columnFieldName) {
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        // 查询基础字段
        List<GoodsColumnEntity> columnEntities = goodsColumnMapper.selectListByQuery(QueryWrapper.create()
                .where(GoodsColumnEntity::getCategory).eq(1)
                .where(GoodsColumnEntity::getCompanyId).eq(SecurityUtils.getCompanyId()));

        // 处理注解和字段
        Map<String, Set<Field>> fieldMap = new HashMap<>();
        Set<String> ignoreFiled = new HashSet<>();
        handleField(clazz, fieldMap, ignoreFiled);
        // 循环处理
        List<JSONObject> result = new ArrayList<>();
        for (Object obj : list) {
            JSONObject item = JSONUtil.parseObj(obj, false);
            result.add(item);
            // 存在goodsColumn注解的基础字段统一处理
            for (GoodsColumnEntity column : columnEntities) {
                Set<Field> fields = fieldMap.get(column.getSign());
                if(CollectionUtil.isEmpty(fields)) {
                    continue;
                }
                for (Field field : fields) {
                    if (field == null) {
                        continue;
                    }
                    boolean addList = field.getAnnotation(GoodsColumn.class).signAddList();
                    String fieldName = field.getName();
                    String sign = column.getSign();
                    Object value = item.get(field.getName());
                    if (addList) {
                        item.set(sign, value);
                    }
                    // id类型字段处理
                    if (JoinColumEnum.isJoinColumn(column.getSign())) {
                        String name = JoinColumEnum.getClearColumnName(column.getSign());
                        try {
                            if (addList) {
                                item.set(sign, item.get(name));
                            }
                        }catch (Exception ignore) {}
                        sign = fieldName;
                    }
                    // 如果不需要加密直接返回
                    if (!needEncrypt()) {
                        continue;
                    }
                    Object encryptValue = getEncryptValue(column.getSecretLevel(), column.getType(), value);
                    if (addList) {
                        item.set(sign, encryptValue);
                    }
                    item.set(fieldName, encryptValue);
                }
            }

            // 处理自定义字段
            if (!StringUtils.isBlank(columnFieldName)) {
                try {
                    encryptCusColumnVo(item, item.getBeanList(columnFieldName, CustomColumnItemDTO.class), true);
                } catch (Exception e) {
                    log.error("自定义字段处理异常", e);
                }
            }
            // 处理完自定义字段之后，删除自定义字段列表
            item.remove(columnFieldName);
            // 处理jsonNull报错问题
            handleJsonNull(item);
            // 处理忽略字段
            handleJsonIgnore(item, ignoreFiled);
        }
        return result;
    }

    public static Object getEncryptValue(Integer secretLevel, Integer type, Object object) {
        if (Objects.isNull(object)) {
            return "****";
        }
        if (secretLevel.equals(1)) {
            return object;
        }
        String value = object.toString();
        try {
            // 机密
            if (secretLevel.equals(3)) {
                return "****";
            }
            // 敏感
            if (StringUtils.isBlank(value)) {
                return "****";
            }
            // 数字类型取个位
            if (type.equals(2)) {
                String plainString = new BigDecimal(value).setScale(0, RoundingMode.DOWN).toPlainString();
                return  "***" + plainString.charAt(plainString.length() - 1);
            } else {
                // 非数字类型取最后一个字符
                return  "***" + value.charAt(value.length() - 1);
            }
        }catch (Exception ignore) {
            return "****";
        }
    }

    /**
     * 递归处理所有json对象中的jsonNull
     * @param object
     */
    private static void handleJsonNull(Object object) {
        if (object instanceof JSONObject item) {
            item.keySet().forEach(key -> {
                if (item.get(key) instanceof JSONNull) {
                    item.set(key, getNullFieldValue(key));
                }
                if (item.get(key) instanceof JSONObject obj) {
                    handleJsonNull(obj);
                }
                if (item.get(key) instanceof JSONArray array) {
                    for (int i = 0; i < array.size(); i++) {
                        if (array.get(i) instanceof JSONNull) {
                            array.set(i, "");
                        }
                        handleJsonNull(array.get(i));
                    }
                }
            });
        }
    }

    private static Object getNullFieldValue(String key) {
        if (key.equals("image") || key.equals("images")) {
            return new ArrayList<>();
        }
        return "";
    }

    /**
     * 根据当前登录用户，对自定义字段vo内的对象进行加密
     * @param list 自定义字段vo
     * @param addId 下拉类型自定义字段是否添加sign + _id字段
     */
    public static void encryptCusColumnVo(JSONObject obj, List<CustomColumnItemDTO> list, boolean addId) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        for (CustomColumnItemDTO dto : list) {
            String sign = dto.getColumnSign();
            // 图片处理
            if (dto.getType().equals(6)) {
                Integer imageId = dto.getImageId();
                if (imageId == null || imageId == 0 || StringUtils.isBlank(dto.getValue().toString())) {
                    obj.set(sign, new ArrayList<>());
                    continue;
                }
                GoodsHasImagesEntity imagesEntity = new GoodsHasImagesEntity()
                        .setImageId(imageId.longValue())
                        .setUrl(dto.getValueStr());
                if (needEncrypt() && dto.getSecretLevel() > 1) {
                    imagesEntity.setUrl("img/secret-image.png");
                }
                obj.set(sign, List.of(imagesEntity));
                continue;
            }
            String value = dto.getValueStr();
            // 下拉类型处理
            if (dto.getType() == 5) {
                try {
                    Map<String, String> lableMap = JSONUtil.toList(dto.getOptions(), Option.class).stream()
                            .collect(Collectors.toMap(item -> item.getValue().toString(), Option::getLabel, (v1, v2) -> v1));
                    value = value.replaceAll("\\[", "").replaceAll("]", "");
                    if (addId) {
                        obj.set(sign + "_id", value);
                    }
                    // 多选、单选处理
                    if (dto.getIsMultiple().equals(1)) {
                        if (StringUtils.isNotBlank(value)) {
                            value = Arrays.stream(value.split(",")).map(lableMap::get).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
                        }
                    }else {
                        value = lableMap.get(value);
                    }
                }catch (Exception ignore){}
            }
            obj.set(sign, value);
            // 设置加密
            if (needEncrypt()) {
                obj.set(sign, getEncryptValue(dto.getSecretLevel(), dto.getType(), value));
            }
        }
    }

    /**
     * 处理字段注解
     * @param clazz
     * @param fieldMap
     * @param fieldSet
     */
    public static void handleField(Class<?> clazz, Map<String, Set<Field>> fieldMap, Set<String> fieldSet) {
        // 如果子类与父类中存在同名字段，则这两个字段同时存在，子类字段在前，父类字段在后。
        Field[] fields = ReflectUtil.getFields(clazz);
        // 使用 Set 去重，避免重复添加父类的同名字段
        Set<String> fieldNameSet = new HashSet<>();
        for (Field field : fields) {
            field.setAccessible(true);
            if (fieldNameSet.contains(field.getName())) continue;
            fieldNameSet.add(field.getName());
            GoodsColumn column = field.getAnnotation(GoodsColumn.class);
            JsonIgnore annotation = field.getAnnotation(JsonIgnore.class);
            if (fieldMap != null && column != null) {
                Set<Field> set = fieldMap.getOrDefault(column.value(), new HashSet<>());
                set.add(field);
                fieldMap.put(column.value(), set);
            }
            if (annotation != null && fieldSet != null) {
                fieldSet.add(field.getName());
            }
        }
    }

    public static void handleJsonIgnore(JSONObject obj, Set<String> ignoreFields) {
        for (String ignoreField : ignoreFields) {
            obj.remove(ignoreField);
        }
    }

    public static Map<String, Field> getCloumnFieldMap(Class<?> clazz) {
        Field[] fields =clazz.getDeclaredFields();
        Map<String, Field> fieldMap = new HashMap<>();
        for (Field field : fields) {
            field.setAccessible(true);
            Column column = field.getAnnotation(Column.class);
            fieldMap.put(column.value(), field);
        }
        return fieldMap;
    }

    /**
     * 判断当前登录用户是否需要处理加密
     * @return
     */
    public static boolean needEncrypt() {
        return !(SecurityUtils.isRoot() || SecurityUtils.isMain() || SecurityUtils.showSecret());
    }

    /**
     * 根据货品字段信息判断是否需要加密
     * @param column
     * @return
     */
    public static boolean needEncrypt(GoodsColumnEntity column) {
        return needEncrypt() && column != null && column.getSecretLevel() > 1;
    }

    /**
     * 根据字段sign判断是否可以编辑
     * @param sign
     * @return
     */
    public static boolean signCanEdit(String sign) {
        GoodsColumnEntity column = CommonUtils.getGoodsColumnsBySign(sign);

        if(column == null){
            return true;
        }

        return !needEncrypt(column);
    }

    /**
     * 加密价格类型字符串
     * @param value
     * @param column
     * @return
     */
    public static String handleEncryptPrice(String value, GoodsColumnEntity column) {
        if (!needEncrypt(column)) {
            return value;
        }
        // 取个位
        try {
            if (column.getSecretLevel().equals(2)) {
                String plainString = new BigDecimal(value).setScale(0, RoundingMode.DOWN).toPlainString();
                plainString = "***" + plainString.charAt(plainString.length() - 1);
                return plainString;
            }else {
                return "****";
            }
        }catch (Exception e) {
            return "****";
        }
    }

    public static void handleJsonImageExport(List<JSONObject> list){
        List<GoodsColumnEntity> goodsColumns = CommonUtils.getGoodsColumns();
        // 图片、自定义图片处理
        list.forEach(item -> {
            // 图片取第一张
            if (item.containsKey("image")) {
                if (item.get("image") instanceof JSONArray array) {
                    if (!array.isEmpty()) {
                        item.set("image", array.get(0, GoodsHasImagesEntity.class).getUrl());
                    }else {
                        item.set("image", "");
                    }
                }
            }
            // 自定义字段图片处理
            for (GoodsColumnEntity goodsColumn : goodsColumns) {
                if (goodsColumn.getType().equals(6) && goodsColumn.getCategory().equals(2)) {
                    List<GoodsHasImagesEntity> images = item.getBeanList(goodsColumn.getSign(), GoodsHasImagesEntity.class);
                    if (CollectionUtil.isNotEmpty(images)) {
                        item.set(goodsColumn.getSign(), images.getFirst().getUrl());
                    }else {
                        item.set(goodsColumn.getSign(), "");
                    }
                }
            }
        });
    }

    /**
     * 根据字段sign和原始值，返回加密后的值
     * @param sign 字段标识
     * @param value 原始值
     * @return 加密后的值
     */
    public static String encryptFieldValue(String sign, Object value) {
        if (!needEncrypt()) {
            return value == null ? "" : value.toString();
        }
        GoodsColumnEntity column = null;
        try {
            column = CommonUtils.getGoodsColumnsBySign(sign);
        } catch (Exception e) {
            // 查询异常，降级为不加密
            return value == null ? "" : value.toString();
        }
        if (column == null || column.getSecretLevel() == null || column.getSecretLevel() <= 1) {
            return value == null ? "" : value.toString();
        }
        // 敏感
        if (column.getSecretLevel().equals(GoodsColumnSecretLevelEnum.SENSITIVE.getValue())) {
            // 图片
            if(column.getType().equals(GoodsColumnTypeEnum.IMAGE.getValue())){
                return "img/secret-image.png";
            }

            // 没有值 或 下拉
            if (value == null || column.getType().equals(GoodsColumnTypeEnum.SELECT.getValue())) {
                return "****";
            }

            if (column.getType() != null && column.getType().equals(GoodsColumnTypeEnum.NUMBER.getValue())) {
                try {
                    String plainString = new java.math.BigDecimal(value.toString()).setScale(0, java.math.RoundingMode.DOWN).toPlainString();
                    return "***" + plainString.charAt(plainString.length() - 1);
                } catch (Exception ignore) {
                    return "****";
                }
            }

            String string = value.toString();
            if (string.isEmpty()) return "****";
            return "***" + string.charAt(string.length() - 1);
        }
        // 机密
        if (column.getSecretLevel().equals(GoodsColumnSecretLevelEnum.SECRET.getValue())) {
            // 图片
            if(column.getType().equals(GoodsColumnTypeEnum.IMAGE.getValue())){
                return "img/secret-image.png";
            }
            return "****";
        }
        return value == null ? "" : value.toString();
    }

    /**
     * 智能加密处理器入口.
     * 自动处理 Page、List 或单个对象,并根据注解递归加密.
     *
     * @param data 待处理的数据 (Page, List, 或单个VO对象)
     * @return 处理后的数据 (Page<JSONObject>, List<JSONObject>, 或 JSONObject)
     */
    public static Object process(Object data) {
        if (data == null) {
            return null;
        }
        if (data instanceof Page) {
            return processPage((Page<?>) data);
        }
        if (data instanceof List) {
            return processList((List<?>) data);
        }
        // 假定是单个对象
        return processObject(data);
    }

    private static Page<JSONObject> processPage(Page<?> page) {
        Page<JSONObject> jsonPage = new Page<>();
        BeanUtil.copyProperties(page, jsonPage, "records");
        jsonPage.setRecords(processList(page.getRecords()));
        return jsonPage;
    }

    private static List<JSONObject> processList(List<?> list) {
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream()
            .map(ColumnEncryptUtil::processObject)
            .collect(Collectors.toList());
    }

    private static JSONObject processObject(Object obj) {
        if (obj == null) {
            return new JSONObject();
        }
        JSONObject item = new JSONObject();
        List<Field> fields = getAllFields(obj.getClass());

        // 1. 处理所有字段, 分离出 @Flatten 列表
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                // 优先处理 @Flatten 注解
                if (field.isAnnotationPresent(Flatten.class)) {
                    Object value = field.get(obj);
                    if (value instanceof List) {
                        flattenAndProcessCustomColumns(item, (List<?>) value);
                    }
                    continue; // 跳过, 不将原始列表添加到结果中
                }

                Object value = field.get(obj);
                GoodsColumn goodsColumn = field.getAnnotation(GoodsColumn.class);

                if (goodsColumn != null) {
                    // 处理带 @GoodsColumn 注解的字段
                    String sign = goodsColumn.value();
                    GoodsColumnEntity columnEntity = CommonUtils.getGoodsColumnsBySign(sign);
                    
                    // 图片字段 或者 没有货品字段信息 或者 常规字段 -> 返回原始值
                    if(sign.equals("image") || sign.equals("images") || columnEntity == null || columnEntity.getSecretLevel() == 1) {
                        item.set(field.getName(), value);
                        continue;
                    }

                    Object processedValue = encryptFieldValue(sign, value);
                    item.set(field.getName(), processedValue);

                    // sign 键值对
                    // item.set(sign, processedValue);
                } else {
                    // 其次处理命名约定的自定义字段列表
                    if (field.getName().toLowerCase().contains("column") && value instanceof List) {
                        item.set(field.getName(), processCustomColumnList((List<?>) value));
                    } else if (value instanceof List) { // 处理其他普通列表
                        item.set(field.getName(), processList((List<?>) value));
                    } else if (isCustomObject(value)) { // 处理内嵌对象
                        item.set(field.getName(), processObject(value));
                    } else { // 处理基本类型
                        item.set(field.getName(), value);
                    }
                }
            } catch (IllegalAccessException e) {
                log.error("处理字段加密时出错: {}", field.getName(), e);
            }
        }

        // 2. 处理 null 值以避免序列化问题
        handleJsonNull(item);
        return item;
    }

    /**
     * 平铺并处理自定义字段列表
     */
    private static void flattenAndProcessCustomColumns(JSONObject item, List<?> customColumns) {
        if (CollectionUtil.isEmpty(customColumns)) {
            return;
        }

        for (Object col : customColumns) {
            String sign = "";
            Object value = null;
            Integer imageId = 0;

            if (col instanceof CustomColumnItemDTO dto) {
                sign = dto.getColumnSign();
                value = dto.getValue();
                imageId = dto.getImageId();
            } else if (col instanceof GoodsHasColumnsEntity entity) {
                sign = entity.getColumnSign();
                value = entity.getValue();
                imageId = entity.getImageId();
            } else if (col instanceof GoodsIncomeHasColumnsEntity entity) {
                sign = entity.getColumnSign();
                value = entity.getValue();
                imageId = entity.getImageId();
            } else if (col instanceof GoodsIncomeTemplateDetailEntity entity) {
                sign = entity.getSign();
                value = entity.getDefaultValue();
                imageId = entity.getImageId().intValue();
            } else if (col instanceof Map) {
                Map<?, ?> map = (Map<?, ?>) col;
                sign = map.containsKey("columnSign") ? String.valueOf(map.get("columnSign"))
                              : String.valueOf(map.get("sign"));
                
                if(StringUtils.isBlank(sign)) continue;
                
                value = map.get("value");
                imageId = map.containsKey("imageId") ? Integer.parseInt(String.valueOf(map.get("imageId"))) : 0;
            }

            if(StringUtils.isNotBlank(sign)){
                GoodsColumnEntity columnEntity = CommonUtils.getGoodsColumnsBySign(sign);
                // 存在货品字段信息
                if(columnEntity != null){
                    // 不需要加密
                    if(!needEncrypt(columnEntity)){
                        // 图片
                        if(columnEntity.getType().equals(GoodsColumnTypeEnum.IMAGE.getValue())){
                            if(imageId == 0){
                                item.set(sign, new ArrayList<>());
                                continue;
                            }

                            Map<String, Object> image = new HashMap<>();
                            image.put("id", imageId);
                            image.put("imageId", imageId);
                            image.put("url", value);

                            item.set(sign, List.of(image));
                            continue;
                        }

                        // 多选
                        if(columnEntity.getType().equals(GoodsColumnTypeEnum.SELECT.getValue()) && columnEntity.getIsMultiple() == 1){
                            item.set(sign, "");
                            item.set(sign + "_id", new ArrayList<>());
                            if(value == null || StrUtil.isBlank(value.toString())){
                                continue;
                            }

                            List<Object> list = JSONUtil.parseArray(value);
                            if(list.isEmpty()){
                                continue;
                            }

                            Map<String, Object> optionMap = new HashMap<>();
                            JSONArray jsonArray = JSONUtil.parseArray(columnEntity.getOptions());
                            jsonArray.forEach(item2 -> {
                                // 处理不同的数据格式
                                if (item2 instanceof JSONObject) {
                                    JSONObject jsonObject = (JSONObject) item2;
                                    optionMap.put(jsonObject.get("value").toString(), jsonObject.get("label"));
                                } else if (item2 instanceof String) {
                                    // 如果是字符串，可能需要解析或直接使用
                                    optionMap.put(item2.toString(), item2.toString());
                                }
                            });

                            List<Object> valueList = list.stream().map(item2 -> {
                                // 处理不同的数据格式
                                if (item2 instanceof JSONObject) {
                                    JSONObject jsonObject = (JSONObject) item2;
                                    return optionMap.get(jsonObject.get("value").toString());
                                } else if (item2 instanceof String) {
                                    // 如果是字符串，直接使用该字符串作为value查找
                                    return optionMap.get(item2.toString());
                                } else {
                                    // 其他类型，转换为字符串后查找
                                    return optionMap.get(String.valueOf(item2));
                                }
                            }).filter(Objects::nonNull).collect(Collectors.toList());

                            item.set(sign, StrUtil.join(",", valueList));
                            item.set(sign + "_id", list);
                            continue;
                        }

                        // 单选
                        if(columnEntity.getType().equals(GoodsColumnTypeEnum.SELECT.getValue())){
                            item.set(sign, "");
                            item.set(sign + "_id", "");
                            if(value == null || StrUtil.isBlank(value.toString())){
                                continue;
                            }

                            Map<String, Object> optionMap = new HashMap<>();
                            JSONArray jsonArray = JSONUtil.parseArray(columnEntity.getOptions());
                            jsonArray.forEach(item2 -> {
                                // 处理不同的数据格式
                                if (item2 instanceof JSONObject) {
                                    JSONObject jsonObject = (JSONObject) item2;
                                    optionMap.put(jsonObject.get("value").toString(), jsonObject.get("label"));
                                } else if (item2 instanceof String) {
                                    // 如果是字符串，可能需要解析或直接使用
                                    optionMap.put(item2.toString(), item2.toString());
                                }
                            });

                            item.set(sign, optionMap.get(value.toString()));
                            item.set(sign + "_id", value.toString());
                            continue;
                        }

                        item.set(sign, encryptFieldValue(sign, value));
                        continue;
                    }
                }
            }

            item.set(sign, encryptFieldValue(sign, value));
        }
    }

    /**
     * 处理自定义字段列表（不平铺），仅加密其中的值
     */
    private static List<Object> processCustomColumnList(List<?> customColumns) {
        if (CollectionUtil.isEmpty(customColumns)) {
            return new ArrayList<>();
        }

        List<Object> resultList = new ArrayList<>();
        for (Object col : customColumns) {
            JSONObject processedItem = new JSONObject();
            if (col instanceof Map) {
                Map<?, ?> originalMap = (Map<?, ?>) col;
                Map<String, Object> newMap = new HashMap<>();
                for(Map.Entry<?, ?> entry : originalMap.entrySet()) {
                    newMap.put(String.valueOf(entry.getKey()), entry.getValue());
                }
                processedItem.putAll(newMap); // 复制所有原始字段

                String sign = newMap.containsKey("columnSign") ? String.valueOf(newMap.get("columnSign"))
                              : String.valueOf(newMap.get("sign"));
                
                if(StringUtils.isNotBlank(sign)) {
                    Object value = newMap.get("value");
                    Object processedValue = encryptFieldValue(sign, value);
                    processedItem.set("value", processedValue); // 只更新value字段
                }
            } else if (isCustomObject(col)) {
                // 反射处理DTO
                try {
                    Class<?> clazz = col.getClass();
                    Field signField = null;
                    Field valueField = null;
                    // 优先找 columnSign
                    try { signField = clazz.getDeclaredField("columnSign"); } catch (NoSuchFieldException ignore) {}
                    if (signField == null) {
                        try { signField = clazz.getDeclaredField("sign"); } catch (NoSuchFieldException ignore) {}
                    }
                    try { valueField = clazz.getDeclaredField("value"); } catch (NoSuchFieldException ignore) {}
                    if (signField != null && valueField != null) {
                        signField.setAccessible(true);
                        valueField.setAccessible(true);
                        String sign = String.valueOf(signField.get(col));
                        Object value = valueField.get(col);
                        Object processedValue = encryptFieldValue(sign, value);
                        valueField.set(col, processedValue);
                    }
                    processedItem = JSONUtil.parseObj(col, false);
                } catch (Exception e) {
                    processedItem.set("item", col); // 反射失败，原样放入
                }
            } else {
                processedItem.set("item", col); // 无法识别的类型，直接放入
            }
            resultList.add(processedItem);
        }
        return resultList;
    }

    /**
     * 递归获取一个类及其所有父类的字段
     */
    private static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null && !clazz.equals(Object.class)) {
            fieldList.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        return fieldList;
    }
    
    /**
     * 判断一个对象是否是需要递归处理的自定义业务对象.
     * 避免递归进入JDK或第三方库的类.
     */
    private static boolean isCustomObject(Object obj) {
        if (obj == null) {
            return false;
        }
        String packageName = obj.getClass().getPackageName();
        return packageName.startsWith("com.xc.boot");
    }
}
