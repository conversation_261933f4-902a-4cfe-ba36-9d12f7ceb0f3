package com.xc.boot.common.util;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import com.xc.boot.common.enums.EnvEnum;

/**
 * <AUTHOR>
 * @ClassName ContextUtils
 * @Date: 2025/6/2 10:20
 * @Description: 提供静态方法获取全局变量
 */
@Component
public class ContextUtils implements ApplicationContextAware {
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        ContextUtils.applicationContext = applicationContext;
    }
    /**
     * 获取当前环境
     * @return
     */
    public static String getActiveProfile() {
        final String[] activeProfiles = applicationContext.getEnvironment().getActiveProfiles();
        return activeProfiles.length > 0 ? activeProfiles[0] : null;
    }

    /**
     * 获取bean
     */
    public static <T> T getBean(Class<T> clazz) {
        return applicationContext.getBean(clazz);
    }

    /**
     * 是否生产环境
     * @return
     */
    public static boolean isProd() {
        String activeProfile = getActiveProfile();
        return activeProfile.equals(EnvEnum.PROD.getValue());
    }

    /**
     * 获取当前环境标识
     * @return
     */
    public static String getEnv() {
        return getActiveProfile();
    }
}
