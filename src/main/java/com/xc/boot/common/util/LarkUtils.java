package com.xc.boot.common.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiFunction;

import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.annotation.Scheduled;

import com.lark.oapi.Client;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.event.EventDispatcher;
import com.lark.oapi.service.im.ImService;
import com.lark.oapi.service.im.v1.model.P2MessageReceiveV1;
import com.lark.oapi.service.im.v1.model.ReplyMessageReq;
import com.lark.oapi.service.im.v1.model.ReplyMessageReqBody;
import com.lark.oapi.service.im.v1.model.ReplyMessageResp;
import com.xc.boot.common.enums.EnvEnum;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 飞书工具类
 */
@Slf4j
public class LarkUtils {
    public static String APP_ID = "cli_a8cbda95523e500d";
    public static String APP_SECRET = "bHVVmnTycE7sLjSPyePMmhr4Mh8hEzN6";

    /**
     * 命令列表
     */
    public static List<Command> COMMANDS = new ArrayList<>();

    /**
     * 命令对象
     */
    @Data
    @Builder
    public static class Command {
        private String name;
        private String[] commands;
        private String description;
        private String example;
        /**
         * 命令处理函数
         * 
         * @param event 事件
         * @param args  命令参数
         * @return 是否处理成功
         */
        private BiFunction<P2MessageReceiveV1, String[], Boolean> handler;
    }

    /**
     * 事件处理器
     */
    private static final EventDispatcher EVENT_HANDLER = EventDispatcher.newBuilder("", "")
            .onP2MessageReceiveV1(new ImService.P2MessageReceiveV1Handler() {
                @Override
                public void handle(P2MessageReceiveV1 event) throws Exception {
                    log.info("[ onP2MessageReceiveV1 access ], data: {}", Jsons.DEFAULT.toJson(event.getEvent()));

                    LarkUtils.onMessageReceive(event);
                }
            })
            .build();

    /**
     * 消息ID缓存，key为消息ID，value为时间戳
     */
    private static final ConcurrentHashMap<String, Long> MESSAGE_CACHE = new ConcurrentHashMap<>();

    /**
     * 消息缓存过期时间（毫秒）
     */
    private static final long MESSAGE_CACHE_EXPIRE_TIME = 3 * 60 * 1000;

    /**
     * 检查消息是否重复
     * 
     * @param messageId 消息ID
     * @return 是否重复
     */
    private static boolean isMessageDuplicate(String messageId) {
        Long timestamp = MESSAGE_CACHE.get(messageId);
        if (timestamp == null) {
            return false;
        }

        // 检查是否过期
        if (System.currentTimeMillis() - timestamp > MESSAGE_CACHE_EXPIRE_TIME) {
            MESSAGE_CACHE.remove(messageId);
            return false;
        }

        return true;
    }

    /**
     * 添加消息到缓存
     * 
     * @param messageId 消息ID
     */
    private static void addMessageToCache(String messageId) {
        MESSAGE_CACHE.put(messageId, System.currentTimeMillis());
    }

    /**
     * 清理过期的消息缓存
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public static void cleanExpiredMessages() {
        long now = System.currentTimeMillis();
        MESSAGE_CACHE.entrySet().removeIf(entry -> now - entry.getValue() > MESSAGE_CACHE_EXPIRE_TIME);
    }

    /**
     * 注册命令
     */
    public static void registerCommands(String env) {
        // * 帮助
        COMMANDS.add(Command.builder()
                .name("帮助")
                .commands(new String[] { "help", "h", "?", "帮助" })
                .description("查看命令列表")
                .example("@机器人 help")
                .handler((event, args) -> {
                    StringBuilder message = new StringBuilder();
                    message.append("🤖 <b>命令列表</b>\n");
                    message.append("━━━━━━━━━━━━━━━━━━━━\n\n");

                    for (Command command : COMMANDS) {
                        // 命令名称和描述
                        message.append("📌 <b>").append(command.getName()).append("</b>\n");
                        message.append("   ").append(command.getDescription()).append("\n");

                        // 命令别名
                        message.append("   > 别名：");
                        for (int i = 0; i < command.getCommands().length; i++) {
                            if (i > 0)
                                message.append("、");
                            message.append(command.getCommands()[i]);
                        }
                        message.append("\n");

                        // 使用示例
                        message.append("   > 示例：").append(command.getExample()).append("\n\n");
                    }

                    message.append("━━━━━━━━━━━━━━━━━━━━\n");
                    message.append("💡 提示：直接输入命令即可使用，无需添加任何前缀");

                    LarkUtils.replyTextMessageWithEvent(event, message.toString());
                    return true;
                })
                .build());

        // * 更新API文档
        if (!env.equals(EnvEnum.PROD.getValue())) {
            // 非生产环境才添加更新API文档命令
            COMMANDS.add(Command.builder()
                    .name("更新API文档")
                    .commands(new String[] { "doc", "更新文档" })
                    .description("更新API文档到Apifox")
                    .example("@机器人 doc")
                    .handler((event, args) -> {
                        LarkUtils.replyTextMessageWithEvent(event, "开始更新API文档...");
                        String result = CommonUtils.updateApiDoc();
                        LarkUtils.replyTextMessageWithEvent(event, result);
                        return true;
                    })
                    .build());
        }
    }

    /**
     * 消息接收事件
     * 
     * @param event 事件
     * @return 是否处理
     */
    public static boolean onMessageReceive(P2MessageReceiveV1 event) {
        // 获取消息ID
        String messageId = event.getEvent().getMessage().getMessageId();
        
        // 检查消息是否重复
        if (isMessageDuplicate(messageId)) {
            log.info("[ onMessageReceive ] Message {} is duplicate, skipping", messageId);
            return false;
        }

        // 记录消息ID到缓存
        addMessageToCache(messageId);

        // 获取消息内容
        String content = event.getEvent().getMessage().getContent();
        if (content == null || content.isEmpty()) {
            replyTextMessageWithEvent(event, "消息为空");
            return false;
        }

        // 解析消息内容
        Map<String, Object> contentMap = JSONUtil.toBean(content, Map.class);
        String text = (String) contentMap.get("text");
        if (text == null || text.isEmpty()) {
            replyTextMessageWithEvent(event, "消息解析失败");
            return false;
        }

        // 按空格拆分命令和参数
        String[] parts = text.trim().split("\\s+");
        if (parts.length == 0) {
            replyTextMessageWithEvent(event, "命令解析失败");
            return false;
        }

        // 如果第一块以@开头，则移除第一块
        int startIndex = 0;
        if (parts[0].startsWith("@")) {
            startIndex = 1;
            if (parts.length <= 1) {
                replyTextMessageWithEvent(event, "命令解析失败");
                return false;
            }
        }

        String command = parts[startIndex];
        String[] args = new String[parts.length - startIndex - 1];
        System.arraycopy(parts, startIndex + 1, args, 0, args.length);

        // 遍历命令列表进行匹配
        for (Command cmd : COMMANDS) {
            for (String cmdName : cmd.getCommands()) {
                if (command.equals(cmdName)) {
                    // 执行命令处理函数
                    try {
                        return cmd.getHandler().apply(event, args);
                    } catch (Exception e) {
                        log.error("[ onMessageReceive error ], command: {}, error: {}", cmdName, e.getMessage(), e);
                        replyTextMessageWithEvent(event, "命令执行失败：" + e.getMessage());
                        return true;
                    }
                }
            }
        }

        replyTextMessageWithEvent(event, "<b>未定义的命令:</b> [ " + command + " ] \n\n" + "你可以使用 <b>help</b> 查看命令列表");
        return false;
    }

    /**
     * 回复文字消息
     * 
     * @param event 事件
     * @param text  文本
     * @return 是否处理
     */
    public static boolean replyTextMessageWithEvent(P2MessageReceiveV1 event, String text) {
        Client client = getClient();

        // 创建请求对象
        ReplyMessageReq req = ReplyMessageReq.newBuilder()
                .messageId(event.getEvent().getMessage().getMessageId())
                .replyMessageReqBody(ReplyMessageReqBody.newBuilder()
                        .content(JSONUtil.toJsonStr(Map.of("text", text)))
                        .msgType("text")
                        .build())
                .build();

        // 发起请求
        try {
            ReplyMessageResp resp = client.im().v1().message().reply(req);

            if (!resp.success()) {
                throw new RuntimeException(resp.getMsg());
            }
        } catch (Exception e) {
            log.error("[ replyTextMessageWithEvent error ], data: {}", Jsons.DEFAULT.toJson(req), e);
        }

        return false;
    }

    public static Client getClient() {
        return Client.newBuilder(APP_ID, APP_SECRET).build();
    }

    /**
     * 启动事件处理器
     * 
     * @param env 环境
     */
    public static void startEventHandler(String env, AsyncTaskExecutor asyncTaskExecutor) {
        // 本地环境不启动事件处理器
        if (env.equals(EnvEnum.LOCAL.getValue())) {
            return;
        }

        // 异步启动事件处理器
        asyncTaskExecutor.execute(() -> {
            // dev 机器人配置
            APP_ID = "cli_a8cbda95523e500d";
            APP_SECRET = "bHVVmnTycE7sLjSPyePMmhr4Mh8hEzN6";

            if (env.equals(EnvEnum.PROD.getValue())) {
                // 替换为 prod 机器人配置
                // todo
                APP_ID = "app_id";
                APP_SECRET = "app_secret";
            }

            // 注册命令
            registerCommands(env);

            // 启动事件处理器
            com.lark.oapi.ws.Client client = new com.lark.oapi.ws.Client.Builder(APP_ID, APP_SECRET)
                    .eventHandler(EVENT_HANDLER)
                    .build();

            client.start();
        });
    }

    /**
     * 应用启动提醒
     */
    public static void appSetupRemind(String env, AsyncTaskExecutor asyncTaskExecutor) {
        if (env.equals(EnvEnum.LOCAL.getValue())) {
            return;
        }

        asyncTaskExecutor.execute(() -> {
            String url = "https://open.feishu.cn/open-apis/bot/v2/hook/20adb3a2-2fc0-4314-b26a-4f4370fc986b";
            String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String content = "✅ " + now + " \\n\\n" + env + " 已启动🎉";
            String body = "{\"msg_type\": \"text\", \"content\": {\"text\": \"" + content + "\"}}";
            HttpUtil.post(url, body);
        });
    }
}
