package com.xc.boot.common.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.mapper.GoodsPriceLogsMapper;
import com.xc.boot.modules.goods.model.bo.GoodsPriceChangeBO;
import com.xc.boot.modules.goods.model.entity.GoodsPriceLogsEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @ClassName PriceUtil
 * @Date: 2025/6/10 10:01
 * @Description: 价格处理工具类
 */

@Component
public class PriceUtil implements ApplicationContextAware {
    private static GoodsPriceLogsMapper goodsPriceLogsMapper;
    @SuppressWarnings("unused")
    private static GoodsMapper goodsMapper;
    @SuppressWarnings("unused")
    private static ApplicationContext applicationContext;
    @SuppressWarnings("unused")
    private static TransactionTemplate transactionTemplate;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        PriceUtil.applicationContext = applicationContext;
        goodsPriceLogsMapper = applicationContext.getBean(GoodsPriceLogsMapper.class);
        transactionTemplate = applicationContext.getBean(TransactionTemplate.class);
    }

    /**
     * 批量更新价格
     * @param changes
     * @return
     */
    public static Integer logPriceChange(List<GoodsPriceChangeBO> changes) {
        if (CollectionUtil.isEmpty(changes)) {
            return 0;
        }
        Long companyId = SecurityUtils.getCompanyId();
        List<GoodsPriceLogsEntity> list = BeanUtil.copyToList(changes, GoodsPriceLogsEntity.class);
        list.forEach(item -> {
            if (Objects.isNull(item.getGoodsId())) {
                throw new BusinessException("货品ID不能为空");
            }
            item.setCompanyId(companyId);
        });
        return goodsPriceLogsMapper.insertBatch(list);
    }

    /**
     * 价格转换处理
     */
    public static BigDecimal fen2yuan(BigDecimal fen) {
        if (Objects.isNull(fen)) {
            return BigDecimal.ZERO;
        }
        return fen.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
    }

    public static BigDecimal fen2yuan(Integer fen) {
        return fen2yuan(new BigDecimal(fen == null ? 0 : fen));
    }

    public static BigDecimal fen2yuan(String fen) {
        return fen2yuan(new BigDecimal(StringUtils.isBlank(fen) ? "0" : fen));
    }

    public static BigDecimal fen2yuan(Long fen) {
        return fen2yuan(new BigDecimal(fen == null ? 0L : fen));
    }

    public static BigDecimal yuan2fen(BigDecimal yuan) {
        if (Objects.isNull(yuan)) {
            return BigDecimal.ZERO;
        }
        return yuan.multiply(new BigDecimal("100")).setScale(0, RoundingMode.HALF_UP);
    }

    public static BigDecimal yuan2fen(String yuan) {
        if (StringUtils.isBlank(yuan)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(yuan).multiply(new BigDecimal("100")).setScale(0, RoundingMode.HALF_UP);
    }

    public static Long yuan2fenLong(BigDecimal yuan) {
        return yuan2fen(yuan).longValue();
    }

    public static Long yuan2fenLong(BigDecimal yuan, boolean ignoreNull) {
        if (!ignoreNull && Objects.isNull(yuan)) {
            return null;
        }
        return yuan2fen(yuan).longValue();
    }

    public static String fen2yuanString(BigDecimal fen) {
        if (Objects.isNull(fen)) {
            return "0.00";
        }
        return fen2yuan(fen).toPlainString();
    }

    /**
     * 去除多余的0
     * @param yuan 金额
     * @return 去除多余的0后的金额
     */
    public static String yuanClear(String yuan) {
        if (Objects.isNull(yuan)) {
            return "0";
        }
        return new BigDecimal(yuan).stripTrailingZeros().toPlainString();
    }

    public static String yuanPreUnit(String yuan) {
        if (Objects.isNull(yuan)) {
            return "¥0.00";
        }
        return "¥" + new BigDecimal(yuan).toPlainString();
    }

    public static String yuanPreUnitClear(String yuan) {
        if (Objects.isNull(yuan)) {
            return "¥0";
        }
        return "¥" + new BigDecimal(yuan).stripTrailingZeros().toPlainString();
    }

    public static String yuanLastUnit(String yuan) {
        if (Objects.isNull(yuan)) {
            return "0.00元";
        }
        return new BigDecimal(yuan).toPlainString() + "元";
    }

    public static String yuanLastUnitClear(String yuan) {
        if (Objects.isNull(yuan)) {
            return "0元";
        }
        return new BigDecimal(yuan).stripTrailingZeros().toPlainString() + "元";
    }

    public static String fen2yuanString(String fen) {
        if (Objects.isNull(fen)) {
            return "0.00";
        }
        return fen2yuan(new BigDecimal(fen)).toPlainString();
    }

    public static String fen2yuanString(Integer fen) {
        return fen2yuan(fen).toPlainString();
    }

    public static BigDecimal formatTwoDecimal(BigDecimal dec) {
        return formatDecimal(dec, 2);
    }

    public static BigDecimal formatTwoDecimal(String dec) {
        return formatDecimal(new BigDecimal(dec), 2);
    }

    public static BigDecimal formatThreeDecimal(BigDecimal dec) {
        return formatDecimal(dec, 3);
    }

    public static BigDecimal formatDecimal(BigDecimal dec, int n) {
        if (Objects.isNull(dec)) {
            dec = BigDecimal.ZERO;
        }
        return dec.setScale(n, RoundingMode.HALF_UP);
    }
}
