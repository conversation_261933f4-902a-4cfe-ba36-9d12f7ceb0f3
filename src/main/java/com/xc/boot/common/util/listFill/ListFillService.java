package com.xc.boot.common.util.listFill;

import com.mybatisflex.core.logicdelete.LogicDeleteManager;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.annotation.RequestCache;
import com.xc.boot.common.base.CustomColumnItemDTO;
import com.xc.boot.common.enums.CategoryEnum;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.gift.mapper.GiftHasImagesMapper;
import com.xc.boot.modules.gift.model.entity.GiftHasImagesEntity;
import com.xc.boot.modules.goods.mapper.GoodsHasColumnsMapper;
import com.xc.boot.modules.goods.mapper.GoodsHasImagesMapper;
import com.xc.boot.modules.goods.mapper.GoodsTakeCategoryMapper;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import com.xc.boot.modules.income.mapper.GoodsIncomeHasColumnsMapper;
import com.xc.boot.modules.income.mapper.GoodsIncomeHasImagesMapper;
import com.xc.boot.modules.income.model.entity.GoodsIncomeHasImagesEntity;
import com.xc.boot.modules.goods.model.vo.CategoryNodeVo;
import com.xc.boot.modules.goods.model.vo.TakeCounterVo;
import com.xc.boot.modules.goods.model.vo.TakeStyleVo;
import com.xc.boot.modules.merchant.mapper.*;
import com.xc.boot.modules.merchant.model.entity.*;
import com.xc.boot.modules.merchant.model.vo.GoodsColumnVO;
import com.xc.boot.modules.pda.mapper.GoodsHasRfidMapper;
import com.xc.boot.modules.pda.model.entity.GoodsHasRfidEntity;
import com.xc.boot.system.mapper.MerchantMapper;
import com.xc.boot.system.mapper.PrintTagMapper;
import com.xc.boot.system.mapper.UserMapper;
import com.xc.boot.system.mapper.UserMerchantMapper;
import com.xc.boot.system.mapper.UserRoleMapper;
import com.xc.boot.system.model.entity.MerchantEntity;
import com.xc.boot.system.model.entity.SysUserEntity;
import com.xc.boot.system.model.vo.UserMerchantVo;
import com.xc.boot.system.model.vo.UserRoleVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.xc.boot.modules.goods.model.entity.table.GoodsHasColumnsTableDef.GOODS_HAS_COLUMNS;
import static com.xc.boot.modules.goods.model.entity.table.GoodsHasImagesTableDef.GOODS_HAS_IMAGES;
import static com.xc.boot.modules.goods.model.entity.table.GoodsTakeCategoryTableDef.GOODS_TAKE_CATEGORY;
import static com.xc.boot.modules.goods.model.entity.table.GoodsTakeCounterTableDef.GOODS_TAKE_COUNTER;
import static com.xc.boot.modules.goods.model.entity.table.GoodsTakeStyleTableDef.GOODS_TAKE_STYLE;
import static com.xc.boot.modules.income.model.entity.table.GoodsIncomeHasColumnsTableDef.GOODS_INCOME_HAS_COLUMNS;
import static com.xc.boot.modules.income.model.entity.table.GoodsIncomeHasImagesTableDef.GOODS_INCOME_HAS_IMAGES;
import static com.xc.boot.modules.merchant.model.entity.table.BrandTableDef.BRAND;
import static com.xc.boot.modules.merchant.model.entity.table.CounterTableDef.COUNTER;
import static com.xc.boot.modules.merchant.model.entity.table.GoodsColumnTableDef.GOODS_COLUMN;
import static com.xc.boot.modules.merchant.model.entity.table.GoodsIncomeTemplateTableDef.GOODS_INCOME_TEMPLATE;
import static com.xc.boot.modules.merchant.model.entity.table.JewelryTableDef.JEWELRY;
import static com.xc.boot.modules.merchant.model.entity.table.QualityTableDef.QUALITY;
import static com.xc.boot.modules.merchant.model.entity.table.StyleTableDef.STYLE;
import static com.xc.boot.modules.merchant.model.entity.table.SubclassTableDef.SUBCLASS;
import static com.xc.boot.modules.merchant.model.entity.table.SupplierTableDef.SUPPLIER;
import static com.xc.boot.modules.merchant.model.entity.table.TechnologyTableDef.TECHNOLOGY;
import static com.xc.boot.modules.pda.model.entity.table.GoodsHasRfidTableDef.GOODS_HAS_RFID;
import static com.xc.boot.system.model.entity.table.MerchantTableDef.MERCHANT;
import static com.xc.boot.system.model.entity.table.PrintTagTableDef.PRINT_TAG;
import static com.xc.boot.system.model.entity.table.SysRoleTableDef.SYS_ROLE;
import static com.xc.boot.system.model.entity.table.SysUserMerchantTableDef.SYS_USER_MERCHANT;
import static com.xc.boot.system.model.entity.table.SysUserRoleTableDef.SYS_USER_ROLE;
import static com.xc.boot.system.model.entity.table.SysUserTableDef.SYS_USER;
import static com.xc.boot.modules.gift.model.entity.table.GiftHasImagesTableDef.GIFT_HAS_IMAGES;


/**
 * <AUTHOR>
 * @ClassName ListFillService
 * @Date: 2025/6/3 17:31
 * @Description: list填充方法类
 */
@Service
@RequiredArgsConstructor
public class ListFillService {
    private final UserRoleMapper userRoleMapper;
    private final UserMerchantMapper userMerchantMapper;
    private final UserMapper userMapper;
    private final QualityMapper qualityMapper;
    private final GoodsColumnMapper goodsColumnMapper;
    private final MerchantMapper merchantMapper;
    private final CounterMapper counterMapper;
    private final SupplierMapper supplierMapper;
    private final SubclassMapper subclassMapper;
    private final BrandMapper brandMapper;
    private final StyleMapper styleMapper;
    private final TechnologyMapper technologyMapper;
    private final JewelryMapper jewelryMapper;
    private final GoodsHasColumnsMapper goodsHasColumnsMapper;
    private final GoodsHasImagesMapper goodsHasImagesMapper;
    private final GiftHasImagesMapper giftHasImagesMapper;
    private final GoodsIncomeTemplateMapper goodsIncomeTemplateMapper;
    private final PrintTagMapper printTagMapper;
    private final GoodsTakeCategoryMapper takeCategoryMapper;
    private final GoodsIncomeHasColumnsMapper goodsIncomeHasColumnsMapper;
    private final GoodsIncomeHasImagesMapper goodsIncomeHasImagesMapper;
    private final GoodsHasRfidMapper goodsHasRfidMapper;

    /**
     * 获取用户角色列表map
     * List<UserRoleVo>
     */
    @RequestCache(prefix = "list_fill:user_role")
    public Map<String, List<UserRoleVo>> getUserRoleVosByUserId(Set<?> keys) {
        return userRoleMapper.selectListByQueryAs(QueryWrapper.create()
                        .leftJoin(SYS_ROLE).on(SYS_USER_ROLE.ROLE_ID.eq(SYS_ROLE.ID))
                        .where(SYS_USER_ROLE.USER_ID.in(keys))
                        .select(SYS_ROLE.NAME,
                                SYS_USER_ROLE.USER_ID,
                                SYS_ROLE.ID), UserRoleVo.class)
                .stream().collect(Collectors.groupingBy(e -> e.getUserId().toString()));
    }

    /**
     * 获取用户商店列表map
     * List<UserMerchantVo>
     */
    @RequestCache(prefix = "list_fill:user_merchant")
    public Map<String, List<UserMerchantVo>> getUserMerchantVosByUserId(Set<?> keys) {
        return userMerchantMapper.selectListByQueryAs(QueryWrapper.create()
                        .leftJoin(MERCHANT).on(SYS_USER_MERCHANT.MERCHANT_ID.eq(MERCHANT.ID))
                        .where(SYS_USER_MERCHANT.USER_ID.in(keys))
                        .select(MERCHANT.NAME,
                                SYS_USER_MERCHANT.USER_ID,
                                MERCHANT.ID), UserMerchantVo.class)
                .stream().collect(Collectors.groupingBy(e -> e.getUserId().toString()));
    }

    /**
     * 获取用户名称map
     * String
     */
    @RequestCache(prefix = "list_fill:user_name")
    public Map<String, String> getUserNameByUserId(Set<?> keys) {
        return LogicDeleteManager.execWithoutLogicDelete(() -> userMapper.selectListByQuery(QueryWrapper.create()
                        .where(SYS_USER.ID.in(keys))
                        .select(SYS_USER.ID, SYS_USER.NICKNAME))
                .stream().collect(Collectors.toMap(e -> e.getId().toString(), SysUserEntity::getNickname)));
    }

    /**
     * 获取成色名称map
     * String
     */
    @RequestCache(prefix = "list_fill:quality")
    public Map<String, String> getQualityNameById(Set<?> keys) {
         return qualityMapper.selectListByQuery(QueryWrapper.create()
                        .where(QUALITY.ID.in(keys))
                        .where(QUALITY.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .select(QUALITY.ID, QUALITY.NAME))
                .stream().collect(Collectors.toMap(e -> e.getId().toString(), QualityEntity::getName));
    }

    /**
     * 获取大类名称map
     * String
     */
    @RequestCache(prefix = "list_fill:category")
    public Map<String, String> getCategoryNameById(Set<?> keys) {
         return Arrays.stream(CategoryEnum.values()).collect(Collectors.toMap(e -> e.getValue().toString(), CategoryEnum::getLabel));
    }

    /**
     * 获取货品字段信息map
     * GoodsColumnVO
     */
    @RequestCache(prefix = "list_fill:goods_column")
    public Map<String, GoodsColumnVO> getGoodsColumnBySign(Set<?> keys) {
        return goodsColumnMapper.selectListByQueryAs(QueryWrapper.create()
                        .from(GoodsColumnEntity.class)
                        .where(GoodsColumnEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                        .where(GoodsColumnEntity::getSign).in(keys), GoodsColumnVO.class)
                .stream().collect(Collectors.toMap(GoodsColumnVO::getSign, column -> column));
    }

    /**
     * 获取门店名称map
     * String
     */
    @RequestCache(prefix = "list_fill:merchant")
    public Map<String, String> getMerchantNameById(Set<?> keys) {
        return merchantMapper.selectListByQuery(QueryWrapper.create()
                        .where(MERCHANT.ID.in(keys))
                        .where(MERCHANT.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .select(MERCHANT.ID, MERCHANT.NAME))
                .stream().collect(Collectors.toMap(e -> e.getId().toString(), MerchantEntity::getName));
    }

    /**
     * 获取柜台名称map
     * String
     */
    @RequestCache(prefix = "list_fill:counter")
    public Map<String, String> getCounterNameById(Set<?> keys) {
        return counterMapper.selectListByQuery(QueryWrapper.create()
                        .where(COUNTER.ID.in(keys))
                        .where(COUNTER.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .select(COUNTER.ID, COUNTER.NAME))
                .stream().collect(Collectors.toMap(e -> e.getId().toString(), CounterEntity::getName));
    }

    /**
     * 获取供应商名称map
     * String
     */
    @RequestCache(prefix = "list_fill:supplier")
    public Map<String, String> getSupplierNameById(Set<?> keys) {
        return supplierMapper.selectListByQuery(QueryWrapper.create()
                        .where(SUPPLIER.ID.in(keys))
                        .where(SUPPLIER.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .select(SUPPLIER.ID, SUPPLIER.NAME))
                .stream().collect(Collectors.toMap(e -> e.getId().toString(), SupplierEntity::getName));
    }

    /**
     * 获取小类名称map
     * String
     */
    @RequestCache(prefix = "list_fill:subclass")
    public Map<String, String> getSubclassNameById(Set<?> keys) {
        return subclassMapper.selectListByQuery(QueryWrapper.create()
                        .where(SUBCLASS.ID.in(keys))
                        .where(SUBCLASS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .select(SUBCLASS.ID, SUBCLASS.NAME))
                .stream().collect(Collectors.toMap(e -> e.getId().toString(), SubclassEntity::getName));
    }

    /**
     * 获取品牌名称map
     * String
     */
    @RequestCache(prefix = "list_fill:brand")
    public Map<String, String> getBrandNameById(Set<?> keys) {
        return brandMapper.selectListByQuery(QueryWrapper.create()
                        .where(BRAND.ID.in(keys))
                        .where(BRAND.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .select(BRAND.ID, BRAND.NAME))
                .stream().collect(Collectors.toMap(e -> e.getId().toString(), BrandEntity::getName));
    }

    /**
     * 获取款式名称map
     * String
     */
    @RequestCache(prefix = "list_fill:style")
    public Map<String, String> getStyleNameById(Set<?> keys) {
        return styleMapper.selectListByQuery(QueryWrapper.create()
                        .where(STYLE.ID.in(keys))
                        .where(STYLE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .select(STYLE.ID, STYLE.NAME))
                .stream().collect(Collectors.toMap(e -> e.getId().toString(), StyleEntity::getName));
    }

    /**
     * 获取工艺名称map
     * String
     */
    @RequestCache(prefix = "list_fill:technology")
    public Map<String, String> getTechnologyNameById(Set<?> keys) {
        return technologyMapper.selectListByQuery(QueryWrapper.create()
                        .where(TECHNOLOGY.ID.in(keys))
                        .where(TECHNOLOGY.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .select(TECHNOLOGY.ID, TECHNOLOGY.NAME))
                .stream().collect(Collectors.toMap(e -> e.getId().toString(), TechnologyEntity::getName));
    }

    /**
     * 获取珠石名称map
     * String
     */
    @RequestCache(prefix = "list_fill:jewelry")
    public Map<String, String> getJewelryMapperNameById(Set<?> keys) {
        return jewelryMapper.selectListByQuery(QueryWrapper.create()
                        .where(JEWELRY.ID.in(keys))
                        .where(JEWELRY.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .select(JEWELRY.ID, JEWELRY.NAME))
                .stream().collect(Collectors.toMap(e -> e.getId().toString(), JewelryEntity::getName));
    }

    /**
     * 获取入库模板名称map
     * String
     */
    @RequestCache(prefix = "list_fill:template")
    public Map<String, String> getTemplateNameById(Set<?> keys) {
        return goodsIncomeTemplateMapper.selectListByQuery(QueryWrapper.create()
                        .where(GOODS_INCOME_TEMPLATE.ID.in(keys))
                        .where(GOODS_INCOME_TEMPLATE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .select(GOODS_INCOME_TEMPLATE.ID, GOODS_INCOME_TEMPLATE.NAME))
                .stream().collect(Collectors.toMap(e -> e.getId().toString(), GoodsIncomeTemplateEntity::getName));
    }

    /**
     * 获取货品图片
     * String(英文逗号分割)
     */
    @RequestCache(prefix = "list_fill:goods_img")
    public Map<String, String> getGoodsImgStringByGoodsId(Set<?> keys) {
        return goodsHasImagesMapper.selectListByQuery(QueryWrapper.create()
                        .where(GOODS_HAS_IMAGES.GOODS_ID.in(keys))
                        .where(GOODS_HAS_IMAGES.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .select(GOODS_HAS_IMAGES.GOODS_ID, GOODS_HAS_IMAGES.URL)
                        .orderBy(GOODS_HAS_IMAGES.SORT, true)
                        .orderBy(GOODS_HAS_IMAGES.ID, true))
                .stream().collect(Collectors.groupingBy(e -> e.getGoodsId().toString(), Collectors.mapping(e -> e.getUrl(), Collectors.joining(","))));
    }

    /**
     * 获取货品图片列表
     * List<GoodsHasImagesEntity>
     */
    @RequestCache(prefix = "list_fill:goods_img_list")
    public Map<String, List<GoodsHasImagesEntity>> getGoodsImgByGoodsId(Set<?> keys) {
        return goodsHasImagesMapper.selectListByQuery(QueryWrapper.create()
                        .where(GOODS_HAS_IMAGES.GOODS_ID.in(keys))
                        .where(GOODS_HAS_IMAGES.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .orderBy(GOODS_HAS_IMAGES.SORT, true)
                        .orderBy(GOODS_HAS_IMAGES.ID, true))
                .stream().collect(Collectors.groupingBy(e -> e.getGoodsId().toString(), Collectors.toList()));
    }

    /**
     * 获取入库单图片列表
     * List<GoodsIncomeHasImagesEntity>
     */
    @RequestCache(prefix = "list_fill:income_img_list")
    public Map<String, List<GoodsIncomeHasImagesEntity>> getIncomeImgByIncomeDetailId(Set<?> keys) {
        return goodsIncomeHasImagesMapper.selectListByQuery(QueryWrapper.create()
                        .where(GOODS_INCOME_HAS_IMAGES.INCOME_DETAIL_ID.in(keys))
                        .where(GOODS_INCOME_HAS_IMAGES.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .orderBy(GOODS_INCOME_HAS_IMAGES.SORT, true)
                        .orderBy(GOODS_INCOME_HAS_IMAGES.ID, true))
                .stream().collect(Collectors.groupingBy(e -> e.getIncomeDetailId().toString(), Collectors.toList()));
    }

    /**
     * 获取自定义字段列表
     * List<CustomerColumnVo>
     */
    @RequestCache(prefix = "list_fill:column_vo")
    public Map<String, List<CustomColumnItemDTO>> getColumnVosById(Set<?> keys) {
        return goodsHasColumnsMapper.selectListByQueryAs(QueryWrapper.create()
                        .leftJoin(GOODS_COLUMN).on(GOODS_COLUMN.ID.eq(GOODS_HAS_COLUMNS.COLUMN_ID))
                        .where(GOODS_HAS_COLUMNS.GOODS_ID.in(keys))
                        .where(GOODS_COLUMN.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .select(GOODS_HAS_COLUMNS.COLUMN_SIGN.as("columnSign"),
                                GOODS_HAS_COLUMNS.VALUE.as("value"),
                                GOODS_COLUMN.TYPE.as("type"),
                                GOODS_COLUMN.ID.as("columnId"),
                                GOODS_COLUMN.IS_MULTIPLE.as("isMultiple"),
                                GOODS_COLUMN.OPTIONS.as("options"),
                                GOODS_COLUMN.SECRET_LEVEL.as("secretLevel"),
                                GOODS_HAS_COLUMNS.GOODS_ID.as("goodsId"),
                                GOODS_HAS_COLUMNS.IMAGE_ID.as("imageId"),
                                GOODS_COLUMN.NUMBER_PRECISION.as("numberPrecision")), CustomColumnItemDTO.class)
                .stream()
                .collect(Collectors.groupingBy(e -> e.getGoodsId().toString(), Collectors.toList()));
    }

    /**
     * 获取标签信息map
     * Map<String, Object>
     */
    @RequestCache(prefix = "list_fill:tag")
    public Map<String, Map<String, Object>> getTagNameById(Set<?> keys) {
        return printTagMapper.selectListByQuery(QueryWrapper.create()
                        .where(PRINT_TAG.ID.in(keys))
                        .select(PRINT_TAG.ID, PRINT_TAG.NAME, PRINT_TAG.IMAGE, PRINT_TAG.TYPE, PRINT_TAG.WIDTH, PRINT_TAG.HEIGHT))
                .stream().collect(Collectors.toMap(
                        e -> e.getId().toString(),
                        e -> {
                            Map<String, Object> info = new HashMap<>();
                            info.put("name", e.getName());
                            info.put("image", e.getImage());
                            info.put("type", e.getType());
                            info.put("width", e.getWidth());
                            info.put("height", e.getHeight());
                            return info;
                        }
                ));
    }

    /**
     * 获取入库单自定义字段列表
     * List<CustomerColumnVo>
     */
    @RequestCache(prefix = "list_fill:income_column_vo")
    public Map<String, List<CustomColumnItemDTO>> getIncomeColumnVosById(Set<?> keys) {
        return goodsIncomeHasColumnsMapper.selectListByQueryAs(QueryWrapper.create()
                        .leftJoin(GOODS_COLUMN).on(GOODS_COLUMN.ID.eq(GOODS_INCOME_HAS_COLUMNS.COLUMN_ID))
                        .where(GOODS_INCOME_HAS_COLUMNS.INCOME_DETAIL_ID.in(keys))
                        .where(GOODS_COLUMN.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .select(GOODS_INCOME_HAS_COLUMNS.COLUMN_SIGN.as("columnSign"),
                                GOODS_INCOME_HAS_COLUMNS.VALUE.as("value"),
                                GOODS_COLUMN.TYPE.as("type"),
                                GOODS_COLUMN.ID.as("columnId"),
                                GOODS_COLUMN.IS_MULTIPLE.as("isMultiple"),
                                GOODS_COLUMN.OPTIONS.as("options"),
                                GOODS_COLUMN.SECRET_LEVEL.as("secretLevel"),
                                GOODS_INCOME_HAS_COLUMNS.INCOME_ID.as("incomeId"),
                                GOODS_INCOME_HAS_COLUMNS.INCOME_DETAIL_ID.as("incomeDetailId"),
                                GOODS_INCOME_HAS_COLUMNS.IMAGE_ID.as("imageId"),
                                GOODS_COLUMN.NUMBER_PRECISION.as("numberPrecision")), CustomColumnItemDTO.class)
                .stream()
                .collect(Collectors.groupingBy(e -> e.getIncomeDetailId().toString(), Collectors.toList()));
    }
    /**
     * 根据盘点单id获取柜台列表
     * List<CounterEntity>
     */
    @RequestCache(prefix = "list_fill:take:counter")
    public Map<String, List<TakeCounterVo>> getCountersByTakeId(Set<?> keys) {
        return counterMapper.selectListByQueryAs(QueryWrapper.create()
                        .leftJoin(GOODS_TAKE_COUNTER).on(GOODS_TAKE_COUNTER.COUNTER_ID.eq(COUNTER.ID))
                        .where(GOODS_TAKE_COUNTER.TAKE_ID.in(keys))
                        .select(COUNTER.ID.as("id"),
                                COUNTER.NAME.as("name"),
                                GOODS_TAKE_COUNTER.TAKE_ID.as("takeId")), TakeCounterVo.class)
                .stream().collect(Collectors.groupingBy(vo -> vo.getTakeId().toString(), Collectors.toList()));
    }

    /**
     * 根据盘点单id获取盘点大类小类列表
     * List<CategoryNodeVo>
     */
    @RequestCache(prefix = "list_fill:take:category")
    public Map<String, List<CategoryNodeVo>> getCategoriesByTakeId(Set<?> keys) {
        List<CategoryNodeVo> nodeVos = takeCategoryMapper.selectListByQueryAs(QueryWrapper.create()
                .leftJoin(SUBCLASS).on(SUBCLASS.ID.eq(GOODS_TAKE_CATEGORY.SUBCLASS_ID))
                .where(GOODS_TAKE_CATEGORY.TAKE_ID.in(keys))
                .select(GOODS_TAKE_CATEGORY.TAKE_ID.as("takeId"),
                        GOODS_TAKE_CATEGORY.SUBCLASS_ID.as("subclassId"),
                        GOODS_TAKE_CATEGORY.CATEGORY_ID.as("categoryId"),
                        SUBCLASS.NAME.as("subclassName")), CategoryNodeVo.class);
        return nodeVos.stream().peek(vo -> {
            vo.setName(CategoryEnum.getByValue(vo.getCategoryId()).getLabel());
        }).collect(Collectors.groupingBy(vo -> vo.getTakeId().toString(), Collectors.toList()));
    }

    /**
     * 获取盘点款式
     * List<TakeStyleVo>
     */
    @RequestCache(prefix = "list_fill:take:style")
    public Map<String, List<TakeStyleVo>> getStylesById(Set<?> keys) {
        return styleMapper.selectListByQueryAs(QueryWrapper.create().from(STYLE)
                        .leftJoin(GOODS_TAKE_STYLE).on(STYLE.ID.eq(GOODS_TAKE_STYLE.STYLE_ID))
                .where(GOODS_TAKE_STYLE.TAKE_ID.in(keys))
                .select(STYLE.ID.as("id"),
                        STYLE.NAME.as("name"),
                        GOODS_TAKE_STYLE.TAKE_ID.as("takeId")), TakeStyleVo.class)
                .stream().collect(Collectors.groupingBy(vo -> vo.getTakeId().toString(), Collectors.toList()));
    }

    /**
     * 获取rfidMap
     * String
     */
    @RequestCache(prefix = "list_fill:take:rfid")
    public Map<String, String> getRfidByGoodsId(Set<?> keys) {
        return goodsHasRfidMapper.selectListByQuery(QueryWrapper.create()
                        .where(GOODS_HAS_RFID.GOODS_ID.in(keys))
                        .where(GOODS_HAS_RFID.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .select(GOODS_HAS_RFID.GOODS_ID, GOODS_HAS_RFID.RFID))
                .stream().collect(Collectors.toMap(item -> item.getGoodsId().toString(), GoodsHasRfidEntity::getRfid));
    }

    /**
     * 获取赠品图片列表map
     * List<GiftHasImagesEntity>
     */
    @RequestCache(prefix = "list_fill:gift_img_list")
    public Map<String, List<GiftHasImagesEntity>> getGiftImgByGiftId(Set<?> keys) {
        return giftHasImagesMapper.selectListByQuery(QueryWrapper.create()
                        .from(GIFT_HAS_IMAGES)
                        .where(GIFT_HAS_IMAGES.GIFT_ID.in(keys))
                        .where(GIFT_HAS_IMAGES.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .orderBy(GIFT_HAS_IMAGES.SORT, true)
                        .orderBy(GIFT_HAS_IMAGES.ID, true))
                .stream().collect(Collectors.groupingBy(e -> e.getGiftId().toString(), Collectors.toList()));
    }

}
