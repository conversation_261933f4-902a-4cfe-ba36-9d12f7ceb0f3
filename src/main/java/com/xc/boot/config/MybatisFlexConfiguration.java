package com.xc.boot.config;


import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.core.dialect.DbType;
import com.mybatisflex.core.dialect.DialectFactory;
import com.mybatisflex.core.logicdelete.LogicDeleteManager;
import com.mybatisflex.core.logicdelete.impl.DateTimeLogicDeleteProcessor;
import com.mybatisflex.spring.boot.MyBatisFlexCustomizer;
import com.xc.boot.common.enums.EnvEnum;
import com.xc.boot.common.listener.CreatedByListener;
import com.xc.boot.common.listener.CreatedByListenerFlag;
import com.xc.boot.common.listener.UpdatedByListener;
import com.xc.boot.common.listener.UpdatedByListenerFlag;
import com.xc.boot.common.util.ContextUtils;
import com.xc.boot.core.handler.DataPermissionDialectHandler;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Configuration
@Log4j2
public class MybatisFlexConfiguration implements MyBatisFlexCustomizer {
    @Value("${spring.profiles.active}")
    private String env;

    // sql记录
    private static final ThreadLocal<List<String>> SQL_RECORD = new ThreadLocal<>();

    @Override
    public void customize(FlexGlobalConfig config) {
        config.registerInsertListener(new CreatedByListener(), CreatedByListenerFlag.class);
        config.registerUpdateListener(new UpdatedByListener(), UpdatedByListenerFlag.class);
        // 开发、测试环境打印 SQL
        if (!env.equals(EnvEnum.PROD.getValue())) {
            //开启审计功能
            AuditManager.setAuditEnable(true);
            //设置 SQL 审计收集器
            AuditManager.setMessageCollector(auditMessage -> {
                String format = String.format("[%s - %sms] %s", auditMessage.getDsName(), auditMessage.getElapsedTime(), auditMessage.getFullSql());
                log.info(format);
                List<String> sqlList = Optional.ofNullable(SQL_RECORD.get()).orElse(new ArrayList<>());
                sqlList.add(format);
                SQL_RECORD.set(sqlList);
            });
        }
        // 注册数据权限
//        DialectFactory.registerDialect(DbType.MYSQL,new DataPermissionDialectHandler());
        LogicDeleteManager.setProcessor(new DateTimeLogicDeleteProcessor());
    }

    public static List<String> getSqlDebugAndClear() {
        try {
            if (!ContextUtils.isProd()) {
                return SQL_RECORD.get();
            }else {
                return null;
            }
        }finally {
             SQL_RECORD.remove();
        }
    }
}
