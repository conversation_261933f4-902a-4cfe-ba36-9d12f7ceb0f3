package com.xc.boot.core.security.extension;

import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.result.ResultCode;
import com.xc.boot.core.security.model.SysUserDetails;
import com.xc.boot.shared.auth.model.LoginForm;
import com.xc.boot.system.mapper.CompanyMapper;
import com.xc.boot.system.model.entity.CompanyEntity;
import com.xc.boot.system.model.entity.SysUserEntity;
import com.xc.boot.system.service.UserService;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

import static com.xc.boot.system.model.entity.table.SysUserTableDef.SYS_USER;


/**
 * <AUTHOR>
 * @ClassName SmsAuthenticationProvider
 * @Date: 2025/6/5 15:17
 * @Description: 短信验证码登录
 */
public class SmsAuthenticationProvider implements AuthenticationProvider {
    private final UserService userService;
    private final CompanyMapper companyMapper;

    public SmsAuthenticationProvider(UserService userService, CompanyMapper companyMapper) {
        this.userService = userService;
        this.companyMapper = companyMapper;
    }

    /**
     * 短信验证码认证逻辑
     */
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        LoginForm form = (LoginForm) authentication.getPrincipal();
        userService.verifyCode(form.getUsername(), form.getCode(), 1, form.getSideCode());
        SysUserEntity userEntity = userService.getOne(QueryWrapper.create()
                .where(SYS_USER.USERNAME.eq(form.getUsername())));

        if (Objects.isNull(userEntity)) {
            throw new BusinessException(ResultCode.USER_NOT_EXIST);
        }
        if (userEntity.getStatus().equals(0)) {
            throw new BusinessException(ResultCode.USER_ACCOUNT_LOCKED);
        }
        // 封装登录用户详细信息
        SysUserDetails sysUserDetails = new SysUserDetails(userEntity);
        // 公司
        CompanyEntity companyEntity = companyMapper.selectOneByQuery(QueryWrapper.create().where(CompanyEntity::getId).eq(userEntity.getCompanyId()));
        if (companyEntity.getStatus().equals(0)) {
            throw new BusinessException(ResultCode.COMPANY_ACCOUNT_LOCKED);
        }
        LocalDateTime expirationDate = companyEntity.getExpirationDate();
        if (Objects.nonNull(expirationDate)) {
            if (expirationDate.isBefore(LocalDateTime.now())) {
                throw new BusinessException(ResultCode.COMPANY_ACCOUNT_EXPIRED);
            }
        }
        sysUserDetails.setCompany(companyEntity);
        // 创建已认证的 SmsAuthenticationToken
        return SmsAuthenticationToken.authenticated(
                sysUserDetails,
                sysUserDetails.getAuthorities()
        );
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return SmsAuthenticationToken.class.isAssignableFrom(authentication);
    }
}
