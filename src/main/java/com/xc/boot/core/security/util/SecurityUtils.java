package com.xc.boot.core.security.util;

import cn.hutool.core.collection.CollectionUtil;
import com.xc.boot.common.constant.SystemConstants;
import com.xc.boot.core.security.model.SysUserDetails;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotNull;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Spring Security 工具类
 *
 * <AUTHOR>
 * @since 2021/1/10
 */
public class SecurityUtils {

    /**
     * 获取当前登录人信息
     *
     * @return Optional<SysUserDetails>
     */
    public static Optional<SysUserDetails> getUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            Object principal = authentication.getPrincipal();
            if (principal instanceof SysUserDetails) {
                return Optional.of((SysUserDetails) principal);
            }
        }
        return Optional.empty();
    }


    /**
     * 获取用户ID
     *
     * @return Long
     */
    public static Long getUserId() {
        return getUser().map(SysUserDetails::getUserId).orElse(null);
    }

    /**
     * 获取商户ID
     *
     * @return Long
     */
    public static Long getCompanyId() {
        return getUser().map(SysUserDetails::getCompanyId).orElse(null);
    }

    /**
     * 获取门店ID列表
     *
     * @return Long
     */
    @NotNull
    public static Set<Long> getMerchantIds() {
        Set<Long> longs = getUser().map(SysUserDetails::getMerchantIds).orElse(new HashSet<>());
        longs.add(0L);
        return longs;
    }


    /**
     * 获取用户账号
     *
     * @return String 用户账号
     */
    public static String getUsername() {
        return getUser().map(SysUserDetails::getUsername).orElse(null);
    }


    /**
     * 获取用户角色集合
     *
     * @return 角色集合
     */
    public static Set<String> getRoles() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            if (CollectionUtil.isNotEmpty(authorities)) {
                return authorities.stream().map(GrantedAuthority::getAuthority)
                        .collect(Collectors.toSet());
            }
        }
        return Collections.EMPTY_SET;
    }

    /**
     * 是否超级管理员
     * <p>
     * 超级管理员忽视任何权限判断
     */
    public static boolean isRoot() {
        Set<String> roles = getRoles();
        return roles.contains(SystemConstants.ROOT_ROLE_CODE);
    }

    /**
     * 是否主账号
     */
    public static boolean isMain() {
        return getUser().map(SysUserDetails::getIsMain).orElse(false);
    }

    /**
     * 是否查看机密
     */
    public static boolean showSecret() {
        return getUser().map(SysUserDetails::getShowSecret).orElse(false);
    }

    /**
     * 获取当前登录端
     */
    public static String  getSideCode() {
        return getUser().map(SysUserDetails::getSideCode).orElse("");
    }

    /**
     * 获取请求中的 Token
     *
     * @return Token 字符串
     */
    public static String getTokenFromRequest() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        return request.getHeader(HttpHeaders.AUTHORIZATION);
    }


}
