package com.xc.boot.modules.gift.controller;

import com.xc.boot.modules.gift.service.GiftIncomeService;
import com.xc.boot.modules.gift.service.GiftIncomeAuditService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.xc.boot.modules.gift.model.dto.GiftIncomeCreateDTO;
import com.xc.boot.modules.gift.model.dto.GiftIncomeAuditDTO;
import com.xc.boot.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import jakarta.validation.Valid;
import com.xc.boot.modules.gift.model.vo.GiftSnCheckVO;
import org.springframework.web.bind.annotation.RequestParam;
import com.xc.boot.modules.gift.model.query.GiftIncomePageQuery;
import com.xc.boot.modules.gift.model.vo.GiftIncomePageVO;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.modules.gift.model.dto.GiftIncomeUpdateDTO;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.modules.gift.model.vo.GiftIncomeInfoVO;
import com.xc.boot.modules.gift.model.query.GiftIncomeDetailPageQuery;
import com.xc.boot.modules.gift.model.vo.GiftIncomeDetailPageVO;
import com.xc.boot.modules.gift.model.dto.GiftIncomeDetailUpdateDTO;
import com.mybatisflex.core.paginate.Page;

/**
 * 赠品入库单控制层
 */
@Tag(name = "赠品-赠品入库单")
@RestController
@RequestMapping("/api/gift/income")
@RequiredArgsConstructor
public class GiftIncomeController {
    private final GiftIncomeService giftIncomeService;
    private final GiftIncomeAuditService giftIncomeAuditService;

    @Operation(summary = "创建赠品入库单")
    @PostMapping("/create")
    public Result<Integer> create(@RequestBody @Valid GiftIncomeCreateDTO dto) {
        Integer id = giftIncomeService.create(dto);
        return Result.success(id);
    }

    @Operation(summary = "检查赠品条码")
    @PostMapping("/check")
    public Result<GiftSnCheckVO> checkGiftSn(@RequestParam("giftSn") String giftSn) {
        GiftSnCheckVO vo = giftIncomeService.checkGiftSn(giftSn);
        return Result.success(vo);
    }

    @Operation(summary = "赠品入库单分页列表")
    @PostMapping("/page")
    public PageResult<GiftIncomePageVO> getIncomePage(@RequestBody GiftIncomePageQuery query) {
        return PageResult.success(giftIncomeService.getIncomePage(query));
    }

    @Operation(summary = "编辑赠品入库单")
    @PostMapping("/edit")
    public Result<Boolean> edit(@RequestBody @jakarta.validation.Valid GiftIncomeUpdateDTO dto) {
        boolean result = giftIncomeService.update(dto);
        return Result.success(result);
    }

    @Operation(summary = "删除赠品入库单")
    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestBody @jakarta.validation.Valid DeleteRequest request) {
        boolean result = giftIncomeService.delete(request.getId());
        return Result.success(result);
    }

    @Operation(summary = "审核赠品入库单")
    @PostMapping("/audit")
    public Result<Boolean> audit(@RequestBody @Valid GiftIncomeAuditDTO dto) {
        boolean result = giftIncomeAuditService.audit(dto.getIds());
        return Result.success(result);
    }

    @Operation(summary = "获取赠品入库单信息")
    @PostMapping("/info")
    public Result<GiftIncomeInfoVO> getIncomeInfo(@RequestBody @Valid DeleteRequest request) {
        GiftIncomeInfoVO info = giftIncomeService.getIncomeInfo(request.getId());
        return Result.success(info);
    }

    @Operation(summary = "赠品入库单明细分页列表")
    @PostMapping("/detail/page")
    public PageResult<GiftIncomeDetailPageVO> getIncomeDetailPage(@RequestBody @Valid GiftIncomeDetailPageQuery query) {
        Page<GiftIncomeDetailPageVO> page = giftIncomeService.getIncomeDetailPage(query);
        return PageResult.success(page);
    }

    @Operation(summary = "编辑赠品入库单明细")
    @PostMapping("/detail/edit")
    public Result<Boolean> editIncomeDetail(@RequestBody @Valid GiftIncomeDetailUpdateDTO dto) {
        boolean result = giftIncomeService.updateIncomeDetail(dto.getId(), dto);
        return Result.success(result);
    }

    @Operation(summary = "删除赠品入库单明细")
    @PostMapping("/detail/delete")
    public Result<Boolean> deleteIncomeDetail(@RequestBody @Valid DeleteRequest request) {
        boolean result = giftIncomeService.deleteIncomeDetail(request.getId());
        return Result.success(result);
    }
}
