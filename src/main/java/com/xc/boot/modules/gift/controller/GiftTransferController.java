package com.xc.boot.modules.gift.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.base.IdsRequest;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.gift.model.dto.*;
import com.xc.boot.modules.gift.model.query.GiftTransferDetailPageQuery;
import com.xc.boot.modules.gift.model.query.GiftTransferGoodsQuery;
import com.xc.boot.modules.gift.model.query.GiftTransferPageQuery;
import com.xc.boot.modules.gift.model.vo.GiftTransferGoodsVO;
import com.xc.boot.modules.gift.model.vo.GiftTransferInfoVO;
import com.xc.boot.modules.gift.model.vo.GiftTransferPageVO;
import com.xc.boot.modules.gift.service.GiftTransferService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequiredArgsConstructor
@Tag(name = "赠品-调拨单管理")
@RequestMapping("/api/gift/transfer")
public class GiftTransferController {

    private final GiftTransferService giftTransferService;

    @Operation(summary = "调拨单分页列表")
    @PostMapping("/page")
    public PageResult<GiftTransferPageVO> page(@Validated @RequestBody GiftTransferPageQuery query) {
        Page<GiftTransferPageVO> page = giftTransferService.pageTransfer(query);
        return PageResult.success(page);
    }

    @Operation(summary = "调拨赠品查询")
    @PostMapping("/goods/query")
    public Result<List<GiftTransferGoodsVO>> queryTransferGoods(@Validated @RequestBody GiftTransferGoodsQuery query) {
        List<GiftTransferGoodsVO> list = giftTransferService.queryTransferGoods(query);
        return Result.success(list);
    }

    @Operation(summary = "创建调拨单")
    @PostMapping("/create")
    public Result<Long> createTransfer(@Validated @RequestBody GiftTransferCreateDTO dto) {
        Long id = giftTransferService.createTransfer(dto);
        return Result.success(id);
    }

    @Operation(summary = "更新调拨单")
    @PutMapping("/update")
    public Result<Boolean> updateTransfer(@Validated @RequestBody GiftTransferUpdateDTO dto) {
        boolean result = giftTransferService.updateTransfer(dto);
        return Result.judge(result);
    }

    @Operation(summary = "删除调拨单")
    @DeleteMapping
    public Result<Boolean> deleteTransfer(@RequestBody @Valid DeleteRequest request) {
        boolean result = giftTransferService.deleteTransfer(request.getId());
        return Result.judge(result);
    }

    @Operation(summary = "审核调拨单")
    @PostMapping("/audit")
    public Result<Boolean> auditTransfer(@RequestBody IdsRequest request) {
        boolean result = giftTransferService.auditTransfer(request.getIds());
        return Result.judge(result);
    }

    @Operation(summary = "调拨单收货")
    @PostMapping("/receipt")
    public Result<Boolean> receiptTransfer(@Validated @RequestBody GiftTransferReceiptDTO dto) {
        boolean result = giftTransferService.receiptTransfer(dto);
        return Result.judge(result);
    }

    @Operation(summary = "获取调拨单基础信息")
    @PostMapping("/info")
    public Result<GiftTransferInfoVO> getTransferInfo(@Validated @RequestBody GiftTransferInfoRequest request) {
        GiftTransferInfoVO vo = giftTransferService.getTransferInfo(request);
        return Result.success(vo);
    }

    @Operation(summary = "调拨单明细分页列表")
    @PostMapping("/detail/page")
    public PageResult<GiftTransferGoodsVO> pageTransferDetail(@Validated @RequestBody GiftTransferDetailPageQuery query) {
        Page<GiftTransferGoodsVO> page = giftTransferService.pageTransferDetail(query);
        return PageResult.success(page);
    }

    @Operation(summary = "编辑调拨单明细")
    @PutMapping("/detail/update")
    public Result<Boolean> updateTransferDetail(@Validated @RequestBody GiftTransferDetailUpdateDTO dto) {
        boolean result = giftTransferService.updateTransferDetail(dto);
        return Result.judge(result);
    }

    @Operation(summary = "删除调拨单明细")
    @DeleteMapping("/detail/delete")
    public Result<Boolean> deleteTransferDetail(@RequestBody @Valid DeleteRequest request) {
        boolean result = giftTransferService.deleteTransferDetail(request.getId());
        return Result.judge(result);
    }
} 