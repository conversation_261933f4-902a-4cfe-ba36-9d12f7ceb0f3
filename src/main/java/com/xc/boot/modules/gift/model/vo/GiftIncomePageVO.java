package com.xc.boot.modules.gift.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "赠品入库单分页VO")
public class GiftIncomePageVO {
    @Schema(description = "入库单ID")
    private Long id;

    @Schema(description = "入库单号")
    private String incomeCode;

    @Schema(description = "所属门店")
    private Integer merchantId;
    @Schema(description = "门店名称")
    private String merchantName;

    @Schema(description = "供应商")
    private Integer supplierId;
    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "入库数量")
    private Integer num;

    @Schema(description = "总成本价(元)")
    private BigDecimal totalCostPrice;

    @Schema(description = "总标签价(元)")
    private BigDecimal totalTagPrice;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "创建人")
    private Integer createdBy;
    @Schema(description = "创建人名称")
    private String createdByName;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "审核人")
    private Integer auditBy;
    @Schema(description = "审核人名称")
    private String auditByName;

    @Schema(description = "审核时间")
    private Date auditAt;

    @Schema(description = "备注")
    private String remark;
} 