package com.xc.boot.modules.gift.model.vo;

import com.xc.boot.modules.gift.model.entity.GiftEntity;
import com.xc.boot.modules.gift.model.entity.GiftHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Data
@Schema(description = "赠品条码检查返回VO")
public class GiftSnCheckVO {
    @Schema(description = "赠品基础信息")
    private GiftEntity gift;

    @Schema(description = "赠品图片信息")
    private List<GiftHasImagesEntity> images;
} 