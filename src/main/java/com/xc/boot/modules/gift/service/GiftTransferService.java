package com.xc.boot.modules.gift.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.gift.model.entity.GiftTransferEntity;
import com.xc.boot.modules.gift.model.query.GiftTransferPageQuery;
import com.xc.boot.modules.gift.model.vo.GiftTransferPageVO;
import com.xc.boot.modules.gift.model.query.GiftTransferGoodsQuery;
import com.xc.boot.modules.gift.model.vo.GiftTransferGoodsVO;
import com.xc.boot.modules.gift.model.dto.GiftTransferCreateDTO;
import com.xc.boot.modules.gift.model.dto.GiftTransferUpdateDTO;
import com.xc.boot.modules.gift.model.dto.GiftTransferReceiptDTO;
import com.xc.boot.modules.gift.model.dto.GiftTransferInfoRequest;
import com.xc.boot.modules.gift.model.vo.GiftTransferInfoVO;
import com.xc.boot.modules.gift.model.query.GiftTransferDetailPageQuery;
import com.xc.boot.modules.gift.model.dto.GiftTransferDetailUpdateDTO;

import java.util.List;

public interface GiftTransferService extends IService<GiftTransferEntity> {
    /**
     * 调拨单分页列表
     */
    Page<GiftTransferPageVO> pageTransfer(GiftTransferPageQuery query);

    /**
     * 调拨赠品查询
     */
    List<GiftTransferGoodsVO> queryTransferGoods(GiftTransferGoodsQuery query);

    /**
     * 创建调拨单
     */
    Long createTransfer(GiftTransferCreateDTO dto);

    /**
     * 更新调拨单
     */
    boolean updateTransfer(GiftTransferUpdateDTO dto);

    /**
     * 删除调拨单
     */
    boolean deleteTransfer(Long id);

    /**
     * 审核调拨单
     */
    boolean auditTransfer(String ids);

    /**
     * 审核调拨单
     * @param ids 调拨单ID
     * @param skipLog 是否跳过日志
     * @return boolean
     */
    boolean auditTransfer(String ids, boolean skipLog);

    /**
     * 调拨单收货
     */
    boolean receiptTransfer(GiftTransferReceiptDTO dto);

    /**
     * 获取调拨单基础信息
     */
    GiftTransferInfoVO getTransferInfo(GiftTransferInfoRequest request);

    /**
     * 调拨单明细分页
     */
    Page<GiftTransferGoodsVO> pageTransferDetail(GiftTransferDetailPageQuery query);

    /**
     * 编辑调拨单明细
     */
    boolean updateTransferDetail(GiftTransferDetailUpdateDTO dto);

    /**
     * 删除调拨单明细
     */
    boolean deleteTransferDetail(Long id);
} 