package com.xc.boot.modules.gift.service.impl;

import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.gift.mapper.GiftIncomeDetailMapper;
import com.xc.boot.modules.gift.mapper.GiftIncomeHasImagesMapper;
import com.xc.boot.modules.gift.mapper.GiftIncomeMapper;
import com.xc.boot.modules.gift.mapper.GiftMapper;
import com.xc.boot.modules.gift.mapper.GiftHasImagesMapper;
import com.xc.boot.modules.gift.model.entity.GiftIncomeDetailEntity;
import com.xc.boot.modules.gift.model.entity.GiftIncomeEntity;
import com.xc.boot.modules.gift.model.entity.GiftIncomeHasImagesEntity;
import com.xc.boot.modules.gift.model.entity.GiftEntity;
import com.xc.boot.modules.gift.model.entity.GiftHasImagesEntity;
import com.xc.boot.modules.gift.service.GiftIncomeAuditService;
import com.xc.boot.common.util.GiftStockUtils;
import com.xc.boot.modules.gift.model.bo.GiftStockNumChangeBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 赠品入库审核服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GiftIncomeAuditServiceImpl implements GiftIncomeAuditService {

    private final GiftIncomeMapper giftIncomeMapper;
    private final GiftIncomeDetailMapper giftIncomeDetailMapper;
    private final GiftIncomeHasImagesMapper giftIncomeHasImagesMapper;
    private final GiftMapper giftMapper;
    private final GiftHasImagesMapper giftHasImagesMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean audit(String ids) {
        log.info("开始审核赠品入库单，IDs: {}", ids);
        
        try {
            // 1. 解析ids字符串，转换为ID列表
            List<Long> incomeIds = parseIds(ids);
            if (incomeIds.isEmpty()) {
                CommonUtils.abort("入库单ID不能为空");
            }

            // 2. 验证入库单是否存在且状态正确
            List<GiftIncomeEntity> incomes = validateIncomes(incomeIds);
            
            // 3. 批量查询入库单明细
            List<GiftIncomeDetailEntity> allDetails = getIncomeDetails(incomeIds);
            
            // 4. 按明细逐个处理，确保每个明细都有正确的gift_id
            processIncomeDetails(allDetails);
            
            // 5. 根据gift_id更新库存
            updateStockByGiftId(allDetails);
            
            // 6. 更新入库单状态
            updateIncomeStatus(incomes);
            
            // 7. 记录操作日志
            recordAuditLog(incomes, "审核赠品入库单");
            
            log.info("赠品入库单审核完成，IDs: {}", ids);
            return true;
            
        } catch (Exception e) {
            log.error("审核赠品入库单失败，IDs: {}", ids, e);
            CommonUtils.abort("审核赠品入库单失败：" + e.getMessage());
            return false;
        }
    }

    /**
     * 解析ids字符串，转换为ID列表
     */
    private List<Long> parseIds(String ids) {
        if (StrUtil.isBlank(ids)) {
            return new ArrayList<>();
        }
        return Arrays.stream(ids.split(","))
            .map(String::trim)
            .filter(StrUtil::isNotBlank)
            .map(Long::parseLong)
            .collect(Collectors.toList());
    }

    /**
     * 验证入库单是否存在且状态正确
     */
    private List<GiftIncomeEntity> validateIncomes(List<Long> incomeIds) {
        List<GiftIncomeEntity> incomes = giftIncomeMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GiftIncomeEntity::getId).in(incomeIds)
                .and(GiftIncomeEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );
        
        if (incomes.size() != incomeIds.size()) {
            CommonUtils.abort("部分入库单不存在或无权限操作");
        }
        
        // 检查状态，只有待审核的入库单才能审核
        for (GiftIncomeEntity income : incomes) {
            if (income.getStatus() != 0) {
                CommonUtils.abort("入库单 [" + income.getIncomeCode() + "] 状态不正确，无法审核");
            }
        }
        
        return incomes;
    }

    /**
     * 批量查询入库单明细
     */
    private List<GiftIncomeDetailEntity> getIncomeDetails(List<Long> incomeIds) {
        return giftIncomeDetailMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GiftIncomeDetailEntity::getReceiveId).in(incomeIds)
                .and(GiftIncomeDetailEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );
    }

    /**
     * 按明细逐个处理，确保每个明细都有正确的gift_id
     */
    private void processIncomeDetails(List<GiftIncomeDetailEntity> details) {
        if (details.isEmpty()) {
            return;
        }
        
        // 收集所有条码
        Set<String> giftSns = details.stream()
            .map(GiftIncomeDetailEntity::getGiftSn)
            .filter(StrUtil::isNotBlank)
            .collect(Collectors.toSet());
            
        // 一次性查询所有条码对应的赠品记录
        Map<String, GiftEntity> existingGiftsMap = new HashMap<>();
        if (!giftSns.isEmpty()) {
            List<GiftEntity> existingGifts = giftMapper.selectListByQuery(
                QueryWrapper.create()
                    .where(GiftEntity::getGiftSn).in(giftSns)
                    .and(GiftEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
            );
            existingGiftsMap = existingGifts.stream()
                .collect(Collectors.toMap(GiftEntity::getGiftSn, gift -> gift));
        }

        // 收集所有需要查询的ID
        Set<Long> detailIds = details.stream()
            .map(GiftIncomeDetailEntity::getId)
            .collect(Collectors.toSet());
        Set<Long> giftIds = existingGiftsMap.values().stream()
            .map(GiftEntity::getId)
            .collect(Collectors.toSet());

        // 批量获取图片信息
        Map<String, List<GiftIncomeHasImagesEntity>> detailImagesMap = new HashMap<>();
        Map<String, List<GiftHasImagesEntity>> giftImagesMap = new HashMap<>();

        if (!detailIds.isEmpty()) {
            // 获取入库单明细的图片
            List<GiftIncomeHasImagesEntity> detailImages = giftIncomeHasImagesMapper.selectListByQuery(
                QueryWrapper.create()
                    .where(GiftIncomeHasImagesEntity::getIncomeDetailId).in(detailIds)
                    .and(GiftIncomeHasImagesEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
            );
            detailImagesMap = detailImages.stream()
                .collect(Collectors.groupingBy(image -> image.getIncomeDetailId().toString()));
        }

        if (!giftIds.isEmpty()) {
            // 获取赠品的图片
            List<GiftHasImagesEntity> giftImages = giftHasImagesMapper.selectListByQuery(
                QueryWrapper.create()
                    .where(GiftHasImagesEntity::getGiftId).in(giftIds)
                    .and(GiftHasImagesEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
            );
            giftImagesMap = giftImages.stream()
                .collect(Collectors.groupingBy(image -> image.getGiftId().toString()));
        }

        // 处理每个明细
        for (GiftIncomeDetailEntity detail : details) {
            String giftSn = detail.getGiftSn();
            if (StrUtil.isBlank(giftSn)) {
                continue;
            }

            GiftEntity existingGift = existingGiftsMap.get(giftSn);
            List<GiftIncomeHasImagesEntity> detailImages = detailImagesMap.get(detail.getId().toString());
            List<GiftHasImagesEntity> existingImages = existingGift != null ? 
                giftImagesMap.get(existingGift.getId().toString()) : new ArrayList<>();

            if (existingGift == null) {
                // 如果赠品不存在，创建新赠品
                GiftEntity newGift = createNewGift(detail);
                giftMapper.insert(newGift);
                
                // 复制图片
                if (detailImages != null && !detailImages.isEmpty()) {
                    copyGiftImages(detailImages, newGift.getId());
                }
                
                // 更新明细中的赠品ID
                detail.setGiftId(newGift.getId().intValue());
                giftIncomeDetailMapper.update(detail);
                
            } else {
                // 如果赠品存在，检查信息一致性
                String inconsistentMessage = isGiftInfoConsistent(detail, existingGift, detailImages, existingImages);
                if (StrUtil.isNotBlank(inconsistentMessage)) {
                    CommonUtils.abort("赠品条码 [" + giftSn + "] 已存在，但信息不一致：" + inconsistentMessage);
                }
                
                // 更新明细中的赠品ID
                detail.setGiftId(existingGift.getId().intValue());
                giftIncomeDetailMapper.update(detail);
                
                // 复制新的图片到赠品
                if (detailImages != null && !detailImages.isEmpty()) {
                    copyGiftImages(detailImages, existingGift.getId());
                }
            }
        }
    }

    /**
     * 检查赠品信息一致性
     */
    private String isGiftInfoConsistent(
        GiftIncomeDetailEntity detail,
        GiftEntity existingGift,
        List<GiftIncomeHasImagesEntity> detailImages,
        List<GiftHasImagesEntity> existingImages
    ) {
        List<String> inconsistentFields = new ArrayList<>();

        // 检查基础信息
        if (!Objects.equals(detail.getName(), existingGift.getName())) {
            inconsistentFields.add("赠品名称");
        }

        if (!Objects.equals(detail.getMerchantId(), existingGift.getMerchantId())) {
            inconsistentFields.add("所属门店");
        }

        if (!Objects.equals(detail.getSupplierId(), existingGift.getSupplierId())) {
            inconsistentFields.add("供应商");
        }

        // 检查重量信息（使用BigDecimal比较）
        if (detail.getWeight() != null && existingGift.getWeight() != null) {
            if (detail.getWeight().compareTo(existingGift.getWeight()) != 0) {
                inconsistentFields.add("重量");
            }
        } else if (detail.getWeight() != null || existingGift.getWeight() != null) {
            inconsistentFields.add("重量");
        }

        // 检查价格信息
        if (!Objects.equals(detail.getCostPrice(), existingGift.getCostPrice())) {
            inconsistentFields.add("成本价");
        }

        if (!Objects.equals(detail.getTagPrice(), existingGift.getTagPrice())) {
            inconsistentFields.add("标签价");
        }

        // 如果有不一致的字段，构建错误信息
        if (!inconsistentFields.isEmpty()) {
            StringBuilder errorMessage = new StringBuilder();
            for (String field : inconsistentFields) {
                errorMessage.append("- ").append(field).append("\n");
            }
            return errorMessage.toString();
        }

        return "";
    }

    /**
     * 创建新赠品
     */
    private GiftEntity createNewGift(GiftIncomeDetailEntity detail) {
        GiftEntity gift = new GiftEntity();
        gift.setCompanyId(SecurityUtils.getCompanyId().intValue());
        gift.setMerchantId(detail.getMerchantId());
        gift.setSupplierId(detail.getSupplierId());
        gift.setGiftSn(detail.getGiftSn());
        gift.setName(detail.getName());
        gift.setWeight(detail.getWeight());
        gift.setCostPrice(detail.getCostPrice());
        gift.setTagPrice(detail.getTagPrice());
        gift.setNum(0);
        gift.setStockNum(0);
        gift.setSoldNum(0);
        gift.setTransferNum(0);
        gift.setFrozenNum(0);
        return gift;
    }

    /**
     * 复制赠品图片
     */
    private void copyGiftImages(List<GiftIncomeHasImagesEntity> detailImages, Long giftId) {
        List<GiftHasImagesEntity> giftImages = new ArrayList<>();
        for (GiftIncomeHasImagesEntity detailImage : detailImages) {
            GiftHasImagesEntity giftImage = new GiftHasImagesEntity();
            giftImage.setCompanyId(detailImage.getCompanyId());
            giftImage.setGiftId(giftId.intValue());
            giftImage.setImageId(detailImage.getImageId());
            giftImage.setUrl(detailImage.getUrl());
            giftImage.setSort(detailImage.getSort());
            giftImages.add(giftImage);
        }
        if (!giftImages.isEmpty()) {
            giftHasImagesMapper.insertBatch(giftImages);
        }
    }

    /**
     * 根据gift_id更新库存
     */
    private void updateStockByGiftId(List<GiftIncomeDetailEntity> details) {
        if (details.isEmpty()) {
            return;
        }

        // 转换为库存变更对象
        List<GiftStockNumChangeBO> changes = details.stream()
                .filter(detail -> detail.getGiftId() != null)
                .map(detail -> GiftStockNumChangeBO.builder()
                        .giftId(detail.getGiftId().longValue())
                        .num(detail.getNum())
                        .stockNum(detail.getNum())
                        .comment("赠品入库")
                        .build())
                .toList();

        // 使用GiftStockUtils更新库存
        GiftStockUtils.updateGiftStocks(changes);
    }

    /**
     * 更新入库单状态
     */
    private void updateIncomeStatus(List<GiftIncomeEntity> incomes) {
        if (incomes.isEmpty()) {
            return;
        }

        Date now = new Date();
        Long auditUserId = SecurityUtils.getUserId();
        
        // 构建更新实体
        GiftIncomeEntity updateEntity = new GiftIncomeEntity();
        updateEntity.setStatus(1);
        updateEntity.setAuditBy(auditUserId != null ? auditUserId.intValue() : null);
        updateEntity.setAuditAt(now);

        // 构建更新条件
        QueryWrapper queryWrapper = QueryWrapper.create()
            .where(GiftIncomeEntity::getId).in(incomes.stream()
                .map(GiftIncomeEntity::getId)
                .collect(Collectors.toList()))
            .and(GiftIncomeEntity::getCompanyId).eq(SecurityUtils.getCompanyId());

        // 执行批量更新
        int updatedRows = giftIncomeMapper.updateByQuery(updateEntity, queryWrapper);
        if (updatedRows != incomes.size()) {
            CommonUtils.abort("更新入库单状态失败，预期更新 " + incomes.size() + " 条记录，实际更新 " + updatedRows + " 条记录");
        }

        // 更新入库单明细状态
        GiftIncomeDetailEntity detailUpdateEntity = new GiftIncomeDetailEntity();
        detailUpdateEntity.setStatus(1);
        detailUpdateEntity.setAuditBy(auditUserId != null ? auditUserId.intValue() : null);
        detailUpdateEntity.setAuditAt(now);

        // 构建明细更新条件
        QueryWrapper detailQueryWrapper = QueryWrapper.create()
            .where(GiftIncomeDetailEntity::getReceiveId).in(incomes.stream()
                .map(GiftIncomeEntity::getId)
                .collect(Collectors.toList()))
            .and(GiftIncomeDetailEntity::getCompanyId).eq(SecurityUtils.getCompanyId());

        // 执行明细批量更新
        int updatedDetailRows = giftIncomeDetailMapper.updateByQuery(detailUpdateEntity, detailQueryWrapper);
        if (updatedDetailRows <= 0) {
            log.warn("更新入库单明细状态失败，预期更新明细记录，实际更新 {} 条记录", updatedDetailRows);
        }
    }

    /**
     * 记录审核日志
     */
    private void recordAuditLog(List<GiftIncomeEntity> incomes, String remark) {
        if (incomes.isEmpty()) {
            return;
        }

        String incomeCodes = incomes.stream()
            .map(GiftIncomeEntity::getIncomeCode)
            .collect(Collectors.joining(", "));

        OpLogUtils.appendOpLog("赠品入库单审核", remark + ": " + incomeCodes, 
            Map.of("入库单数", incomes.size(), "入库单号", incomeCodes));
    }
} 