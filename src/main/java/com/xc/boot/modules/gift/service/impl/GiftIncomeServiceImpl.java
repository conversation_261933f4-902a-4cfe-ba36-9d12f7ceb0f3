package com.xc.boot.modules.gift.service.impl;

import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.modules.gift.model.entity.GiftIncomeEntity;
import com.xc.boot.modules.gift.mapper.GiftIncomeMapper;
import com.xc.boot.modules.gift.service.GiftIncomeService;
import com.xc.boot.modules.gift.service.GiftIncomeAuditService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.xc.boot.modules.gift.model.dto.GiftIncomeCreateDTO;
import com.xc.boot.modules.gift.model.dto.GiftIncomeDetailCreateDTO;
import com.xc.boot.modules.gift.model.dto.GiftIncomeUpdateDTO;
import com.xc.boot.modules.gift.model.entity.GiftIncomeDetailEntity;
import com.xc.boot.modules.gift.model.entity.GiftIncomeHasImagesEntity;
import com.xc.boot.modules.gift.model.entity.GiftEntity;
import com.xc.boot.modules.gift.model.entity.GiftHasImagesEntity;
import com.xc.boot.modules.gift.model.query.GiftIncomePageQuery;
import com.xc.boot.modules.gift.model.vo.GiftIncomePageVO;
import com.xc.boot.modules.gift.model.vo.GiftIncomeInfoVO;
import com.xc.boot.modules.gift.model.vo.GiftSnCheckVO;
import com.xc.boot.modules.gift.mapper.GiftIncomeDetailMapper;
import com.xc.boot.modules.gift.mapper.GiftIncomeHasImagesMapper;
import com.xc.boot.modules.gift.mapper.GiftMapper;
import com.xc.boot.modules.gift.mapper.GiftHasImagesMapper;
import com.xc.boot.common.util.SnUtils;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.common.base.FileItemDTO;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.common.util.listFill.ListFillUtil;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.excel.ExcelUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.query.QueryMethods;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.*;
import com.xc.boot.modules.gift.model.query.GiftIncomeDetailPageQuery;
import com.xc.boot.modules.gift.model.vo.GiftIncomeDetailPageVO;
import com.xc.boot.modules.gift.model.dto.GiftIncomeDetailUpdateDTO;
import cn.hutool.core.collection.CollectionUtil;
import java.util.stream.Collectors;
import com.mybatisflex.core.row.Row;
import com.xc.boot.common.util.QueryUtils;

/**
 * 赠品入库服务实现类
 */
@Service
@RequiredArgsConstructor
public class GiftIncomeServiceImpl extends ServiceImpl<GiftIncomeMapper, GiftIncomeEntity> implements GiftIncomeService {
    private final GiftIncomeDetailMapper giftIncomeDetailMapper;
    private final GiftIncomeHasImagesMapper giftIncomeHasImagesMapper;
    private final GiftMapper giftMapper;
    private final GiftHasImagesMapper giftHasImagesMapper;
    private final ListFillService listFillService;
    private final GiftIncomeAuditService giftIncomeAuditService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer create(GiftIncomeCreateDTO dto) {
        // 1. 生成入库单号
        String incomeCode = SnUtils.generateIncomeCode();
        // 2. 创建主表
        GiftIncomeEntity income = new GiftIncomeEntity();
        income.setIncomeCode(incomeCode);
        income.setCompanyId(SecurityUtils.getCompanyId().intValue());
        income.setMerchantId(dto.getMerchantId());
        income.setSupplierId(dto.getSupplierId() == null ? 0 : dto.getSupplierId());
        income.setRemark(dto.getRemark());
        income.setStatus(0); // 待审核
        income.setCreatedBy(SecurityUtils.getUserId().intValue());
        income.setAuditBy(0);
        boolean success = this.save(income);
        CommonUtils.abortIf(!success, "保存赠品入库单失败");
        // 3. 批量保存明细
        List<GiftIncomeDetailEntity> details = new ArrayList<>();
        BigDecimal totalWeight = BigDecimal.ZERO;
        Long totalCostPrice = 0L;
        Long totalTagPrice = 0L;
        int totalNum = 0;
        for (GiftIncomeDetailCreateDTO detailDTO : dto.getDetails()) {
            GiftIncomeDetailEntity detail = new GiftIncomeDetailEntity();
            detail.setReceiveId(income.getId().intValue());
            detail.setIncomeCode(incomeCode);
            detail.setCompanyId(income.getCompanyId());
            detail.setMerchantId(income.getMerchantId());
            detail.setSupplierId(dto.getSupplierId() == null ? 0 : dto.getSupplierId());
            detail.setGiftSn(detailDTO.getGiftSn());
            detail.setName(detailDTO.getGiftName());
            detail.setNum(detailDTO.getNum());
            detail.setWeight(detailDTO.getWeight());
            detail.setCostPrice(PriceUtil.yuan2fen(detailDTO.getCostPrice()).intValue());
            detail.setTagPrice(PriceUtil.yuan2fen(detailDTO.getTagPrice()).intValue());
            detail.setStatus(0);
            detail.setAuditBy(0);
            details.add(detail);
            // 汇总
            if (detailDTO.getWeight() != null) {
                totalWeight = totalWeight.add(detailDTO.getWeight());
            }
            if (detailDTO.getCostPrice() != null) {
                totalCostPrice += PriceUtil.yuan2fen(detailDTO.getCostPrice()).longValue() * detailDTO.getNum();
            }
            if (detailDTO.getTagPrice() != null) {
                totalTagPrice += PriceUtil.yuan2fen(detailDTO.getTagPrice()).longValue() * detailDTO.getNum();
            }
            totalNum += detailDTO.getNum();
        }
        int rows = giftIncomeDetailMapper.insertBatch(details);
        CommonUtils.abortIf(rows <= 0, "保存赠品入库明细失败");
        // 4. 处理图片
        List<GiftIncomeHasImagesEntity> allImages = new ArrayList<>();
        List<Long> allImageIds = new ArrayList<>();
        // 重新查询明细以获取ID
        List<GiftIncomeDetailEntity> savedDetails = giftIncomeDetailMapper.selectListByQuery(
            com.mybatisflex.core.query.QueryWrapper.create().where(GiftIncomeDetailEntity::getIncomeCode).eq(incomeCode)
        );
        for (int i = 0; i < savedDetails.size(); i++) {
            GiftIncomeDetailEntity detail = savedDetails.get(i);
            GiftIncomeDetailCreateDTO detailDTO = dto.getDetails().get(i);
            if (detailDTO.getImage() != null && !detailDTO.getImage().isEmpty()) {
                for (int j = 0; j < detailDTO.getImage().size(); j++) {
                    FileItemDTO image = detailDTO.getImage().get(j);
                    if (image.getUrl() == null || image.getUrl().isEmpty()) continue;
                    GiftIncomeHasImagesEntity imageEntity = new GiftIncomeHasImagesEntity();
                    imageEntity.setCompanyId(income.getCompanyId());
                    imageEntity.setIncomeId(income.getId().intValue());
                    imageEntity.setIncomeDetailId(detail.getId().intValue());
                    imageEntity.setImageId(image.getId().intValue());
                    imageEntity.setUrl(image.getUrl());
                    imageEntity.setSort(j + 1);
                    allImages.add(imageEntity);
                    allImageIds.add(image.getId());
                }
            }
        }
        if (!allImages.isEmpty()) {
            giftIncomeHasImagesMapper.insertBatch(allImages);
            CommonUtils.batchUpdateFileStatus(allImageIds, 1);
        }
        // 5. 汇总更新主表
        income.setNum(totalNum);
        income.setTotalWeight(totalWeight);
        income.setTotalCostPrice(totalCostPrice);
        income.setTotalTagPrice(totalTagPrice);
        success = this.updateById(income);
        CommonUtils.abortIf(!success, "更新赠品入库单汇总数据失败");
        // 6. 日志
        OpLogUtils.appendOpLog("创建赠品入库单", "创建赠品入库单: " + incomeCode + ", 明细数: " + details.size(), dto);
        
        // 7. 如果关闭了审核, 则继续执行审核逻辑
        if (!CommonUtils.getCompanySettings(SecurityUtils.getCompanyId()).getGiftIncomeAuditEnabled()) {
            giftIncomeAuditService.audit(income.getId().toString());
        }
        
        return income.getId().intValue();
    }

    @Override
    public GiftSnCheckVO checkGiftSn(String giftSn) {
        GiftEntity gift = giftMapper.selectOneByQuery(
            com.mybatisflex.core.query.QueryWrapper.create()
                .where(GiftEntity::getGiftSn).eq(giftSn)
                .and(GiftEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                // 门店权限限制
                .and(GiftEntity::getMerchantId).in(SecurityUtils.getMerchantIds())
        );
        if (gift == null) {
            return null;
        }
        java.util.List<GiftHasImagesEntity> images = giftHasImagesMapper.selectListByQuery(
            com.mybatisflex.core.query.QueryWrapper.create()
                .where(GiftHasImagesEntity::getGiftId).eq(gift.getId())
                .and(GiftHasImagesEntity::getCompanyId).eq(gift.getCompanyId())
                .orderBy(GiftHasImagesEntity::getSort, true)
        );
        GiftSnCheckVO vo = new GiftSnCheckVO();
        vo.setGift(gift);
        vo.setImages(images);
        return vo;
    }

    @Override
    public Page<GiftIncomePageVO> getIncomePage(GiftIncomePageQuery queryParams) {
        QueryWrapper query = QueryWrapper.create()
            .select(
                QueryMethods.column(GiftIncomeEntity::getIncomeCode),
                QueryMethods.column(GiftIncomeEntity::getMerchantId),
                QueryMethods.column(GiftIncomeEntity::getSupplierId),
                QueryMethods.column(GiftIncomeEntity::getNum),
                QueryMethods.column(GiftIncomeEntity::getTotalCostPrice),
                QueryMethods.column(GiftIncomeEntity::getTotalTagPrice),
                QueryMethods.column(GiftIncomeEntity::getStatus),
                QueryMethods.column(GiftIncomeEntity::getCreatedBy),
                QueryMethods.column(GiftIncomeEntity::getCreatedAt),
                QueryMethods.column(GiftIncomeEntity::getAuditBy),
                QueryMethods.column(GiftIncomeEntity::getAuditAt),
                QueryMethods.column(GiftIncomeEntity::getRemark)
            )
            // 公司隔离
            .where(GiftIncomeEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
            // 门店权限限制
            .and(GiftIncomeEntity::getMerchantId).in(SecurityUtils.getMerchantIds());
        // 入库单号精确查询
        if (StrUtil.isNotBlank(queryParams.getIncomeCode())) {
            query.and(GiftIncomeEntity::getIncomeCode).eq(queryParams.getIncomeCode());
        }
        // 所属门店精确查询
        if (StrUtil.isNotBlank(queryParams.getMerchantIds())) {
            List<Integer> merchantIds = QueryUtils.parseIntegerIds(queryParams.getMerchantIds());
            if (!merchantIds.isEmpty()) {
                query.and(GiftIncomeEntity::getMerchantId).in(merchantIds);
            }
        }
        // 供应商精确查询
        if (StrUtil.isNotBlank(queryParams.getSupplierIds())) {
            List<Integer> supplierIds = QueryUtils.parseIntegerIds(queryParams.getSupplierIds());
            if (!supplierIds.isEmpty()) {
                query.and(GiftIncomeEntity::getSupplierId).in(supplierIds);
            }
        }
        // 状态精确查询
        if (queryParams.getStatus() != null) {
            query.and(GiftIncomeEntity::getStatus).eq(queryParams.getStatus());
        }
        // 创建人精确查询
        if (StrUtil.isNotBlank(queryParams.getCreatedByIds())) {
            List<Integer> createdByIds = QueryUtils.parseIntegerIds(queryParams.getCreatedByIds());
            if (!createdByIds.isEmpty()) {
                query.and(GiftIncomeEntity::getCreatedBy).in(createdByIds);
            }
        }
        // 审核人精确查询
        if (StrUtil.isNotBlank(queryParams.getAuditByIds())) {
            List<Integer> auditByIds = QueryUtils.parseIntegerIds(queryParams.getAuditByIds());
            if (!auditByIds.isEmpty()) {
                query.and(GiftIncomeEntity::getAuditBy).in(auditByIds);
            }
        }
        // 创建时间范围查询
        if (queryParams.getCreatedAtRange() != null && queryParams.getCreatedAtRange().size() >= 2) {
            query.and(GiftIncomeEntity::getCreatedAt)
                .between(DateUtil.parseDateTime(queryParams.getCreatedAtRange().get(0)), DateUtil.parseDateTime(queryParams.getCreatedAtRange().get(1)));
        }
        // 审核时间范围查询
        if (queryParams.getAuditAtRange() != null && queryParams.getAuditAtRange().size() >= 2) {
            query.and(GiftIncomeEntity::getAuditAt)
                .between(DateUtil.parseDateTime(queryParams.getAuditAtRange().get(0)), DateUtil.parseDateTime(queryParams.getAuditAtRange().get(1)));
        }
        // 默认按ID倒序
        query.orderBy(GiftIncomeEntity::getId, false);
        Page<GiftIncomePageVO> page = this.mapper.paginateAs(queryParams.getPageNum(), queryParams.getPageSize(), query, GiftIncomePageVO.class);
        // 批量填充门店、供应商、用户名称
        if (page.getRecords() != null && !page.getRecords().isEmpty()) {
            fillIncomePageVOs(page.getRecords());
        }
        // 导出
        if (queryParams.getExport() != null && queryParams.getExport() == 1) {
            exportIncomes(query, queryParams);
            return null;
        }
        // 打印
        if (queryParams.getPrint() != null && queryParams.getPrint() == 1) {
            return printIncomes(query, queryParams);
        }
        return page;
    }

    /**
     * 批量填充GiftIncomePageVO的门店、供应商、用户名称
     */
    private void fillIncomePageVOs(List<GiftIncomePageVO> voList) {
        if (voList == null || voList.isEmpty()) {
            return;
        }
        Set<Integer> merchantIds = new HashSet<>();
        Set<Integer> supplierIds = new HashSet<>();
        Set<Integer> userIds = new HashSet<>();
        for (GiftIncomePageVO vo : voList) {
            if (vo.getMerchantId() != null) merchantIds.add(vo.getMerchantId());
            if (vo.getSupplierId() != null) supplierIds.add(vo.getSupplierId());
            if (vo.getCreatedBy() != null) userIds.add(vo.getCreatedBy());
            if (vo.getAuditBy() != null) userIds.add(vo.getAuditBy());
        }
        ListFillUtil.of(voList)
            .build(listFillService::getMerchantNameById, merchantIds, "merchantId", "merchantName")
            .build(listFillService::getSupplierNameById, supplierIds, "supplierId", "supplierName")
            .build(listFillService::getUserNameByUserId, userIds, "createdBy", "createdByName")
            .build(listFillService::getUserNameByUserId, userIds, "auditBy", "auditByName")
            .handle();
    }

    /**
     * 导出赠品入库单列表
     */
    private void exportIncomes(QueryWrapper query, GiftIncomePageQuery queryParams) {
        // 检查导出数量限制
        long count = this.mapper.selectCountByQuery(query);
        CommonUtils.abortIf(count > CommonUtils.getMaxExportSize(), "导出数量超过限制");
        ExcelUtil.of(this.mapper, query, GiftIncomePageVO.class, "gift_incomes", "赠品入库单列表")
                .getData((mapper, wrapper) -> {
                    List<GiftIncomePageVO> voList = mapper.selectListByQueryAs(wrapper, GiftIncomePageVO.class);
                    fillIncomePageVOs(voList);
                    return voList;
                })
                .doExport();
    }

    /**
     * 打印赠品入库单列表，返回填充后的列表数据（不分页）
     */
    private Page<GiftIncomePageVO> printIncomes(QueryWrapper query, GiftIncomePageQuery queryParams) {
        // 检查打印数量限制
        long count = this.mapper.selectCountByQuery(query);
        CommonUtils.abortIf(count > CommonUtils.getMaxPrintSize(), "打印数量超过限制");
        List<GiftIncomePageVO> voList = this.mapper.selectListByQueryAs(query, GiftIncomePageVO.class);
        if (!voList.isEmpty()) {
            fillIncomePageVOs(voList);
        }
        Page<GiftIncomePageVO> page = new Page<>(1, voList.size(), count);
        page.setRecords(voList);
        return page;
    }

    @Override
    public boolean update(GiftIncomeUpdateDTO dto) {
        GiftIncomeEntity entity = this.getById(dto.getId());
        CommonUtils.abortIf(entity == null, "入库单不存在");
        CommonUtils.abortIf(entity.getStatus() != 0, "仅待审核的入库单可编辑");
        // 门店权限检查
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(entity.getMerchantId().longValue()), "无权操作其他门店的数据");
        entity.setRemark(dto.getRemark());
        boolean result = this.updateById(entity);
        if (result) {
            OpLogUtils.appendOpLog("编辑赠品入库单", "编辑赠品入库单: " + entity.getIncomeCode() + ", 备注: " + dto.getRemark(), dto);
        }
        return result;
    }

    @Override
    public boolean delete(Long id) {
        // 1. 查询入库单
        GiftIncomeEntity income = this.getById(id);
        CommonUtils.abortIf(income == null, "入库单不存在");
        CommonUtils.abortIf(income.getStatus() != 0, "仅待审核的入库单可删除");
        // 门店权限检查
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(income.getMerchantId().longValue()), "无权操作其他门店的数据");
        // 2. 查询明细
        List<GiftIncomeDetailEntity> details = giftIncomeDetailMapper.selectListByQuery(
            QueryWrapper.create().where(GiftIncomeDetailEntity::getReceiveId).eq(id)
        );
        // 3. 查询图片
        List<GiftIncomeHasImagesEntity> images = giftIncomeHasImagesMapper.selectListByQuery(
            QueryWrapper.create().where(GiftIncomeHasImagesEntity::getIncomeId).eq(id)
        );
        List<Long> imageIds = new ArrayList<>();
        for (GiftIncomeHasImagesEntity img : images) {
            if (img.getImageId() != null) imageIds.add(img.getImageId().longValue());
        }
        // 4. 删除图片关联
        if (!images.isEmpty()) {
            giftIncomeHasImagesMapper.deleteByQuery(
                QueryWrapper.create().where(GiftIncomeHasImagesEntity::getIncomeId).eq(id)
            );
        }
        // 5. 删除明细
        if (!details.isEmpty()) {
            giftIncomeDetailMapper.deleteByQuery(
                QueryWrapper.create().where(GiftIncomeDetailEntity::getReceiveId).eq(id)
            );
        }
        // 6. 删除主表
        boolean result = this.removeById(id);
        // 7. 更新图片状态为未使用
        if (!imageIds.isEmpty()) {
            CommonUtils.batchUpdateFileStatus(imageIds, 0);
        }
        // 8. 记录操作日志
        if (result) {
            OpLogUtils.appendOpLog("删除赠品入库单", "删除赠品入库单: " + income.getIncomeCode() + ", 明细数: " + details.size(), Map.of("入库单", income, "明细数", details.size()));
        }
        return result;
    }

    @Override
    public GiftIncomeInfoVO getIncomeInfo(Long id) {
        // 1. 构建查询条件
        QueryWrapper query = QueryWrapper.create()
            .select(
                QueryMethods.column(GiftIncomeEntity::getIncomeCode),
                QueryMethods.column(GiftIncomeEntity::getMerchantId),
                QueryMethods.column(GiftIncomeEntity::getSupplierId),
                QueryMethods.column(GiftIncomeEntity::getStatus),
                QueryMethods.column(GiftIncomeEntity::getNum),
                QueryMethods.column(GiftIncomeEntity::getTotalWeight),
                QueryMethods.column(GiftIncomeEntity::getTotalCostPrice),
                QueryMethods.column(GiftIncomeEntity::getTotalTagPrice),
                QueryMethods.column(GiftIncomeEntity::getCreatedBy),
                QueryMethods.column(GiftIncomeEntity::getCreatedAt),
                QueryMethods.column(GiftIncomeEntity::getAuditBy),
                QueryMethods.column(GiftIncomeEntity::getAuditAt),
                QueryMethods.column(GiftIncomeEntity::getRemark)
            )
            .where(GiftIncomeEntity::getId).eq(id)
            .and(GiftIncomeEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
            // 门店权限限制
            .and(GiftIncomeEntity::getMerchantId).in(SecurityUtils.getMerchantIds());

        // 2. 查询入库单信息
        GiftIncomeInfoVO info = this.mapper.selectOneByQueryAs(query, GiftIncomeInfoVO.class);
        CommonUtils.abortIf(info == null, "入库单不存在");

        // 3. 填充关联数据
        ListFillUtil.of(Collections.singletonList(info))
            .build(listFillService::getMerchantNameById, Collections.singleton(info.getMerchantId()), "merchantId", "storeName")
            .build(listFillService::getSupplierNameById, Collections.singleton(info.getSupplierId()), "supplierId", "supplierName")
            .build(listFillService::getUserNameByUserId, Collections.singleton(info.getCreatedBy()), "createdBy", "creator")
            .build(listFillService::getUserNameByUserId, Collections.singleton(info.getAuditBy()), "auditBy", "auditor")
            .handle();

        // 4. 处理价格字段转换
        if (info.getTotalCostPrice() != null) {
            info.setTotalCostPrice(PriceUtil.fen2yuan(info.getTotalCostPrice()));
        }
        if (info.getTotalTagPrice() != null) {
            info.setTotalTagPrice(PriceUtil.fen2yuan(info.getTotalTagPrice()));
        }

        return info;
    }

    @Override
    public Page<GiftIncomeDetailPageVO> getIncomeDetailPage(GiftIncomeDetailPageQuery queryParams) {
        // 构建查询条件
        QueryWrapper query = QueryWrapper.create()
            .select(
                QueryMethods.column(GiftIncomeDetailEntity::getId),
                QueryMethods.column(GiftIncomeDetailEntity::getReceiveId),
                QueryMethods.column(GiftIncomeDetailEntity::getIncomeCode),
                QueryMethods.column(GiftIncomeDetailEntity::getGiftSn),
                QueryMethods.column(GiftIncomeDetailEntity::getGiftId),
                QueryMethods.column(GiftIncomeDetailEntity::getName),
                QueryMethods.column(GiftIncomeDetailEntity::getSupplierId),
                QueryMethods.column(GiftIncomeDetailEntity::getNum),
                QueryMethods.column(GiftIncomeDetailEntity::getWeight),
                QueryMethods.column(GiftIncomeDetailEntity::getCostPrice),
                QueryMethods.column(GiftIncomeDetailEntity::getTagPrice),
                QueryMethods.column(GiftIncomeDetailEntity::getStatus),
                QueryMethods.column(GiftIncomeDetailEntity::getAuditBy),
                QueryMethods.column(GiftIncomeDetailEntity::getAuditAt)
            )
            .where(GiftIncomeDetailEntity::getReceiveId).eq(queryParams.getId())
            .and(GiftIncomeDetailEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
            // 门店权限限制
            .and(GiftIncomeDetailEntity::getMerchantId).in(SecurityUtils.getMerchantIds());

        // 处理ids参数
        if (CollectionUtil.isNotEmpty(queryParams.getIds())) {
            query.in(GiftIncomeDetailEntity::getId, queryParams.getIds());
        }

        // 处理导出
        if (queryParams.getExport() != null && queryParams.getExport() == 1) {
            exportIncomeDetails(query, queryParams);
            return null;
        }

        // 处理打印
        if (queryParams.getPrint() != null && queryParams.getPrint() == 1) {
            return printIncomeDetails(query, queryParams);
        }

        // 执行分页查询
        Page<GiftIncomeDetailPageVO> page = giftIncomeDetailMapper.paginateAs(
            queryParams.getPageNum(),
            queryParams.getPageSize(),
            query,
            GiftIncomeDetailPageVO.class);

        // 填充关联数据
        if (!page.getRecords().isEmpty()) {
            fillIncomeDetailPageVOs(page.getRecords());
        }

        return page;
    }

    /**
     * 导出赠品入库单明细
     */
    private void exportIncomeDetails(QueryWrapper query, GiftIncomeDetailPageQuery queryParams) {
        ExcelUtil.of(giftIncomeDetailMapper, query, GiftIncomeDetailPageVO.class, "gift_income_detail_list", "赠品入库单明细")
            .getData((mapper, wrapper) -> {
                List<GiftIncomeDetailPageVO> voList = giftIncomeDetailMapper.selectListByQueryAs(wrapper, GiftIncomeDetailPageVO.class);
                if (!voList.isEmpty()) {
                    fillIncomeDetailPageVOs(voList);
                }
                return voList;
            })
            .doExport();
    }

    /**
     * 打印赠品入库单明细
     */
    private Page<GiftIncomeDetailPageVO> printIncomeDetails(QueryWrapper query, GiftIncomeDetailPageQuery queryParams) {
        // 检查打印数量
        CommonUtils.abortIf(
            giftIncomeDetailMapper.selectCountByQuery(query) > CommonUtils.getMaxPrintSize(),
            "打印数量超过限制");

        // 查询数据
        List<GiftIncomeDetailPageVO> voList = giftIncomeDetailMapper.selectListByQueryAs(query, GiftIncomeDetailPageVO.class);
        if (!voList.isEmpty()) {
            fillIncomeDetailPageVOs(voList);
        }

        // 构建分页结果
        Page<GiftIncomeDetailPageVO> page = new Page<>();
        page.setRecords(voList);
        page.setPageNumber(1);
        page.setPageSize(voList.size());
        page.setTotalRow(voList.size());
        return page;
    }

    /**
     * 填充赠品入库单明细关联数据
     */
    private void fillIncomeDetailPageVOs(List<GiftIncomeDetailPageVO> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        Set<Long> incomeDetailIds = new HashSet<>();
        Set<Integer> supplierIds = new HashSet<>();
        for (GiftIncomeDetailPageVO vo : records) {
            if (vo.getId() != null) {
                incomeDetailIds.add(vo.getId());
            }
            if (vo.getSupplierId() != null) {
                supplierIds.add(vo.getSupplierId());
            }
        }

        // 查询图片数据
        Map<Long, List<FileItemDTO>> imageMap = new HashMap<>();
        if (!incomeDetailIds.isEmpty()) {
            List<GiftIncomeHasImagesEntity> images = giftIncomeHasImagesMapper.selectListByQuery(
                QueryWrapper.create()
                    .where(GiftIncomeHasImagesEntity::getIncomeDetailId).in(incomeDetailIds)
                    .and(GiftIncomeHasImagesEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .orderBy(GiftIncomeHasImagesEntity::getSort, true)
            );

            // 按明细ID分组图片
            for (GiftIncomeHasImagesEntity image : images) {
                Long detailId = image.getIncomeDetailId().longValue();
                FileItemDTO fileItem = new FileItemDTO();
                fileItem.setId(image.getImageId().longValue());
                fileItem.setUrl(image.getUrl());
                imageMap.computeIfAbsent(detailId, k -> new ArrayList<>()).add(fileItem);
            }
        }

        // 填充供应商名称
        ListFillUtil.of(records)
            .build(listFillService::getSupplierNameById, supplierIds, "supplierId", "supplierName")
            .handle();

        // 填充数据
        for (GiftIncomeDetailPageVO vo : records) {
            // 价格字段分转元
            if (vo.getCostPrice() != null) {
                vo.setCostPrice(PriceUtil.fen2yuan(vo.getCostPrice()));
            }
            if (vo.getTagPrice() != null) {
                vo.setTagPrice(PriceUtil.fen2yuan(vo.getTagPrice()));
            }
            
            // 计算总价和总重量
            if (vo.getNum() != null) {
                if (vo.getCostPrice() != null) {
                    vo.setTotalCostPrice(vo.getCostPrice().multiply(new BigDecimal(vo.getNum())));
                }
                if (vo.getTagPrice() != null) {
                    vo.setTotalTagPrice(vo.getTagPrice().multiply(new BigDecimal(vo.getNum())));
                }
                if (vo.getWeight() != null) {
                    vo.setTotalWeight(vo.getWeight().multiply(new BigDecimal(vo.getNum())));
                }
            }
            
            // 填充图片
            vo.setImage(imageMap.get(vo.getId()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteIncomeDetail(Long id) {
        // 获取明细信息
        GiftIncomeDetailEntity detail = giftIncomeDetailMapper.selectOneById(id);
        CommonUtils.abortIf(detail == null, "入库单明细不存在");
        CommonUtils.abortIf(!detail.getCompanyId().equals(SecurityUtils.getCompanyId().intValue()), "无权操作其他商户的数据");
        // 门店权限检查
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(detail.getMerchantId().longValue()), "无权操作其他门店的数据");

        // 检查是否是最后一条明细
        Long detailCount = giftIncomeDetailMapper.selectCountByQuery(
            QueryWrapper.create()
                .where(GiftIncomeDetailEntity::getReceiveId).eq(detail.getReceiveId())
                .and(GiftIncomeDetailEntity::getCompanyId).eq(detail.getCompanyId())
        );
        CommonUtils.abortIf(detailCount <= 1, "当前入库单仅剩最后一条明细，请前往入库单列表操作");

        // 获取关联的图片
        List<GiftIncomeHasImagesEntity> images = giftIncomeHasImagesMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GiftIncomeHasImagesEntity::getIncomeDetailId).eq(detail.getId())
                .and(GiftIncomeHasImagesEntity::getCompanyId).eq(detail.getCompanyId())
        );

        // 更新图片状态为未使用
        if (!images.isEmpty()) {
            List<Long> imageIds = images.stream()
                .map(image -> image.getImageId().longValue())
                .collect(Collectors.toList());
            CommonUtils.batchUpdateFileStatus(imageIds, 0);
        }

        // 删除关联的图片
        giftIncomeHasImagesMapper.deleteByQuery(
            QueryWrapper.create()
                .where(GiftIncomeHasImagesEntity::getIncomeDetailId).eq(detail.getId())
                .and(GiftIncomeHasImagesEntity::getCompanyId).eq(detail.getCompanyId())
        );

        // 删除明细
        giftIncomeDetailMapper.deleteById(id);

        // 更新入库单统计信息
        GiftIncomeEntity income = mapper.selectOneById(detail.getReceiveId());
        if (income != null) {
            // 查询统计数据
            Row stats = giftIncomeDetailMapper.selectOneByQueryAs(
                QueryWrapper.create()
                    .select(
                        QueryMethods.sum(QueryMethods.column(GiftIncomeDetailEntity::getCostPrice)
                            .multiply(QueryMethods.column(GiftIncomeDetailEntity::getNum))).as("total_cost_price"),
                        QueryMethods.sum(QueryMethods.column(GiftIncomeDetailEntity::getTagPrice)
                            .multiply(QueryMethods.column(GiftIncomeDetailEntity::getNum))).as("total_tag_price")
                    )
                    .where(GiftIncomeDetailEntity::getReceiveId).eq(income.getId())
                    .and(GiftIncomeDetailEntity::getCompanyId).eq(income.getCompanyId()),
                Row.class
            );

            // 更新入库单
            income.setTotalCostPrice(stats.getLong("total_cost_price"));
            income.setTotalTagPrice(stats.getLong("total_tag_price"));
            mapper.update(income);

            // 记录操作日志
            OpLogUtils.appendOpLog(income.getIncomeCode(), "删除入库单明细", null);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateIncomeDetail(Long id, GiftIncomeDetailUpdateDTO dto) {
        // 1. 获取明细信息
        GiftIncomeDetailEntity detail = giftIncomeDetailMapper.selectOneById(id);
        CommonUtils.abortIf(detail == null, "入库单明细不存在");
        CommonUtils.abortIf(!detail.getCompanyId().equals(SecurityUtils.getCompanyId().intValue()), "无权操作其他商户的数据");
        // 门店权限检查
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(detail.getMerchantId().longValue()), "无权操作其他门店的数据");

        // 2. 获取入库单信息
        GiftIncomeEntity income = mapper.selectOneById(detail.getReceiveId());
        CommonUtils.abortIf(income == null, "入库单不存在");
        CommonUtils.abortIf(income.getStatus() != 0, "只能编辑未审核的入库单");

        // 3. 更新明细基本信息（赠品编号不可编辑）
        detail.setName(dto.getName());
        detail.setNum(dto.getNum());
        detail.setWeight(dto.getWeight());
        detail.setCostPrice(PriceUtil.yuan2fen(dto.getCostPrice()).intValue());
        detail.setTagPrice(PriceUtil.yuan2fen(dto.getTagPrice()).intValue());

        // 4. 更新明细
        boolean success = giftIncomeDetailMapper.update(detail) > 0;
        CommonUtils.abortIf(!success, "更新入库单明细失败");

        // 收集所有需要更新状态的图片ID
        Set<Long> oldImageIds = new HashSet<>();
        Set<Long> newImageIds = new HashSet<>();

        // 5. 处理图片
        if (dto.getImage() != null) {
            // 获取原有图片
            List<GiftIncomeHasImagesEntity> oldImages = giftIncomeHasImagesMapper.selectListByQuery(
                QueryWrapper.create()
                    .where(GiftIncomeHasImagesEntity::getIncomeDetailId).eq(detail.getId())
                    .and(GiftIncomeHasImagesEntity::getCompanyId).eq(detail.getCompanyId())
            );

            // 收集原有图片ID
            oldImages.forEach(img -> oldImageIds.add(img.getImageId().longValue()));

            // 收集新图片ID
            dto.getImage().forEach(img -> newImageIds.add(img.getId()));

            // 删除原有图片关联
            giftIncomeHasImagesMapper.deleteByQuery(
                QueryWrapper.create()
                    .where(GiftIncomeHasImagesEntity::getIncomeDetailId).eq(detail.getId())
                    .and(GiftIncomeHasImagesEntity::getCompanyId).eq(detail.getCompanyId())
            );

            // 添加新的图片关联
            if (!dto.getImage().isEmpty()) {
                List<GiftIncomeHasImagesEntity> images = new ArrayList<>();
                for (int i = 0; i < dto.getImage().size(); i++) {
                    FileItemDTO image = dto.getImage().get(i);
                    GiftIncomeHasImagesEntity imageEntity = new GiftIncomeHasImagesEntity();
                    imageEntity.setCompanyId(detail.getCompanyId());
                    imageEntity.setIncomeId(detail.getReceiveId());
                    imageEntity.setIncomeDetailId(detail.getId().intValue());
                    imageEntity.setImageId(image.getId().intValue());
                    imageEntity.setUrl(image.getUrl());
                    imageEntity.setSort(i + 1);
                    images.add(imageEntity);
                }
                giftIncomeHasImagesMapper.insertBatch(images);
            }
        }

        // 6. 一次性更新所有图片状态
        // 将不再使用的图片状态设为未使用
        List<Long> unusedImageIds = oldImageIds.stream()
            .filter(imageId -> !newImageIds.contains(imageId))
            .collect(Collectors.toList());
        if (!unusedImageIds.isEmpty()) {
            CommonUtils.batchUpdateFileStatus(unusedImageIds, 0);
        }
        // 将新使用的图片状态设为已使用
        List<Long> newImageIdsList = new ArrayList<>(newImageIds);
        if (!newImageIdsList.isEmpty()) {
            CommonUtils.batchUpdateFileStatus(newImageIdsList, 1);
        }

        // 7. 更新入库单统计信息
        Row stats = giftIncomeDetailMapper.selectOneByQueryAs(
            QueryWrapper.create()
                .select(
                    QueryMethods.sum(QueryMethods.column(GiftIncomeDetailEntity::getCostPrice)
                        .multiply(QueryMethods.column(GiftIncomeDetailEntity::getNum))).as("total_cost_price"),
                    QueryMethods.sum(QueryMethods.column(GiftIncomeDetailEntity::getTagPrice)
                        .multiply(QueryMethods.column(GiftIncomeDetailEntity::getNum))).as("total_tag_price")
                )
                .where(GiftIncomeDetailEntity::getReceiveId).eq(income.getId())
                .and(GiftIncomeDetailEntity::getCompanyId).eq(income.getCompanyId()),
            Row.class
        );

        // 更新入库单
        income.setTotalCostPrice(stats.getLong("total_cost_price"));
        income.setTotalTagPrice(stats.getLong("total_tag_price"));
        mapper.update(income);

        // 8. 记录操作日志
        OpLogUtils.appendOpLog(income.getIncomeCode(), "更新入库单明细", null);

        return true;
    }
} 