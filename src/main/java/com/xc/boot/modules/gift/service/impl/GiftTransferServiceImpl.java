package com.xc.boot.modules.gift.service.impl;

import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Row;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.base.IBaseEnum;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.common.util.QueryUtils;
import com.xc.boot.common.util.SnUtils;
import com.xc.boot.common.util.GiftStockUtils;
import com.xc.boot.common.util.excel.ExcelUtil;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtil;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.gift.mapper.GiftHasImagesMapper;
import com.xc.boot.modules.gift.mapper.GiftMapper;
import com.xc.boot.modules.gift.mapper.GiftTransferDetailMapper;
import com.xc.boot.modules.gift.mapper.GiftTransferMapper;
import com.xc.boot.modules.gift.model.bo.GiftStockNumChangeBO;
import com.xc.boot.modules.gift.model.dto.*;
import com.xc.boot.modules.gift.model.entity.GiftEntity;
import com.xc.boot.modules.gift.model.entity.GiftHasImagesEntity;
import com.xc.boot.modules.gift.model.entity.GiftTransferDetailEntity;
import com.xc.boot.modules.gift.model.entity.GiftTransferEntity;
import com.xc.boot.modules.gift.model.enums.GiftTransferStatusEnum;
import com.xc.boot.modules.gift.model.query.GiftTransferDetailPageQuery;
import com.xc.boot.modules.gift.model.query.GiftTransferGoodsQuery;
import com.xc.boot.modules.gift.model.query.GiftTransferPageQuery;
import com.xc.boot.modules.gift.model.vo.GiftTransferGoodsVO;
import com.xc.boot.modules.gift.model.vo.GiftTransferInfoVO;
import com.xc.boot.modules.gift.model.vo.GiftTransferPageVO;
import com.xc.boot.modules.gift.service.GiftTransferService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.mybatisflex.core.query.QueryMethods.column;

@Service
public class GiftTransferServiceImpl extends ServiceImpl<GiftTransferMapper, GiftTransferEntity> implements GiftTransferService {

    @Autowired
    private ListFillService listFillService;
    @Autowired
    private GiftMapper giftMapper;
    @Autowired
    private GiftTransferDetailMapper giftTransferDetailMapper;
    @Autowired
    private GiftHasImagesMapper giftHasImagesMapper;

    @Override
    public Page<GiftTransferPageVO> pageTransfer(GiftTransferPageQuery query) {
        QueryWrapper wrapper = QueryWrapper.create()
                .from(GiftTransferEntity.class)
                .where(GiftTransferEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .and(column(GiftTransferEntity::getMerchantOutcome).in(SecurityUtils.getMerchantIds())
                        .or(column(GiftTransferEntity::getMerchantIncome).in(SecurityUtils.getMerchantIds())))
                .and(GiftTransferEntity::getTransferSn).like(query.getTransferSn(), StrUtil.isNotBlank(query.getTransferSn()))
                .and(GiftTransferEntity::getStatus).eq(query.getStatus(), query.getStatus() != null)
                .and(GiftTransferEntity::getCreatedAt).between(
                        !CollectionUtils.isEmpty(query.getCreatedAt()) ? query.getCreatedAt().get(0) : null,
                        !CollectionUtils.isEmpty(query.getCreatedAt()) ? query.getCreatedAt().get(1) : null,
                        !CollectionUtils.isEmpty(query.getCreatedAt()) && query.getCreatedAt().size() == 2)
                .and(GiftTransferEntity::getAuditAt).between(
                        !CollectionUtils.isEmpty(query.getAuditAt()) ? query.getAuditAt().get(0) : null,
                        !CollectionUtils.isEmpty(query.getAuditAt()) ? query.getAuditAt().get(1) : null,
                        !CollectionUtils.isEmpty(query.getAuditAt()) && query.getAuditAt().size() == 2)
                .and(GiftTransferEntity::getReceiptAt).between(
                        !CollectionUtils.isEmpty(query.getReceiptAt()) ? query.getReceiptAt().get(0) : null,
                        !CollectionUtils.isEmpty(query.getReceiptAt()) ? query.getReceiptAt().get(1) : null,
                        !CollectionUtils.isEmpty(query.getReceiptAt()) && query.getReceiptAt().size() == 2)
                .orderBy(GiftTransferEntity::getId, false);

        if (StrUtil.isNotBlank(query.getMerchantOutcomeIds())) {
            wrapper.and(GiftTransferEntity::getMerchantOutcome).in(QueryUtils.parseIds(query.getMerchantOutcomeIds()));
        }
        if (StrUtil.isNotBlank(query.getMerchantIncomeIds())) {
            wrapper.and(GiftTransferEntity::getMerchantIncome).in(QueryUtils.parseIds(query.getMerchantIncomeIds()));
        }
        if (StrUtil.isNotBlank(query.getCreatedByIds())) {
            wrapper.and(GiftTransferEntity::getCreatedBy).in(QueryUtils.parseIds(query.getCreatedByIds()));
        }
        if (StrUtil.isNotBlank(query.getAuditByIds())) {
            wrapper.and(GiftTransferEntity::getAuditBy).in(QueryUtils.parseIds(query.getAuditByIds()));
        }
        if (StrUtil.isNotBlank(query.getReceiptByIds())) {
            wrapper.and(GiftTransferEntity::getReceiptBy).in(QueryUtils.parseIds(query.getReceiptByIds()));
        }

        if (query.getExport() != null && query.getExport() == 1) {
            exportTransfers(wrapper);
            return null;
        }
        if (query.getPrint() != null && query.getPrint() == 1) {
            return printTransfers(wrapper);
        }

        Page<GiftTransferEntity> page = this.page(new Page<>(query.getPageNum(), query.getPageSize()), wrapper);
        List<GiftTransferPageVO> voList = page.getRecords().stream().map(entity -> {
            GiftTransferPageVO vo = new GiftTransferPageVO();
            BeanUtils.copyProperties(entity, vo);
            vo.setStatusLabel(IBaseEnum.getLabelByValue(entity.getStatus(), GiftTransferStatusEnum.class));
            vo.setTotalCostPrice(PriceUtil.fen2yuan(entity.getTotalCostPrice()));
            vo.setTotalTagPrice(PriceUtil.fen2yuan(entity.getTotalTagPrice()));
            return vo;
        }).collect(Collectors.toList());

        fillTransferPageVOs(voList);

        Page<GiftTransferPageVO> voPage = new Page<>(page.getPageNumber(), page.getPageSize(), page.getTotalRow());
        voPage.setRecords(voList);
        return voPage;
    }

    private void exportTransfers(QueryWrapper query) {
        long count = this.mapper.selectCountByQuery(query);
        CommonUtils.abortIf(count > CommonUtils.getMaxExportSize(), "导出数量超过限制");

        ExcelUtil.of(this.mapper, query, GiftTransferPageVO.class, "transfers", "赠品调拨单列表")
                .getData((mapper, wrapper) -> {
                    List<GiftTransferPageVO> voList = mapper.selectListByQueryAs(wrapper, GiftTransferPageVO.class);
                    fillTransferPageVOs(voList);
                    return voList;
                })
                .doExport();
    }

    private Page<GiftTransferPageVO> printTransfers(QueryWrapper query) {
        long count = this.mapper.selectCountByQuery(query);
        CommonUtils.abortIf(count > CommonUtils.getMaxPrintSize(), "打印数量超过限制");

        List<GiftTransferPageVO> voList = this.mapper.selectListByQueryAs(query, GiftTransferPageVO.class);
        fillTransferPageVOs(voList);

        Page<GiftTransferPageVO> page = new Page<>(1, voList.size(), count);
        page.setRecords(voList);
        return page;
    }

    private void fillTransferPageVOs(List<GiftTransferPageVO> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }

        Set<Integer> merchantIds = voList.stream()
                .flatMap(vo -> java.util.stream.Stream.of(vo.getMerchantOutcome(), vo.getMerchantIncome()))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Set<Integer> userIds = voList.stream()
                .flatMap(vo -> java.util.stream.Stream.of(vo.getCreatedBy(), vo.getAuditBy(), vo.getReceiptBy()))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        ListFillUtil.of(voList)
                .build(listFillService::getMerchantNameById, merchantIds, "merchantOutcome", "merchantOutcomeName")
                .build(listFillService::getMerchantNameById, merchantIds, "merchantIncome", "merchantIncomeName")
                .build(listFillService::getUserNameByUserId, userIds, "createdBy", "createdByName")
                .build(listFillService::getUserNameByUserId, userIds, "auditBy", "auditByName")
                .build(listFillService::getUserNameByUserId, userIds, "receiptBy", "receiptByName")
                .handle();
    }

    @Override
    public List<GiftTransferGoodsVO> queryTransferGoods(GiftTransferGoodsQuery query) {
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(query.getMerchantOutcome().longValue()), "无权操作其他门店的数据");

        QueryWrapper wrapper = QueryWrapper.create()
                .from(GiftEntity.class)
                .where(GiftEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .and(GiftEntity::getMerchantId).eq(query.getMerchantOutcome())
                .and(GiftEntity::getStockNum).gt(0)
                .and(GiftEntity::getGiftSn).eq(query.getGiftSn(), StrUtil.isNotBlank(query.getGiftSn()));

        List<GiftEntity> giftList = giftMapper.selectListByQuery(wrapper);
        if (CollectionUtils.isEmpty(giftList)) return List.of();

        List<GiftTransferGoodsVO> voList = giftList.stream().map(entity -> {
            GiftTransferGoodsVO vo = new GiftTransferGoodsVO();
            BeanUtils.copyProperties(entity, vo);
            vo.setNum(entity.getStockNum()); // 可调拨数量为库存数量
            return vo;
        }).collect(Collectors.toList());

        fillGiftGoodsVOs(voList);
        return voList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTransfer(GiftTransferCreateDTO dto) {
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(dto.getMerchantOutcome().longValue()), "无权操作调出门店");
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(dto.getMerchantIncome().longValue()), "无权操作调入门店");

        GiftTransferEntity entity = new GiftTransferEntity();
        BeanUtils.copyProperties(dto, entity);
        entity.setCompanyId(SecurityUtils.getCompanyId().intValue());
        entity.setStatus(GiftTransferStatusEnum.PENDING.getValue());
        entity.setCreatedBy(SecurityUtils.getUserId().intValue());
        entity.setTransferSn(SnUtils.generateTransferCode());
        this.save(entity);

        Set<Long> giftIds = dto.getDetails().stream().map(GiftTransferCreateDTO.Detail::getId).collect(Collectors.toSet());
        Map<Long, GiftEntity> giftMap = giftIds.isEmpty() ? Collections.emptyMap() :
                giftMapper.selectListByIds(giftIds).stream().collect(Collectors.toMap(GiftEntity::getId, g -> g));

        List<GiftStockNumChangeBO> stockChanges = new ArrayList<>();
        List<GiftTransferDetailEntity> detailEntities = new ArrayList<>();

        for (GiftTransferCreateDTO.Detail d : dto.getDetails()) {
            GiftEntity gift = giftMap.get(d.getId());
            CommonUtils.abortIf(gift == null, "赠品不存在: " + d.getId());
            CommonUtils.abortIf(gift.getStockNum() < d.getNum(), "赠品[" + gift.getGiftSn() + "]库存不足");

            GiftTransferDetailEntity detail = new GiftTransferDetailEntity();
            detail.setCompanyId(entity.getCompanyId());
            detail.setTransferId(entity.getId().intValue());
            detail.setGiftId(d.getId().intValue());
            detail.setNum(d.getNum());
            detailEntities.add(detail);

            stockChanges.add(GiftStockNumChangeBO.builder()
                    .giftId(d.getId())
                    .stockNum(-d.getNum())
                    .frozenNum(d.getNum())
                    .comment("创建赠品调拨单: " + entity.getTransferSn())
                    .build());
        }

        if (!detailEntities.isEmpty()) {
            giftTransferDetailMapper.insertBatchSelective(detailEntities);
        }

        if (!stockChanges.isEmpty()) {
            GiftStockUtils.updateGiftStocks(stockChanges);
        }

        updateTransferStats(entity.getId());
        OpLogUtils.appendOpLog("赠品调拨单-创建", "创建调拨单: " + entity.getTransferSn(), dto);

        // 如果关闭了审核, 则继续执行审核逻辑
        if (!CommonUtils.getCompanySettings(SecurityUtils.getCompanyId()).getTransferAuditEnabled()) {
            auditTransfer(entity.getId().toString(), true);
        }

        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTransfer(GiftTransferUpdateDTO dto) {
        GiftTransferEntity entity = this.getById(dto.getId());
        CommonUtils.abortIf(entity == null, "调拨单不存在");
        CommonUtils.abortIf(!Objects.equals(entity.getStatus(), GiftTransferStatusEnum.PENDING.getValue()), "仅待审核状态的调拨单可以编辑");
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(entity.getMerchantOutcome().longValue()), "无权操作");

        entity.setRemark(dto.getRemark());
        boolean success = this.updateById(entity);

        if (success) {
            OpLogUtils.appendOpLog("赠品调拨单-更新", "更新调拨单: " + entity.getTransferSn(), dto);
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTransfer(Long id) {
        GiftTransferEntity entity = this.getById(id);
        CommonUtils.abortIf(entity == null, "调拨单不存在");
        CommonUtils.abortIf(!Objects.equals(entity.getStatus(), GiftTransferStatusEnum.PENDING.getValue()), "仅待审核状态的调拨单可以删除");
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(entity.getMerchantOutcome().longValue()), "无权操作");

        List<GiftTransferDetailEntity> details = giftTransferDetailMapper.selectListByQuery(
                QueryWrapper.create().where(GiftTransferDetailEntity::getTransferId).eq(id));

        if (!details.isEmpty()) {
            List<GiftStockNumChangeBO> stockChanges = new ArrayList<>();
            for (GiftTransferDetailEntity detail : details) {
                // 解冻库存: frozenNum -> stockNum
                stockChanges.add(GiftStockNumChangeBO.builder()
                        .giftId(detail.getGiftId().longValue())
                        .stockNum(detail.getNum())
                        .frozenNum(-detail.getNum())
                        .comment("删除赠品调拨单: " + entity.getTransferSn())
                        .build());
            }
            GiftStockUtils.updateGiftStocks(stockChanges);
        }

        giftTransferDetailMapper.deleteByQuery(QueryWrapper.create().where(GiftTransferDetailEntity::getTransferId).eq(id));
        boolean result = this.removeById(id);

        OpLogUtils.appendOpLog("赠品调拨单-删除", "删除调拨单: " + id, null);
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditTransfer(String ids) {
        return this.auditTransfer(ids, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditTransfer(String ids, boolean skipLog) {
        List<Long> idList = QueryUtils.parseIds(ids);
        CommonUtils.abortIf(CollectionUtils.isEmpty(idList), "请选择要审核的调拨单");

        List<GiftTransferEntity> transferList = this.listByIds(idList);
        for (GiftTransferEntity entity : transferList) {
            CommonUtils.abortIf(!Objects.equals(entity.getStatus(), GiftTransferStatusEnum.PENDING.getValue()), "单号 " + entity.getTransferSn() + " 不是待审核状态");
            CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(entity.getMerchantOutcome().longValue()), "无权操作单号 " + entity.getTransferSn());

            // 查询明细
            List<GiftTransferDetailEntity> details = giftTransferDetailMapper.selectListByQuery(
                    QueryWrapper.create().where(GiftTransferDetailEntity::getTransferId).eq(entity.getId()));

            // 更新原赠品库存: 扣减原始数量和冻结数量
            if (!details.isEmpty()) {
                List<GiftStockNumChangeBO> stockChanges = new ArrayList<>();
                for (GiftTransferDetailEntity detail : details) {
                    stockChanges.add(GiftStockNumChangeBO.builder()
                            .giftId(detail.getGiftId().longValue())
                            .num(-detail.getNum())
                            .frozenNum(-detail.getNum())
                            .comment("审核赠品调拨单: " + entity.getTransferSn())
                            .build());
                }
                GiftStockUtils.updateGiftStocks(stockChanges);
            }

            // 更新状态为调拨中
            entity.setStatus(GiftTransferStatusEnum.TRANSFERRING.getValue());
            entity.setAuditBy(SecurityUtils.getUserId().intValue());
            entity.setAuditAt(new Date());
            this.updateById(entity);
            
            if (!skipLog) {
                OpLogUtils.appendOpLog("赠品调拨单-审核", "审核调拨单: " + entity.getTransferSn(), null);
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean receiptTransfer(GiftTransferReceiptDTO dto) {
        GiftTransferEntity transfer = this.getById(dto.getId());
        CommonUtils.abortIf(transfer == null, "调拨单不存在");
        CommonUtils.abortIf(!Objects.equals(transfer.getStatus(), GiftTransferStatusEnum.TRANSFERRING.getValue()), "该调拨单不是调拨中状态");
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(transfer.getMerchantIncome().longValue()), "无权收货该调拨单");

        List<GiftTransferDetailEntity> details = giftTransferDetailMapper.selectListByQuery(
                QueryWrapper.create().where(GiftTransferDetailEntity::getTransferId).eq(dto.getId()));

        List<GiftStockNumChangeBO> stockChanges = new ArrayList<>();

        for (GiftTransferDetailEntity detail : details) {
            GiftEntity originalGift = giftMapper.selectOneById(detail.getGiftId());
            CommonUtils.abortIf(originalGift == null, "原赠品不存在");

            // 查找或创建目标赠品
            GiftEntity targetGift = giftMapper.selectOneByQuery(QueryWrapper.create()
                    .where(GiftEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(GiftEntity::getMerchantId).eq(transfer.getMerchantIncome())
                    .and(GiftEntity::getGiftSn).eq(originalGift.getGiftSn()));

            if (targetGift == null) {
                targetGift = createNewGiftFromTransfer(originalGift, transfer.getMerchantIncome(), detail.getNum());
                giftMapper.insert(targetGift);
                copyGiftImages(originalGift.getId(), targetGift.getId());
            } else {
                // 计划增加目标赠品库存
                stockChanges.add(GiftStockNumChangeBO.builder()
                        .giftId(targetGift.getId())
                        .num(detail.getNum())
                        .stockNum(detail.getNum())
                        .comment("赠品调拨收货(入库): " + transfer.getTransferSn())
                        .build());
            }
            detail.setTargetGiftId(targetGift.getId().intValue());
            giftTransferDetailMapper.update(detail);
        }

        if (!stockChanges.isEmpty()) {
            GiftStockUtils.updateGiftStocks(stockChanges);
        }

        transfer.setStatus(GiftTransferStatusEnum.COMPLETED.getValue());
        transfer.setReceiptBy(SecurityUtils.getUserId().intValue());
        transfer.setReceiptAt(new Date());
        transfer.setReceiptRemark(dto.getReceiptRemark());
        this.updateById(transfer);

        OpLogUtils.appendOpLog("赠品调拨单-收货", "收货调拨单: " + transfer.getTransferSn(), dto);
        return true;
    }
    
    private GiftEntity createNewGiftFromTransfer(GiftEntity originalGift, Integer merchantId, Integer num) {
        GiftEntity newGift = new GiftEntity();
        BeanUtils.copyProperties(originalGift, newGift, "id", "companyId", "merchantId", "num", "stockNum", "soldNum", "transferNum", "frozenNum");
        newGift.setCompanyId(SecurityUtils.getCompanyId().intValue());
        newGift.setMerchantId(merchantId);
        newGift.setNum(num);
        newGift.setStockNum(num);
        newGift.setSoldNum(0);
        newGift.setTransferNum(0);
        newGift.setFrozenNum(0);
        return newGift;
    }
    
    private void copyGiftImages(Long sourceGiftId, Long targetGiftId) {
        List<GiftHasImagesEntity> sourceImages = giftHasImagesMapper.selectListByQuery(
            QueryWrapper.create().where(GiftHasImagesEntity::getGiftId).eq(sourceGiftId)
        );
        
        if (!CollectionUtils.isEmpty(sourceImages)) {
            List<GiftHasImagesEntity> targetImages = sourceImages.stream()
                .map(image -> {
                    GiftHasImagesEntity newImage = new GiftHasImagesEntity();
                    newImage.setCompanyId(SecurityUtils.getCompanyId().intValue());
                    newImage.setGiftId(targetGiftId.intValue());
                    newImage.setImageId(image.getImageId());
                    newImage.setUrl(image.getUrl());
                    newImage.setSort(image.getSort());
                    return newImage;
                })
                .collect(Collectors.toList());
            
            if (!targetImages.isEmpty()) {
                giftHasImagesMapper.insertBatch(targetImages);
            }
        }
    }

    @Override
    public GiftTransferInfoVO getTransferInfo(GiftTransferInfoRequest request) {
        GiftTransferEntity entity = this.getById(request.getId());
        CommonUtils.abortIf(entity == null, "调拨单不存在");
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(entity.getMerchantOutcome().longValue()) && !SecurityUtils.getMerchantIds().contains(entity.getMerchantIncome().longValue()), "无权查看");

        GiftTransferInfoVO vo = new GiftTransferInfoVO();
        BeanUtils.copyProperties(entity, vo);
        vo.setStatusLabel(IBaseEnum.getLabelByValue(entity.getStatus(), GiftTransferStatusEnum.class));
        
        Set<Integer> merchantIds = Set.of(entity.getMerchantOutcome(), entity.getMerchantIncome());
        Set<Integer> userIds = Set.of(entity.getCreatedBy(), entity.getAuditBy(), entity.getReceiptBy());

        Map<String, String> merchantNameMap = listFillService.getMerchantNameById(merchantIds);
        Map<String, String> userNameMap = listFillService.getUserNameByUserId(userIds);

        vo.setMerchantOutcomeName(merchantNameMap.get(String.valueOf(entity.getMerchantOutcome())));
        vo.setMerchantIncomeName(merchantNameMap.get(String.valueOf(entity.getMerchantIncome())));
        vo.setCreatedByName(userNameMap.get(String.valueOf(entity.getCreatedBy())));
        vo.setAuditByName(userNameMap.get(String.valueOf(entity.getAuditBy())));
        vo.setReceiptByName(userNameMap.get(String.valueOf(entity.getReceiptBy())));
        
        return vo;
    }
    
    @Override
    public Page<GiftTransferGoodsVO> pageTransferDetail(GiftTransferDetailPageQuery query) {
        GiftTransferEntity transfer = this.getById(query.getTransferId());
        CommonUtils.abortIf(transfer == null, "调拨单不存在");
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(transfer.getMerchantOutcome().longValue()) && !SecurityUtils.getMerchantIds().contains(transfer.getMerchantIncome().longValue()), "无权查看");

        QueryWrapper wrapper = QueryWrapper.create()
                .from(GiftTransferDetailEntity.class)
                .where(GiftTransferDetailEntity::getTransferId).eq(query.getTransferId());
        
        if (query.getExport() != null && query.getExport() == 1) {
            exportTransferDetails(wrapper);
            return null;
        }
        if (query.getPrint() != null && query.getPrint() == 1) {
            return printTransferDetails(wrapper);
        }

        Page<GiftTransferDetailEntity> page = giftTransferDetailMapper.paginate(
                new Page<>(query.getPageNum(), query.getPageSize()), wrapper);
        
        List<GiftTransferGoodsVO> voList = getGiftTransferGoodsVOS(page.getRecords());
        
        Page<GiftTransferGoodsVO> voPage = new Page<>(page.getPageNumber(), page.getPageSize(), page.getTotalRow());
        voPage.setRecords(voList);
        return voPage;
    }

    private void exportTransferDetails(QueryWrapper wrapper) {
        long count = giftTransferDetailMapper.selectCountByQuery(wrapper);
        CommonUtils.abortIf(count > CommonUtils.getMaxExportSize(), "导出数量超过限制");

        ExcelUtil.of(giftTransferDetailMapper, wrapper, GiftTransferGoodsVO.class, "transfer_detail", "赠品调拨单明细")
                .getData((mapper, w) -> {
                    List<GiftTransferDetailEntity> details = giftTransferDetailMapper.selectListByQuery(w);
                    return getGiftTransferGoodsVOS(details);
                })
                .doExport();
    }
    
    private Page<GiftTransferGoodsVO> printTransferDetails(QueryWrapper wrapper) {
        long count = giftTransferDetailMapper.selectCountByQuery(wrapper);
        CommonUtils.abortIf(count > CommonUtils.getMaxPrintSize(), "打印数量超过限制");
        List<GiftTransferDetailEntity> details = giftTransferDetailMapper.selectListByQuery(wrapper);
        List<GiftTransferGoodsVO> voList = getGiftTransferGoodsVOS(details);
        Page<GiftTransferGoodsVO> page = new Page<>(1, voList.size(), count);
        page.setRecords(voList);
        return page;
    }

    private List<GiftTransferGoodsVO> getGiftTransferGoodsVOS(List<GiftTransferDetailEntity> details) {
        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }
        
        Set<Integer> giftIds = details.stream().map(GiftTransferDetailEntity::getGiftId).collect(Collectors.toSet());
        Map<Long, GiftEntity> giftMap = giftMapper.selectListByIds(giftIds).stream().collect(Collectors.toMap(GiftEntity::getId, g -> g));

        List<GiftTransferGoodsVO> voList = details.stream().map(detail -> {
            GiftEntity gift = giftMap.get(detail.getGiftId().longValue());
            if (gift == null) return null;
            GiftTransferGoodsVO vo = new GiftTransferGoodsVO();
            BeanUtils.copyProperties(gift, vo);
            vo.setId(gift.getId());
            vo.setNum(detail.getNum());
            vo.setCostPrice(PriceUtil.fen2yuan(gift.getCostPrice()));
            vo.setTagPrice(PriceUtil.fen2yuan(gift.getTagPrice()));
            
            if (vo.getWeight() != null && vo.getNum() != null) {
                vo.setTotalWeight(vo.getWeight().multiply(BigDecimal.valueOf(vo.getNum())));
            }
            if (vo.getCostPrice() != null && vo.getNum() != null) {
                vo.setTotalCostPrice(vo.getCostPrice().multiply(BigDecimal.valueOf(vo.getNum())));
            }
            if (vo.getTagPrice() != null && vo.getNum() != null) {
                vo.setTotalTagPrice(vo.getTagPrice().multiply(BigDecimal.valueOf(vo.getNum())));
            }
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        fillGiftGoodsVOs(voList);
        return voList;
    }

    private void fillGiftGoodsVOs(List<GiftTransferGoodsVO> voList) {
        if (CollectionUtils.isEmpty(voList)) return;
        
        Set<Long> giftIds = voList.stream().map(GiftTransferGoodsVO::getId).collect(Collectors.toSet());
        Map<String, List<GiftHasImagesEntity>> imagesMap = listFillService.getGiftImgByGiftId(giftIds);

        if (imagesMap.isEmpty()) return;

        for (GiftTransferGoodsVO vo : voList) {
            List<GiftHasImagesEntity> images = imagesMap.get(String.valueOf(vo.getId()));
            if (!CollectionUtils.isEmpty(images)) {
                vo.setImages(images.stream().map(GiftHasImagesEntity::getUrl).collect(Collectors.toList()));
            }
        }
    }
    
    private void updateTransferStats(Long transferId) {
        if (transferId == null) return;
        List<GiftTransferDetailEntity> details = giftTransferDetailMapper.selectListByQuery(
                QueryWrapper.create().where(GiftTransferDetailEntity::getTransferId).eq(transferId));

        if (CollectionUtils.isEmpty(details)) {
            this.removeById(transferId); // 如果没有明细了，直接删除主单
            return;
        }

        Set<Integer> giftIds = details.stream().map(GiftTransferDetailEntity::getGiftId).collect(Collectors.toSet());
        Map<Long, GiftEntity> giftMap = giftMapper.selectListByIds(giftIds).stream().collect(Collectors.toMap(GiftEntity::getId, g -> g));

        int totalNum = 0;
        int totalCostPrice = 0;
        int totalTagPrice = 0;

        for (GiftTransferDetailEntity detail : details) {
            GiftEntity gift = giftMap.get(detail.getGiftId().longValue());
            if (gift != null) {
                totalNum += detail.getNum();
                if (gift.getCostPrice() != null) {
                    totalCostPrice += gift.getCostPrice() * detail.getNum();
                }
                if (gift.getTagPrice() != null) {
                    totalTagPrice += gift.getTagPrice() * detail.getNum();
                }
            }
        }
        
        GiftTransferEntity transfer = new GiftTransferEntity();
        transfer.setId(transferId);
        transfer.setNum(totalNum);
        transfer.setTotalCostPrice(totalCostPrice);
        transfer.setTotalTagPrice(totalTagPrice);
        this.updateById(transfer);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTransferDetail(GiftTransferDetailUpdateDTO dto) {
        GiftTransferDetailEntity detail = giftTransferDetailMapper.selectOneById(dto.getId());
        CommonUtils.abortIf(detail == null, "明细不存在");
        GiftTransferEntity transfer = this.getById(detail.getTransferId().longValue());
        CommonUtils.abortIf(transfer == null, "调拨单不存在");
        CommonUtils.abortIf(!Objects.equals(transfer.getStatus(), GiftTransferStatusEnum.PENDING.getValue()), "仅待审核状态的调拨单可以编辑");
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(transfer.getMerchantOutcome().longValue()), "无权操作");

        GiftEntity gift = giftMapper.selectOneById(detail.getGiftId());
        CommonUtils.abortIf(gift == null, "赠品不存在");

        int quantityChange = dto.getNum() - detail.getNum();
        CommonUtils.abortIf(gift.getStockNum() - quantityChange < 0, "库存不足");

        // 更新冻结库存
        GiftStockNumChangeBO stockChange = GiftStockNumChangeBO.builder()
                .giftId(gift.getId())
                .stockNum(-quantityChange)
                .frozenNum(quantityChange)
                .comment("更新赠品调拨单明细: " + transfer.getTransferSn())
                .build();
        GiftStockUtils.updateGiftStocks(List.of(stockChange));

        detail.setNum(dto.getNum());
        giftTransferDetailMapper.update(detail);

        updateTransferStats(transfer.getId());
        OpLogUtils.appendOpLog("赠品调拨单明细-编辑", "编辑明细: " + detail.getId(), dto);
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTransferDetail(Long id) {
        GiftTransferDetailEntity detail = giftTransferDetailMapper.selectOneById(id);
        CommonUtils.abortIf(detail == null, "明细不存在");
        GiftTransferEntity transfer = this.getById(detail.getTransferId().longValue());
        CommonUtils.abortIf(transfer == null, "调拨单不存在");
        CommonUtils.abortIf(!Objects.equals(transfer.getStatus(), GiftTransferStatusEnum.PENDING.getValue()), "仅待审核状态的调拨单可以删除");
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(transfer.getMerchantOutcome().longValue()), "无权操作");

        // 检查是否是最后一条明细
        Long detailCount = giftTransferDetailMapper.selectCountByQuery(
                QueryWrapper.create().where(GiftTransferDetailEntity::getTransferId).eq(detail.getTransferId()));
        CommonUtils.abortIf(detailCount <= 1, "当前调拨单仅剩最后一条明细，请直接删除主单");

        // 恢复冻结库存
        GiftStockNumChangeBO stockChange = GiftStockNumChangeBO.builder()
                .giftId(detail.getGiftId().longValue())
                .stockNum(detail.getNum())
                .frozenNum(-detail.getNum())
                .comment("删除赠品调拨单明细: " + transfer.getTransferSn())
                .build();
        GiftStockUtils.updateGiftStocks(List.of(stockChange));

        giftTransferDetailMapper.deleteById(id);
        updateTransferStats(transfer.getId());

        OpLogUtils.appendOpLog("赠品调拨单明细-删除", "删除明细: " + id, id);
        return true;
    }
} 