package com.xc.boot.modules.goods.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.goods.model.query.GoodsTransferPageQuery;
import com.xc.boot.modules.goods.model.query.GoodsTransferGoodsQuery;
import com.xc.boot.modules.goods.model.query.GoodsTransferCreateDTO;
import com.xc.boot.modules.goods.model.vo.GoodsTransferPageVO;
import com.xc.boot.modules.goods.model.vo.GoodsTransferGoodsVO;
import com.xc.boot.modules.goods.service.GoodsTransferService;
import com.xc.boot.modules.goods.model.dto.GoodsTransferReceiptDTO;
import com.xc.boot.modules.goods.model.dto.GoodsTransferUpdateDTO;
import com.xc.boot.modules.goods.model.dto.GoodsTransferInfoRequest;
import com.xc.boot.modules.goods.model.vo.GoodsTransferInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.xc.boot.common.util.ColumnEncryptUtil;
import cn.hutool.json.JSONObject;
import com.xc.boot.common.base.DeleteRequest;
import jakarta.validation.Valid;
import com.xc.boot.common.base.IdsRequest;
import com.xc.boot.modules.goods.model.query.GoodsTransferDetailPageQuery;
import com.xc.boot.modules.goods.model.dto.GoodsTransferDetailUpdateDTO;

import java.util.List;

@RestController
@RequiredArgsConstructor
@Tag(name = "货品管理-调拨单管理")
@RequestMapping("/api/goods/transfer")
public class GoodsTransferController {
    private final GoodsTransferService goodsTransferService;

    @Operation(summary = "调拨单分页列表")
    @PostMapping("/page")
    public PageResult<GoodsTransferPageVO> page(@Validated @RequestBody GoodsTransferPageQuery query) {
        Page<GoodsTransferPageVO> page = goodsTransferService.pageTransfer(query);
        return PageResult.success(page);
    }

    @Operation(summary = "调拨货品查询")
    @PostMapping("/goods/query")
    public Result<List<JSONObject>> queryTransferGoods(@Validated @RequestBody GoodsTransferGoodsQuery query) {
        List<GoodsTransferGoodsVO> list = goodsTransferService.queryTransferGoods(query);
        List<JSONObject> result = ColumnEncryptUtil.encrypt(list, GoodsTransferGoodsVO.class, "customerColumns");
        return Result.success(result);
    }

    @Operation(summary = "创建调拨单")
    @PostMapping("/create")
    public Result<Long> createTransfer(@Validated @RequestBody GoodsTransferCreateDTO dto) {
        Long id = goodsTransferService.createTransfer(dto);
        return Result.success(id);
    }

    @Operation(summary = "更新调拨单")
    @PutMapping("/update")
    public Result<Boolean> updateTransfer(@Validated @RequestBody GoodsTransferUpdateDTO dto) {
        boolean result = goodsTransferService.updateTransfer(dto);
        return Result.judge(result);
    }

    @Operation(summary = "删除调拨单")
    @DeleteMapping
    public Result<Boolean> deleteTransfer(@RequestBody @Valid DeleteRequest request) {
        boolean result = goodsTransferService.deleteTransfer(request.getId());
        return Result.judge(result);
    }

    @Operation(summary = "审核调拨单")
    @PostMapping("/audit")
    public Result<Boolean> auditTransfer(@RequestBody IdsRequest request) {
        boolean result = goodsTransferService.auditTransfer(request.getIds());
        return Result.judge(result);
    }

    @Operation(summary = "调拨单收货")
    @PostMapping("/receipt")
    public Result<Boolean> receiptTransfer(@Validated @RequestBody GoodsTransferReceiptDTO dto) {
        boolean result = goodsTransferService.receiptTransfer(dto);
        return Result.judge(result);
    }

    @Operation(summary = "获取调拨单基础信息")
    @PostMapping("/info")
    public Result<?> getTransferInfo(@Validated @RequestBody GoodsTransferInfoRequest request) {
        GoodsTransferInfoVO vo = goodsTransferService.getTransferInfo(request);
        List<JSONObject> result = ColumnEncryptUtil.encrypt(List.of(vo), GoodsTransferInfoVO.class, null);
        return Result.success(result.get(0));
    }

    @Operation(summary = "调拨单明细分页列表")
    @PostMapping("/detail/page")
    public PageResult<?> pageTransferDetail(@Validated @RequestBody GoodsTransferDetailPageQuery query) {
        Page<GoodsTransferGoodsVO> page = goodsTransferService.pageTransferDetail(query);
        if (page == null) return PageResult.success(null);
        Page<JSONObject> result = ColumnEncryptUtil.encrypt(page, GoodsTransferGoodsVO.class, null);
        return PageResult.success(result);
    }

    @Operation(summary = "编辑调拨单明细")
    @PutMapping("/detail/update")
    public Result<Boolean> updateTransferDetail(@Validated @RequestBody GoodsTransferDetailUpdateDTO dto) {
        boolean result = goodsTransferService.updateTransferDetail(dto);
        return Result.judge(result);
    }

    @Operation(summary = "删除调拨单明细")
    @PostMapping("/detail/delete")
    public Result<Boolean> deleteTransferDetail(@Validated @RequestBody DeleteRequest request) {
        boolean result = goodsTransferService.deleteTransferDetail(request.getId());
        return Result.judge(result);
    }
} 