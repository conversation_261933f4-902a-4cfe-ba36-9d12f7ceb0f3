package com.xc.boot.modules.goods.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import com.xc.boot.common.listener.CreatedByListenerFlag;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 货品出库实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "goods_outcome")
public class GoodsOutcomeEntity extends BaseEntity implements CreatedByListenerFlag {
    
    /**
     * 出库单号
     */
    @Column(value = "outcome_code")
    private String outcomeCode;

    /**
     * 所属商户ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 所属门店ID
     */
    @Column(value = "merchant_id")
    private Long merchantId;

    /**
     * 供应商ID
     */
    @Column(value = "supplier_id")
    private Long supplierId;

    /**
     * 出库数量
     */
    @Column(value = "num")
    private Integer num;

    /**
     * 总重量(g)
     */
    @Column(value = "total_weight")
    private BigDecimal totalWeight;

    /**
     * 总金重(g)
     */
    @Column(value = "total_net_gold_weight")
    private BigDecimal totalNetGoldWeight;

    /**
     * 总银重(g)
     */
    @Column(value = "total_net_silver_weight")
    private BigDecimal totalNetSilverWeight;

    /**
     * 总成本价(分)
     */
    @Column(value = "total_cost_price")
    private Long totalCostPrice;

    /**
     * 备注
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 状态(0:待审核,1:已审核)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 审核人ID
     */
    @Column(value = "audit_by")
    private Long auditBy;

    /**
     * 审核时间
     */
    @Column(value = "audit_at")
    private Date auditAt;

    /**
     * 创建人ID
     */
    @Column(value = "created_by")
    private Long createdBy;
} 