package com.xc.boot.modules.goods.model.form;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.CustomColumnItemDTO;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "货品修改表单")
@Accessors(chain = true)
@Table(value = "goods")
public class GoodsUpdateForm {

    @Column(value = "id")
    @NotNull(message = "货品ID不能为空")
    private Long id;

    /**
     * 所属柜台ID
     */
    @Column(value = "counter_id")
    private String counter_id;

    /**
     * 供应商ID
     */
    @Column(value = "supplier_id")
    private String supplier_id;

    /**
     * 所属大类ID
     */
    @Column(value = "category_id")
    private Long category_id;

    /**
     * 所属小类ID
     */
    @Column(value = "subclass_id")
    private String subclass_id;

    /**
     * 品牌ID
     */
    @Column(value = "brand_id")
    private String brand_id;

    /**
     * 款式ID
     */
    @Column(value = "style_id")
    private String style_id;

    /**
     * 成色ID
     */
    @Column(value = "quality_id")
    private String quality_id;

    /**
     * 工艺ID
     */
    @Column(value = "technology_id")
    private String technology_id;

    /**
     * 主石ID
     */
    @Column(value = "main_stone_id")
    private String main_stone_id;

    /**
     * 辅石ID
     */
    @Column(value = "sub_stone_id")
    private String sub_stone_id;

    /**
     * 货品条码
     */
    @Column(value = "goods_sn")
    private String goods_sn;

    /**
     * 货品名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 销售方式(1:按重量,2:按数量)
     */
    @Column(value = "sales_type")
    private String sales_type;

    /**
     * 批次号
     */
    @Column(value = "batch_no")
    private String batch_no;

    /**
     * 证书号
     */
    @Column(value = "cert_no")
    private String cert_no;

    /**
     * 备注
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 重量(g)
     */
    @Column(value = "weight")
    private String weight;

    /**
     * 净金重(g)
     */
    @Column(value = "net_gold_weight")
    private String net_gold_weight;

    /**
     * 净银重(g)
     */
    @Column(value = "net_silver_weight")
    private String net_silver_weight;

    /**
     * 主石数
     */
    @Column(value = "main_stone_count")
    private String main_stone_count;

    /**
     * 主石重(ct)
     */
    @Column(value = "main_stone_weight")
    private String main_stone_weight;

    /**
     * 辅石数
     */
    @Column(value = "sub_stone_count")
    private String sub_stone_count;

    /**
     * 辅石重(ct)
     */
    @Column(value = "sub_stone_weight")
    private String sub_stone_weight;

    /**
     * 圈口
     */
    @Column(value = "circle_size")
    private String circle_size;

    /**
     * 成本单价(分)
     */
    @Column(value = "cost_price")
    private String cost_price;

    /**
     * 金进单价(分)
     */
    @Column(value = "gold_price")
    private String gold_price;

    /**
     * 银进单价(分)
     */
    @Column(value = "silver_price")
    private String silver_price;

    /**
     * 进工费单价(分)
     */
    @Column(value = "work_price")
    private String work_price;

    /**
     * 证书费(分)
     */
    @Column(value = "cert_price")
    private String cert_price;

    /**
     * 工费单价(分)
     */
    @Column(value = "sale_work_price")
    private String sale_work_price;

    /**
     * 标签单价(分)
     */
    @Column(value = "tag_price")
    private String tag_price;

    /**
     * 原始数量
     */
    @Column(value = "num")
    private String num;

    /**
     * 库存数量
     */
    @Column(value = "stock_num")
    private String stock_num;

    /**
     * 采购退数量
     */
    @Column(value = "return_num")
    private String return_num;

    /**
     * 售出数量
     */
    @Column(value = "sold_num")
    private String sold_num;

    /**
     * 调拨中数量
     */
    @Column(value = "transfer_num")
    private String transfer_num;

    /**
     * 冻结数量
     */
    @Column(value = "frozen_num")
    private String frozen_num;

    @Schema(description = "自定义字段")
    private List<CustomColumnItemDTO> customerColumns;

    @Schema(description = "商品图片列表")
    private List<GoodsHasImagesEntity> images;

}
