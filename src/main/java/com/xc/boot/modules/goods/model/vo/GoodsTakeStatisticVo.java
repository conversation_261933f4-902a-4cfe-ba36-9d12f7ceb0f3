package com.xc.boot.modules.goods.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Schema(description = "盘点单统计vo")
@Data
public class GoodsTakeStatisticVo {
    @Schema(description = "货品总数")
    private Integer totalNum = 0;
    @Schema(description = "总重量(g)")
    private BigDecimal totalWeight = BigDecimal.ZERO.setScale(3, RoundingMode.HALF_UP);
    @Schema(description = "总金重(g)")
    private BigDecimal totalGoldWeight = BigDecimal.ZERO.setScale(3, RoundingMode.HALF_UP);
    @Schema(description = "总银重(g)")
    private BigDecimal totalSilverWeight = BigDecimal.ZERO.setScale(3, RoundingMode.HALF_UP);
    @Schema(description = "总成本价(元)")
    private String totalCostPrice = "0.00";
}
