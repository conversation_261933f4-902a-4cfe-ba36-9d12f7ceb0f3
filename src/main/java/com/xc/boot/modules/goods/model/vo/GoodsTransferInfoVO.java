package com.xc.boot.modules.goods.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

import com.xc.boot.common.annotation.GoodsColumn;

@Data
@Schema(description = "调拨单基础信息VO")
public class GoodsTransferInfoVO {
    @Schema(description = "调拨单号")
    private String transferSn;

    @Schema(description = "调拨类型")
    private String typeLabel;

    @Schema(description = "调出门店")
    private String merchantOutcomeName;

    @Schema(description = "状态")
    private String statusLabel;

    @Schema(description = "调入门店")
    private String merchantIncomeName;

    @Schema(description = "调入柜台")
    private String counterIncomeName;

    @Schema(description = "总数量")
    private Integer num;

    @Schema(description = "总重量(g)")
    @GoodsColumn(value = "weight")
    private BigDecimal totalWeight;

    @Schema(description = "总金重(g)")
    @GoodsColumn(value = "net_gold_weight")
    private BigDecimal totalNetGoldWeight;

    @Schema(description = "总银重(g)")
    @GoodsColumn(value = "net_silver_weight")
    private BigDecimal totalNetSilverWeight;

    @Schema(description = "总成本价")
    @GoodsColumn(value = "cost_price")
    private BigDecimal totalCostPrice;

    @Schema(description = "创建人")
    private String createdByName;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "备注")
    private String remark;
} 