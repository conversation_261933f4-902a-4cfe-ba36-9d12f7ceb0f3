package com.xc.boot.modules.goods.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.enums.JoinColumEnum;
import com.xc.boot.common.enums.NumColumEnum;
import com.xc.boot.common.enums.PriceColumEnum;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.model.Option;
import com.xc.boot.common.util.ColumnEncryptUtil;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.common.util.excel.ExcelUtil;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtil;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsHasColumnsMapper;
import com.xc.boot.modules.goods.mapper.GoodsHasImagesMapper;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.model.bo.CustomColumnBO;
import com.xc.boot.modules.goods.model.bo.GoodsPriceChangeBO;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.goods.model.entity.GoodsHasColumnsEntity;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import com.xc.boot.modules.goods.model.query.GoodsPageQuery;
import com.xc.boot.modules.goods.model.query.GoodsPrintQuery;
import com.xc.boot.modules.goods.model.vo.*;
import com.xc.boot.modules.goods.service.GoodsService;
import com.xc.boot.modules.income.mapper.GoodsIncomeDetailMapper;
import com.xc.boot.modules.merchant.mapper.GoodsColumnMapper;
import com.xc.boot.modules.merchant.mapper.GoodsIncomeTemplateDetailMapper;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateDetailEntity;
import com.xc.boot.system.mapper.GoodsLogMapper;
import com.xc.boot.system.model.entity.GoodsLogEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.mybatisflex.core.query.QueryMethods.sum;
import static com.xc.boot.common.util.OpLogUtils.*;
import static com.xc.boot.modules.goods.model.entity.table.GoodsHasColumnsTableDef.GOODS_HAS_COLUMNS;
import static com.xc.boot.modules.goods.model.entity.table.GoodsTableDef.GOODS;
import static com.xc.boot.modules.income.model.entity.table.GoodsIncomeDetailTableDef.GOODS_INCOME_DETAIL;
import static com.xc.boot.modules.merchant.model.entity.table.GoodsColumnTableDef.GOODS_COLUMN;
import static com.xc.boot.modules.merchant.model.entity.table.GoodsIncomeTemplateDetailTableDef.GOODS_INCOME_TEMPLATE_DETAIL;
import static com.xc.boot.modules.merchant.model.entity.table.GoodsIncomeTemplateTableDef.GOODS_INCOME_TEMPLATE;

/**
 * 货品服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsServiceImpl extends ServiceImpl<GoodsMapper, GoodsEntity> implements GoodsService {
    private final ListFillService fillService;
    private final GoodsHasImagesMapper goodsHasImagesMapper;
    private final GoodsHasColumnsMapper goodsHasColumnsMapper;
    private final GoodsIncomeTemplateDetailMapper goodsIncomeTemplateDetailMapper;
    private final GoodsColumnMapper goodsColumnMapper;
    private final GoodsLogMapper goodsLogMapper;
    private final GoodsIncomeDetailMapper incomeDetailMapper;

    @Override
    public Page<GoodsPageVo> goodsPage(GoodsPageQuery query) {
        QueryWrapper wrapper = buildWrapper(query);
        wrapper.orderBy(GOODS.ID, false);

        // 导出
        if (query.getExport().equals(1)) {
            doExport(wrapper);
            return new Page<>();
        }

        int pageNum = query.getPageNum();
        int pageSize = query.getPageSize();
        // 打印
        if (query.getPrint().equals(1)) {
            long count = this.count(wrapper);
            Assert.isTrue(count <= CommonUtils.getMaxPrintSize(), String.format("打印数据条数超出最大限制%d条", CommonUtils.getMaxPrintSize()));
            pageNum = 1;
            pageSize = CommonUtils.getMaxPrintSize();
        }
        Page<GoodsPageVo> page = this.mapper.paginateAs(pageNum, pageSize, wrapper, GoodsPageVo.class);
        List<GoodsPageVo> records = page.getRecords();
        if (records.isEmpty()) {
            return page;
        }
        this.fillList(records);
        return page;
    }

    @Override
    @Transactional
    public void goodsUpdate(JSONObject form) {
        Long id = form.getLong("id");
        Asserts.notNull(id, "id不能为空");
        Long companyId = SecurityUtils.getCompanyId();
        GoodsEntity goods = this.getById(id);
        Assert.notNull(goods, "货品不存在");
        Assert.isTrue(goods.getCompanyId().equals(companyId), "无权操作该货品");
        Assert.isTrue(SecurityUtils.getMerchantIds().contains(goods.getMerchantId()) || SecurityUtils.isMain(), "无权操作该货品");
        // 查询用户可编辑字段
        List<CustomColumnBO> goodsDefaultColumn = getGoodsDefaultColumn(companyId, Long.valueOf(goods.getCategoryId()), goods.getId());
        Map<String, Field> goodsFiledMap = ColumnEncryptUtil.getCloumnFieldMap(GoodsEntity.class);
        // 日志
        StringBuilder logString = new StringBuilder();
        // 价格变更日志
        GoodsPriceChangeBO priceChangeBO = null;

        // 字段处理
        for (CustomColumnBO column : goodsDefaultColumn) {
            // 不能编辑该字段
            if (!column.getSecretLevel().equals(1) && !SecurityUtils.isMain() && !SecurityUtils.showSecret()) {
                continue;
            }
            if (column.getCategory().equals(2) && column.getType().equals(5)) {
                form.set(column.getSign(), form.getStr(column.getSign() + "_id"));
            }
            // form没有传这个字段
            if (!form.containsKey(column.getSign())) {
                continue;
            }
            // 自定义字段处理
            if (column.getCategory().equals(2)) {
                customerColumnHandle(column, form, goods, logString);
                continue;
            }
            // 基础字段处理
            String value = form.getStr(column.getSign());
            Field field = goodsFiledMap.get(column.getSign());
            Object oldValue = ReflectUtil.getFieldValue(goods, field);
            if (oldValue == null) {
                oldValue = "";
            }
            // image处理
            if (column.getSign().equals("image")) {
                // 删除旧关联
                List<GoodsHasImagesEntity> oldImages = fillService.getGoodsImgByGoodsId(Set.of(goods.getId())).getOrDefault(goods.getId().toString(), Collections.emptyList());
                Set<String> oldUrlSet = oldImages.stream().map(GoodsHasImagesEntity::getUrl).collect(Collectors.toSet());
                List<Long> oldIds = oldImages.stream().map(GoodsHasImagesEntity::getId).toList();
                if (CollectionUtil.isNotEmpty(oldIds)) {
                    goodsHasImagesMapper.deleteBatchByIds(oldIds);
                    CommonUtils.batchUpdateFileStatus(oldIds, 0);
                }
                // 处理新图片
                List<GoodsHasImagesEntity> newImages = form.getBeanList("image", GoodsHasImagesEntity.class);
                if (newImages == null) {
                    newImages = new ArrayList<>();
                }
                int sort = 1;
                for (GoodsHasImagesEntity newImage : newImages) {
                    if (newImage.getImageId() == null) {
                        newImage.setImageId(newImage.getId());
                    }
                    newImage.setId(null);
                    newImage.setGoodsId(goods.getId());
                    newImage.setCompanyId(companyId);
                    newImage.setSort(sort++);
                }
                if (CollectionUtil.isNotEmpty(newImages)) {
                    goodsHasImagesMapper.insertBatch(newImages);
                    List<Long> newIds = newImages.stream().map(GoodsHasImagesEntity::getImageId).toList();
                    CommonUtils.batchUpdateFileStatus(newIds, 1);
                }
                List<String> newUrls = newImages.stream().map(GoodsHasImagesEntity::getUrl).toList();
                boolean needLog = oldUrlSet.size() != newUrls.size();
                for (String newUrl : newUrls) {
                    if (!oldUrlSet.contains(newUrl)) {
                        needLog = true;
                        break;
                    }
                }
                // 记录日志
                if (needLog) {
                    logString.append("图片").append(COLON).append(String.join(",", oldUrlSet)).append(MODIFY_STRING).append(String.join(",", newUrls)).append(NEW_LINE);
                }
                continue;
            }

            // 如果是价格字段， 元 转 分
            if (PriceColumEnum.isPriceColumn(column.getSign())) {
                value = PriceUtil.yuan2fen(value).toPlainString();
            }

            // 如果新老值相同 不做修改
            try {
                // 尝试比较双方是否是数字类型
                BigDecimal old = new BigDecimal(oldValue.toString());
                BigDecimal newValue = new BigDecimal(value);
                if (old.compareTo(newValue) == 0) {
                    continue;
                }
            } catch (Exception ignore) {
            }
            // 尝试比较string是否相同
            if (oldValue.toString().equals(value)) {
                continue;
            }
            // 记录价格变更
            if (PriceColumEnum.isPriceColumn(column.getSign())) {
                if (priceChangeBO == null) {
                    priceChangeBO = new GoodsPriceChangeBO()
                            .setGoodsId(goods.getId())
                            .setComment("货品列表修改货品");
                }
                long priceChange = new BigDecimal(value).subtract(PriceUtil.formatTwoDecimal(oldValue.toString())).longValue();
                try {
                    Field priceField = GoodsPriceChangeBO.class.getDeclaredField(column.getSign());
                    priceField.setAccessible(true);
                    ReflectUtil.setFieldValue(priceChangeBO, priceField, priceChange);
                }catch (Exception ignore) {}
            }
            // 修改为新值
            // 获取旧值名称（如果是_id类型 则取到label值）
            String oldValueName = getValueNameBySign(column.getSign(), oldValue);
            try {
                if (StringUtils.isBlank(value)) {
                    logString.append(column.getName()).append(COLON).append(oldValueName).append(MODIFY_STRING).append(" ").append(NEW_LINE);
                    field.set(goods, null);
                } else {
                    String newName = getValueNameBySign(column.getSign(), value);
                    logString.append(column.getName()).append(COLON).append(oldValueName).append(MODIFY_STRING).append(newName).append(NEW_LINE);
                    ReflectUtil.setFieldValue(goods, field, value);
                }
            } catch (Exception e) {
                log.error("货品修改失败", e);
                throw new BusinessException("货品修改失败");
            }
        }
        // 修改前校验
        // 1：成本单价 = 金进金额 + 银进金额 + 进工费单价 + 证书费
        // 2：证书费不能大于成本单价
        BigDecimal costYuan = PriceUtil.fen2yuan(goods.getGoldPrice()).multiply(goods.getNetGoldWeight()).setScale(2, RoundingMode.HALF_UP)
                .add(PriceUtil.fen2yuan(goods.getSilverPrice()).multiply(goods.getNetSilverWeight()).setScale(2, RoundingMode.HALF_UP))
                .add(PriceUtil.fen2yuan(goods.getWorkPrice()))
                .add(PriceUtil.fen2yuan(goods.getCertPrice()));
        long cost = PriceUtil.yuan2fen(costYuan).longValue();
        Assert.isTrue(goods.getCostPrice().equals(cost), "成本单价有误，修改失败(成本单价 = 金进金额 + 银进金额 + 进工费单价 + 证书费)");
        Assert.isTrue(goods.getCertPrice() <= goods.getCostPrice(), "证书费不能大于成本单价");
        this.mapper.update(goods, false);
        if (priceChangeBO != null) {
            PriceUtil.logPriceChange(List.of(priceChangeBO));
        }
        if (!logString.isEmpty()) {
            OpLogUtils.appendGoodsLog("货品列表-修改货品", "修改货品", logString.toString(), goods);
        }
    }

    private void customerColumnHandle(CustomColumnBO column, JSONObject form, GoodsEntity goods, StringBuilder logString) {
        // 没有则新增 有则更新
        GoodsHasColumnsEntity goodsHasColumns;
        if (column.getGoodsHasColumnId() == null) {
            goodsHasColumns = new GoodsHasColumnsEntity()
                    .setGoodsId(goods.getId())
                    .setColumnId(column.getId().intValue())
                    .setColumnSign(column.getSign())
                    .setValue("")
                    .setImageId(0)
                    .setCompanyId(SecurityUtils.getCompanyId());
        }else {
            goodsHasColumns = new GoodsHasColumnsEntity();
            goodsHasColumns.setId(column.getGoodsHasColumnId());
        }

        // 图片处理
        if (column.getType().equals(6)) {
            if (StringUtils.isBlank(form.getStr(column.getSign()))) {
                return;
            }
            List<GoodsHasImagesEntity> images = form.getBeanList(column.getSign(), GoodsHasImagesEntity.class);
            GoodsHasImagesEntity image = null;
            if (CollectionUtil.isNotEmpty(images)) {
                image = images.getFirst();
                if (image.getImageId() == null) {
                    image.setImageId(image.getId());
                }
            }
            // 更新
            if (image != null && !image.getImageId().equals(column.getImageId())) {
                CommonUtils.updateFileStatus(column.getImageId(), 0);
                CommonUtils.updateFileStatus(image.getImageId(), 1);
                goodsHasColumns.setImageId(image.getImageId().intValue());
                goodsHasColumns.setValue(image.getUrl());
                logString.append(column.getName()).append(COLON).append(column.getValue() == null ? " " : column.getValue())
                        .append(MODIFY_STRING).append(image.getUrl()).append(NEW_LINE);
            }
            // 删除
            if (image == null && column.getImageId() != null) {
                CommonUtils.updateFileStatus(column.getImageId(), 0);
                goodsHasColumns.setImageId(0);
                goodsHasColumns.setValue("");
                logString.append(column.getName()).append(COLON).append(column.getValue()).append(MODIFY_STRING).append(" ").append(NEW_LINE);
            }
        }else {
            String newValue = form.getStr(column.getSign());
            if (column.getValue() != null && column.getValue().equals(newValue)) {
                return;
            }
            goodsHasColumns.setValue(newValue);
            // 下拉列表类型 取label记录日志
            if (column.getType().equals(5)) {
                try {
                    Map<String, String> labelMap = JSONUtil.toList(column.getOptions(), Option.class).stream()
                            .collect(Collectors.toMap(item -> item.getValue().toString(), Option::getLabel, (v1, v2) -> v1));
                    boolean isMultiple = column.getIsMultiple().equals(1);
                    // 多选下拉处理
                    if (isMultiple) {
                        String[] oldValues = column.getValue().replaceAll("\\[", "").replaceAll("\\]", "").split(",");
                        StringBuilder oldLabel = new StringBuilder();
                        for (String oldValue : oldValues) {
                            oldLabel.append(labelMap.get(oldValue)).append(",");
                        }
                        if (!oldLabel.isEmpty()) {
                            oldLabel.deleteCharAt(oldLabel.length() - 1);
                        }
                        String[] newValueName = newValue.replaceAll("\\[", "").replaceAll("\\]", "").split(",");
                        StringBuilder newLabel = new StringBuilder();
                        for (String value : newValueName) {
                            newLabel.append(labelMap.get(value)).append(",");
                        }
                        if (!newLabel.isEmpty()) {
                            newLabel.deleteCharAt(newLabel.length() - 1);
                        }
                        logString.append(column.getName()).append(COLON).append(oldLabel).append(MODIFY_STRING).append(newLabel).append(NEW_LINE);
                    }else {
                        // 单选下拉处理
                        String oldValue = labelMap.get(column.getValue());
                        oldValue = oldValue == null ? " " : oldValue;
                        String newValueName = labelMap.get(newValue);
                        newValueName = newValueName == null ? " " : newValueName;
                        logString.append(column.getName()).append(COLON).append(oldValue).append(MODIFY_STRING).append(newValueName).append(NEW_LINE);
                    }
                }catch (Exception ignore){}
            }else {
                logString.append(column.getName()).append(COLON).append(column.getValue() != null ? column.getValue() : " ").append(MODIFY_STRING).append(newValue).append(NEW_LINE);
            }
        }
        goodsHasColumnsMapper.insertOrUpdate(goodsHasColumns,  true);
    }


    @Override
    public GoodsStatisticVo statistic(GoodsPageQuery query) {
        QueryWrapper wrapper = buildWrapper(query);
        wrapper.select(sum(GOODS.STOCK_NUM).as("totalStock"),
                sum(GOODS.NET_GOLD_WEIGHT.multiply(GOODS.STOCK_NUM)).as("totalNetGoldWeight"),
                sum(GOODS.NET_SILVER_WEIGHT.multiply(GOODS.STOCK_NUM)).as("totalNetSilverWeight"),
                sum(GOODS.COST_PRICE.multiply(GOODS.STOCK_NUM)).as("totalCost"));
        GoodsStatisticVo vo = mapper.selectOneByQueryAs(wrapper, GoodsStatisticVo.class);
        if (Objects.isNull(vo)) {
            return new GoodsStatisticVo();
        }
        vo.setTotalCost(PriceUtil.fen2yuanString(vo.getTotalCost()));
        vo.setTotalNetGoldWeight(PriceUtil.formatThreeDecimal(vo.getTotalNetGoldWeight()));
        vo.setTotalNetSilverWeight(PriceUtil.formatThreeDecimal(vo.getTotalNetSilverWeight()));
        GoodsColumnEntity column = CommonUtils.getGoodsColumnsBySign(PriceColumEnum.COST_PRICE.getSign());
        String cost = ColumnEncryptUtil.handleEncryptPrice(vo.getTotalCost(), column);
        vo.setTotalCost(cost);
        return vo;
    }

    @Override
    public Page<GoodsLogVo> log(String goodsSn, Integer pageNum, Integer pageSize) {
        QueryWrapper wrapper = QueryWrapper.create()
                .where(GoodsLogEntity::getGoodsSn).eq(goodsSn)
                .where(GoodsLogEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .where(GoodsLogEntity::getMerchantId).in(SecurityUtils.getMerchantIds(), !SecurityUtils.isMain())
                .orderBy(GoodsLogEntity::getCreatedAt, false);
        Page<GoodsLogVo> page = goodsLogMapper.paginateAs(pageNum, pageSize, wrapper, GoodsLogVo.class);
        List<GoodsLogVo> vos = page.getRecords();
        if (vos.isEmpty()) {
            return page;
        }
        ListFillUtil.of(vos)
                .build(fillService::getUserNameByUserId, vos.stream().map(GoodsLogVo::getUserId).collect(Collectors.toSet()), "userId", "creatorName")
                .handle();
        return page;
    }

    @Override
    public List<JSONObject> print(GoodsPrintQuery query) {
        List<GoodsPrintVo> vos = null;
        boolean isIncome = Objects.nonNull(query.getIncomeId());
        if (CollectionUtil.isNotEmpty(query.getGoodsIds())) {
            vos = this.mapper.selectListByQueryAs(QueryWrapper.create()
                    .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                    .where(GOODS.MERCHANT_ID.in(SecurityUtils.getMerchantIds()))
                    .where(GOODS.ID.in(query.getGoodsIds())), GoodsPrintVo.class);
        }

        if (Objects.nonNull(query.getIncomeId())) {
            vos = incomeDetailMapper.selectListByQueryAs(QueryWrapper.create()
                    .where(GOODS_INCOME_DETAIL.RECEIVE_ID.eq(query.getIncomeId()))
                    .where(GOODS_INCOME_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                    .where(GOODS_INCOME_DETAIL.MERCHANT_ID.in(SecurityUtils.getMerchantIds())), GoodsPrintVo.class);
        }
        if (CollectionUtil.isEmpty(vos)) {
            return new ArrayList<>();
        }
        // 填充
        Set<Long> categoryIds  = new HashSet<>(List.of(0L));
        Set<Long> subclassIds  = new HashSet<>(List.of(0L));
        Set<Long> brandIds  = new HashSet<>(List.of(0L));
        Set<Long> styleIds  = new HashSet<>(List.of(0L));
        Set<Long> qualityIds  = new HashSet<>(List.of(0L));
        Set<Long> technologyIds  = new HashSet<>(List.of(0L));
        Set<Long> mainStoneIds  = new HashSet<>(List.of(0L));
        Set<Long> subStoneIds  = new HashSet<>(List.of(0L));
        Set<Long> counterIds  = new HashSet<>(List.of(0L));
        Set<Long> ids  = new HashSet<>();
        vos.forEach(vo -> {
            categoryIds.add(vo.getCategoryId());
            subclassIds.add(vo.getSubclassId());
            brandIds.add(vo.getBrandId());
            styleIds.add(vo.getStyleId());
            qualityIds.add(vo.getQualityId());
            technologyIds.add(vo.getTechnologyId());
            mainStoneIds.add(vo.getMainStoneId());
            subStoneIds.add(vo.getSubStoneId());
            counterIds.add(vo.getCounterId());
            ids.add(vo.getId());
        });
        ListFillUtil fillUtil = ListFillUtil.of(vos)
                .build(fillService::getCategoryNameById, categoryIds, "categoryId", "category")
                .build(fillService::getSubclassNameById, subclassIds, "subclassId", "subclass")
                .build(fillService::getBrandNameById, brandIds, "brandId", "brand")
                .build(fillService::getStyleNameById, styleIds, "styleId", "style")
                .build(fillService::getQualityNameById, qualityIds, "qualityId", "quality")
                .build(fillService::getTechnologyNameById, technologyIds, "technologyId", "technology")
                .build(fillService::getJewelryMapperNameById, mainStoneIds, "mainStoneId", "mainStone")
                .build(fillService::getJewelryMapperNameById, subStoneIds, "subStoneId", "subStone")
                .build(fillService::getCounterNameById, counterIds, "counterId", "counter")
                .peek(obj -> {
                    GoodsPrintVo vo = (GoodsPrintVo) obj;
                    if (vo.getCustomerColumns() != null) {
                        vo.setCustomerColumns(vo.getCustomerColumns().stream().filter(column -> column.getType() != 6).toList());
                    }

                    vo.setCostPrice(PriceUtil.fen2yuanString(vo.getCostPrice()));
                    vo.setCostPriceClear(PriceUtil.yuanClear(vo.getCostPrice()));
                    vo.setCostPricePreUnit(PriceUtil.yuanPreUnit(vo.getCostPrice()));
                    vo.setCostPricePreUnitClear(PriceUtil.yuanPreUnitClear(vo.getCostPrice()));
                    vo.setCostPriceLastUnit(PriceUtil.yuanLastUnit(vo.getCostPrice()));
                    vo.setCostPriceLastUnitClear(PriceUtil.yuanLastUnitClear(vo.getCostPrice()));

                    vo.setWorkPrice(PriceUtil.fen2yuanString(vo.getWorkPrice()));
                    vo.setWorkPriceClear(PriceUtil.yuanClear(vo.getWorkPrice()));
                    vo.setWorkPricePreUnit(PriceUtil.yuanPreUnit(vo.getWorkPrice()));
                    vo.setWorkPricePreUnitClear(PriceUtil.yuanPreUnitClear(vo.getWorkPrice()));
                    vo.setWorkPriceLastUnit(PriceUtil.yuanLastUnit(vo.getWorkPrice()));
                    vo.setWorkPriceLastUnitClear(PriceUtil.yuanLastUnitClear(vo.getWorkPrice()));

                    vo.setTagPrice(PriceUtil.fen2yuanString(vo.getTagPrice()));
                    vo.setTagPriceClear(PriceUtil.yuanClear(vo.getTagPrice()));
                    vo.setTagPricePreUnit(PriceUtil.yuanPreUnit(vo.getTagPrice()));
                    vo.setTagPricePreUnitClear(PriceUtil.yuanPreUnitClear(vo.getTagPrice()));
                    vo.setTagPriceLastUnit(PriceUtil.yuanLastUnit(vo.getTagPrice()));
                    vo.setTagPriceLastUnitClear(PriceUtil.yuanLastUnitClear(vo.getTagPrice()));

                    vo.setCertPrice(PriceUtil.fen2yuanString(vo.getCertPrice()));
                    vo.setCertPriceClear(PriceUtil.yuanClear(vo.getCertPrice()));
                    vo.setCertPricePreUnit(PriceUtil.yuanPreUnit(vo.getCertPrice()));
                    vo.setCertPricePreUnitClear(PriceUtil.yuanPreUnitClear(vo.getCertPrice()));
                    vo.setCertPriceLastUnit(PriceUtil.yuanLastUnit(vo.getCertPrice()));
                    vo.setCertPriceLastUnitClear(PriceUtil.yuanLastUnitClear(vo.getCertPrice()));
                });
        if (isIncome) {
            fillUtil.build(fillService::getIncomeColumnVosById, ids, "id", "customerColumns");
        }else {
            fillUtil.build(fillService::getColumnVosById, ids, "id", "customerColumns");
        }
        fillUtil.handle();
        return ColumnEncryptUtil.encrypt(vos, GoodsPrintVo.class, "customerColumns");
    }

    @Override
    public List<GoodsPrintFieldVo> printFields() {
        // 基本字段
        Field[] fields = ReflectUtil.getFields(GoodsPrintVo.class);
        List<GoodsPrintFieldVo> base = Arrays.stream(fields)
                .filter(field -> field.getAnnotation(Schema.class) != null)
                .map(field -> new GoodsPrintFieldVo(field.getAnnotation(Schema.class).description(), field.getName(), 0))
                .toList();
        List<GoodsPrintFieldVo> list = new ArrayList<>(base);
        // 自定义字段
        List<GoodsPrintFieldVo> custom = CommonUtils.getGoodsColumns().stream()
                .filter(column -> column.getCategory().equals(2) && !column.getType().equals(6))
                .map(column -> new GoodsPrintFieldVo(column.getName(), column.getSign(), 1))
                .toList();
        list.addAll(custom);
        return list;
    }

    /**
     * 根据大类id，查询当前用户可编辑的字段（包括基础字段和自定义字段）
     * @param companyId
     * @param categoryId
     * @param goodsId
     * @return
     */
    private List<CustomColumnBO> getGoodsDefaultColumn(Long companyId, Long categoryId, Long goodsId) {
        List<GoodsIncomeTemplateDetailEntity> templateDetails = goodsIncomeTemplateDetailMapper.selectListByQuery(QueryWrapper.create()
                .leftJoin(GOODS_INCOME_TEMPLATE).on(GOODS_INCOME_TEMPLATE_DETAIL.TEMPLATE_ID.eq(GOODS_INCOME_TEMPLATE.ID))
                .where(GOODS_INCOME_TEMPLATE.COMPANY_ID.eq(companyId))
                .where(GOODS_INCOME_TEMPLATE.CATEGORY_ID.eq(categoryId))
                .where(GOODS_INCOME_TEMPLATE.DEFAULT_FLAG.eq(1))
                .where(GOODS_INCOME_TEMPLATE.STATUS.eq(1))
                .where(GOODS_INCOME_TEMPLATE_DETAIL.ENABLED.eq(1))
        );
        List<String> signs = templateDetails.stream().map(GoodsIncomeTemplateDetailEntity::getSign).toList();

        signs = signs.stream().filter(sign -> !sign.equals("goods_sn") && !sign.equals("counter_id") && !sign.equals("num")).toList();
        List<CustomColumnBO> columnBOS = goodsColumnMapper.selectListByQueryAs(QueryWrapper.create()
                .where(GOODS_COLUMN.SIGN.in(signs))
                .where(GOODS_COLUMN.COMPANY_ID.eq(companyId))
                .select(GOODS_COLUMN.ALL_COLUMNS), CustomColumnBO.class);
        Map<String, CustomColumnBO> map = columnBOS.stream().collect(Collectors.toMap(CustomColumnBO::getSign, columnBO -> columnBO));
        // 填充自定义字段
        List<GoodsHasColumnsEntity> goodsHasColumns = goodsHasColumnsMapper.selectListByQuery(QueryWrapper.create()
                .where(GOODS_HAS_COLUMNS.GOODS_ID.eq(goodsId))
                .where(GOODS_HAS_COLUMNS.COLUMN_SIGN.in(signs))
                .where(GOODS_HAS_COLUMNS.COMPANY_ID.eq(SecurityUtils.getCompanyId())));
        for (GoodsHasColumnsEntity goodsHasColumn : goodsHasColumns) {
            CustomColumnBO columnBO = map.get(goodsHasColumn.getColumnSign());
            columnBO.setValue(goodsHasColumn.getValue());
            columnBO.setImageId(goodsHasColumn.getImageId().longValue());
            columnBO.setGoodsHasColumnId(goodsHasColumn.getId());
            columnBO.setGoodsId(goodsHasColumn.getGoodsId());
        }
        return columnBOS;
    }

    private QueryWrapper buildWrapper(GoodsPageQuery query) {
        // 固定条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()));
        Set<Long> merchantIds = SecurityUtils.getMerchantIds();
        merchantIds.add(0L);
        // 非主账号限制门店
        queryWrapper.where(GOODS.MERCHANT_ID.in(merchantIds, !SecurityUtils.isMain()));
        // 筛选条件
        queryWrapper.where(GOODS.GOODS_SN.eq(query.getGoodsSn(), StringUtils.isNotBlank(query.getGoodsSn())));
        queryWrapper.where(GOODS.NAME.like(query.getName(), StringUtils.isNotBlank(query.getName())));
        if (StringUtils.isNotBlank(query.getMerchantIds())) {
            queryWrapper.where(GOODS.MERCHANT_ID.in(List.of(query.getMerchantIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getSupplierIds())) {
            queryWrapper.where(GOODS.SUPPLIER_ID.in(List.of(query.getSupplierIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getStyleIds())){
            queryWrapper.where(GOODS.SUPPLIER_ID.in(List.of(query.getStyleIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getCategoryIds())){
            queryWrapper.where(GOODS.CATEGORY_ID.in(List.of(query.getCategoryIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getSubclassIds())){
            queryWrapper.where(GOODS.SUBCLASS_ID.in(List.of(query.getSubclassIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getCounterIds())){
            queryWrapper.where(GOODS.COUNTER_ID.in(List.of(query.getCounterIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getQualityIds())){
            queryWrapper.where(GOODS.QUALITY_ID.in(List.of(query.getQualityIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getBrandIds())){
            queryWrapper.where(GOODS.BRAND_ID.in(List.of(query.getBrandIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getStyleIds())){
            queryWrapper.where(GOODS.STYLE_ID.in(List.of(query.getStyleIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getMainStoneIds())){
            queryWrapper.where(GOODS.MAIN_STONE_ID.in(List.of(query.getMainStoneIds().split(","))));
        }

        if (StringUtils.isNotBlank(query.getSubStoneIds())){
            queryWrapper.where(GOODS.SUB_STONE_ID.in(List.of(query.getSubStoneIds().split(","))));
        }

        if (query.getStatus() != null) {
            String[] split = query.getStatus().split(",");
            for (String status : split) {
                if (status.equals("1")) {
                    queryWrapper.where(GOODS.STOCK_NUM.gt(0));
                }else if (status.equals("0")) {
                    queryWrapper.where(GOODS.SOLD_NUM.gt(0));
                }
            }
        }
        if (CollectionUtil.isNotEmpty(query.getIds())) {
            queryWrapper.where(GOODS.ID.in(query.getIds()));
        }
        return queryWrapper;
    }

    private void fillList(List<GoodsPageVo> list) {
        Set<Long> brandIds = new HashSet<>(List.of(0L));
        Set<Long> counterIds = new HashSet<>(List.of(0L));
        Set<Long> supplierIds = new HashSet<>(List.of(0L));
        Set<Long> merchantIds = new HashSet<>(List.of(0L));
        Set<Long> styleIds = new HashSet<>(List.of(0L));
        Set<Long> subclassIds = new HashSet<>(List.of(0L));
        Set<Long> qualityIds = new HashSet<>(List.of(0L));
        Set<Long> technologyIds = new HashSet<>(List.of(0L));
        Set<Long> mainStoneIds = new HashSet<>(List.of(0L));
        Set<Long> subStoneIds = new HashSet<>(List.of(0L));
        Set<Long> ids = new HashSet<>(List.of(0L));
        for (GoodsPageVo vo : list) {
            ids.add(vo.getId());
            merchantIds.add(vo.getMerchantId());
            counterIds.add(vo.getCounterId());
            supplierIds.add(vo.getSupplierId());
            subclassIds.add(vo.getSubclassId());
            brandIds.add(vo.getBrandId());
            styleIds.add(vo.getStyleId());
            qualityIds.add(vo.getQualityId());
            technologyIds.add(vo.getTechnologyId());
            mainStoneIds.add(vo.getMainStoneId());
            subStoneIds.add(vo.getSubStoneId());
        }
        ListFillUtil.of(list)
                .build(fillService::getMerchantNameById, merchantIds, "merchantId", "merchant")
                .build(fillService::getCounterNameById, counterIds, "counterId", "counter")
                .build(fillService::getSupplierNameById, supplierIds, "supplierId", "supplier")
                .build(fillService::getCategoryNameById, null, "categoryId", "category")
                .build(fillService::getSubclassNameById, subclassIds, "subclassId", "subclass")
                .build(fillService::getBrandNameById, brandIds, "brandId", "brand")
                .build(fillService::getStyleNameById, styleIds, "styleId", "style")
                .build(fillService::getQualityNameById, qualityIds, "qualityId", "quality")
                .build(fillService::getTechnologyNameById, technologyIds, "technologyId", "technology")
                .build(fillService::getJewelryMapperNameById, mainStoneIds, "mainStoneId", "mainStone")
                .build(fillService::getJewelryMapperNameById, subStoneIds, "subStoneId", "subStone")
                .build(fillService::getColumnVosById, ids, "id", "customerColumns")
                .build(fillService::getGoodsImgByGoodsId, ids, "id", "images")
                .peek(obj -> {
                    GoodsPageVo vo = (GoodsPageVo) obj;
                    // 价格字段format
                    vo.setCostPrice(PriceUtil.fen2yuan(vo.getCostPrice()));
                    vo.setGoldPrice(PriceUtil.fen2yuan(vo.getGoldPrice()));
                    vo.setSilverPrice(PriceUtil.fen2yuan(vo.getSilverPrice()));
                    vo.setWorkPrice(PriceUtil.fen2yuan(vo.getWorkPrice()));
                    vo.setCertPrice(PriceUtil.fen2yuan(vo.getCertPrice()));
                    vo.setSaleWorkPrice(PriceUtil.fen2yuan(vo.getSaleWorkPrice()));
                    vo.setTagPrice(PriceUtil.fen2yuan(vo.getTagPrice()));
                    if (vo.getImages() == null) {
                        vo.setImages(new ArrayList<>());
                    }
                    if (vo.getCustomerColumns() == null) {
                        vo.setCustomerColumns(new ArrayList<>());
                    }
                })
                .handle();
    }

    private void doExport(QueryWrapper wrapper) {
        ExcelUtil.of(this.mapper, wrapper, GoodsPageVo.class, "goods", "货品列表")
                .getData((mapper, queryWrapper) -> {
                    if (mapper instanceof GoodsMapper goodsMapper) {
                        List<GoodsPageVo> list = goodsMapper.selectListByQueryAs(wrapper, GoodsPageVo.class);
                        this.fillList(list);
                        List<JSONObject> columns = ColumnEncryptUtil.encrypt(list, GoodsPageVo.class, "customerColumns");
                        ColumnEncryptUtil.handleJsonImageExport(columns);
                        // 处理数量字段
                        columns.forEach(item -> {
                            String numBuilder = NumColumEnum.STOCK_NUM.getLabel() + COLON + item.get(NumColumEnum.STOCK_NUM.getSign()) + SPACE +
                                    NumColumEnum.SOLD_NUM.getLabel() + COLON + item.get(NumColumEnum.SOLD_NUM.getSign()) + SPACE +
                                    NumColumEnum.RETURN_NUM.getLabel() + COLON + item.get(NumColumEnum.RETURN_NUM.getSign()) + SPACE +
                                    NumColumEnum.TRANSFER_NUM.getLabel() + COLON + item.get(NumColumEnum.TRANSFER_NUM.getSign()) + SPACE +
                                    NumColumEnum.FROZEN_NUM.getLabel() + COLON + item.get(NumColumEnum.FROZEN_NUM.getSign());
                            item.set(NumColumEnum.NUM_DETAIL.getSign(), numBuilder);
                            if (StringUtils.isNotBlank(item.getStr("sales_type"))) {
                                item.set("sales_type", item.getStr("sales_type").equals("1") ? "按重量" : "按数量");
                            }
                        });
                        return columns;
                    }
                    return new ArrayList<>();
                }).doExport();
    }


    /**
     * 关联字段类型获取字段名称,用于日志记录
     * @param sign
     * @param oldValue
     * @return
     */
    private String getValueNameBySign(String sign, Object oldValue) {
        if (Objects.isNull(oldValue)) {
            return "";
        }
        if (JoinColumEnum.COUNTER_ID.getSign().equals(sign)) {
            return fillService.getCounterNameById(Set.of(oldValue.toString())).get(oldValue.toString());
        }
        if (JoinColumEnum.SUPPLIER_ID.getSign().equals(sign)) {
            return fillService.getSupplierNameById(Set.of(oldValue.toString())).get(oldValue.toString());
        }
        if (JoinColumEnum.CATEGORY_ID.getSign().equals(sign)) {
            return fillService.getCategoryNameById(Set.of(oldValue.toString())).get(oldValue.toString());
        }
        if (JoinColumEnum.SUBCLASS_ID.getSign().equals(sign)) {
            return fillService.getSubclassNameById(Set.of(oldValue.toString())).get(oldValue.toString());
        }
        if (JoinColumEnum.BRAND_ID.getSign().equals(sign)) {
            return fillService.getBrandNameById(Set.of(oldValue.toString())).get(oldValue.toString());
        }
        if (JoinColumEnum.STYLE_ID.getSign().equals(sign)) {
            return fillService.getStyleNameById(Set.of(oldValue.toString())).get(oldValue.toString());
        }
        if (JoinColumEnum.QUALITY_ID.getSign().equals(sign)) {
            return fillService.getQualityNameById(Set.of(oldValue.toString())).get(oldValue.toString());
        }
        if (JoinColumEnum.TECHNOLOGY_ID.getSign().equals(sign)) {
            return fillService.getTechnologyNameById(Set.of(oldValue.toString())).get(oldValue.toString());
        }
        if (JoinColumEnum.MAIN_STONE_ID.getSign().equals(sign)) {
            return fillService.getJewelryMapperNameById(Set.of(oldValue.toString())).get(oldValue.toString());
        }
        if (JoinColumEnum.SUB_STONE_ID.getSign().equals(sign)) {
            return fillService.getJewelryMapperNameById(Set.of(oldValue.toString())).get(oldValue.toString());
        }
        if (PriceColumEnum.isPriceColumn(sign)) {
            return PriceUtil.fen2yuanString(oldValue.toString());
        }
        return oldValue.toString();
    }
}
