package com.xc.boot.modules.goods.service.impl;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.modules.goods.mapper.GoodsTransferMapper;
import com.xc.boot.modules.goods.model.entity.GoodsTransferEntity;
import com.xc.boot.modules.goods.model.query.GoodsTransferPageQuery;
import com.xc.boot.modules.goods.model.vo.GoodsTransferPageVO;
import com.xc.boot.modules.goods.service.GoodsTransferService;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtil;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.model.enums.GoodsTransferStatusEnum;
import com.xc.boot.modules.goods.model.query.GoodsTransferGoodsQuery;
import com.xc.boot.modules.goods.model.vo.GoodsTransferGoodsVO;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.model.bo.StockNumChangeBO;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.common.util.excel.ExcelUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.xc.boot.modules.goods.model.query.GoodsTransferCreateDTO;
import com.xc.boot.modules.goods.mapper.GoodsTransferDetailMapper;
import com.xc.boot.modules.goods.model.entity.GoodsTransferDetailEntity;
import com.xc.boot.common.util.OpLogUtils;
import org.springframework.transaction.annotation.Transactional;
import com.xc.boot.common.util.SnUtils;
import com.xc.boot.common.util.StockUtils;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.Map;
import java.util.stream.Collectors;
import com.xc.boot.modules.goods.model.dto.GoodsTransferReceiptDTO;
import com.xc.boot.modules.goods.mapper.GoodsHasColumnsMapper;
import com.xc.boot.modules.goods.mapper.GoodsHasImagesMapper;
import com.xc.boot.modules.goods.model.entity.GoodsHasColumnsEntity;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import com.xc.boot.modules.goods.model.dto.GoodsTransferUpdateDTO;
import com.xc.boot.modules.goods.model.dto.GoodsTransferInfoRequest;
import com.xc.boot.modules.goods.model.vo.GoodsTransferInfoVO;
import com.xc.boot.modules.goods.model.query.GoodsTransferDetailPageQuery;
import com.xc.boot.modules.goods.model.dto.GoodsTransferDetailUpdateDTO;
import com.mybatisflex.core.row.Row;
import com.mybatisflex.core.query.QueryMethods;
import com.xc.boot.common.util.QueryUtils;
import cn.hutool.core.util.StrUtil;
import org.springframework.util.CollectionUtils;

@Service
public class GoodsTransferServiceImpl extends ServiceImpl<GoodsTransferMapper, GoodsTransferEntity> implements GoodsTransferService {
    @Autowired
    private ListFillService listFillService;

    @Autowired
    private GoodsMapper goodsMapper;

    @Autowired
    private GoodsTransferDetailMapper goodsTransferDetailMapper;

    @Autowired
    private GoodsHasColumnsMapper goodsHasColumnsMapper;

    @Autowired
    private GoodsHasImagesMapper goodsHasImagesMapper;

    @Override
    public Page<GoodsTransferPageVO> pageTransfer(GoodsTransferPageQuery query) {
        QueryWrapper wrapper = QueryWrapper.create()
                .from(GoodsTransferEntity.class)
                .where(GoodsTransferEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                // 门店权限限制：调出门店或调入门店必须在用户权限范围内
                .and(QueryMethods.column(GoodsTransferEntity::getMerchantOutcome).in(SecurityUtils.getMerchantIds())
                    .or(QueryMethods.column(GoodsTransferEntity::getMerchantIncome).in(SecurityUtils.getMerchantIds())))
                .and(GoodsTransferEntity::getTransferSn).like(query.getTransferSn(), query.getTransferSn() != null)
                .and(GoodsTransferEntity::getType).eq(query.getType(), query.getType() != null)
                .and(GoodsTransferEntity::getStatus).eq(query.getStatus(), query.getStatus() != null)
                .and(GoodsTransferEntity::getCreatedAt).between(
                        !CollectionUtils.isEmpty(query.getCreatedAtRange()) ? query.getCreatedAtRange().get(0) : null,
                        !CollectionUtils.isEmpty(query.getCreatedAtRange()) ? query.getCreatedAtRange().get(1) : null,
                        !CollectionUtils.isEmpty(query.getCreatedAtRange()) && query.getCreatedAtRange().size() == 2)
                .and(GoodsTransferEntity::getAuditAt).between(
                        !CollectionUtils.isEmpty(query.getAuditAtRange()) ? query.getAuditAtRange().get(0) : null,
                        !CollectionUtils.isEmpty(query.getAuditAtRange()) ? query.getAuditAtRange().get(1) : null,
                        !CollectionUtils.isEmpty(query.getAuditAtRange()) && query.getAuditAtRange().size() == 2)
                .and(GoodsTransferEntity::getReceiptAt).between(
                        !CollectionUtils.isEmpty(query.getReceiptAtRange()) ? query.getReceiptAtRange().get(0) : null,
                        !CollectionUtils.isEmpty(query.getReceiptAtRange()) ? query.getReceiptAtRange().get(1) : null,
                        !CollectionUtils.isEmpty(query.getReceiptAtRange()) && query.getReceiptAtRange().size() == 2)
                .orderBy(GoodsTransferEntity::getId, false);

        // 处理调出门店多选
        if (StrUtil.isNotBlank(query.getMerchantOutcomeIds())) {
            List<Long> merchantOutcomeIds = QueryUtils.parseIds(query.getMerchantOutcomeIds());
            if (!merchantOutcomeIds.isEmpty()) {
                wrapper.and(GoodsTransferEntity::getMerchantOutcome).in(merchantOutcomeIds);
            }
        }

        // 处理调入门店多选
        if (StrUtil.isNotBlank(query.getMerchantIncomeIds())) {
            List<Long> merchantIncomeIds = QueryUtils.parseIds(query.getMerchantIncomeIds());
            if (!merchantIncomeIds.isEmpty()) {
                wrapper.and(GoodsTransferEntity::getMerchantIncome).in(merchantIncomeIds);
            }
        }

        // 处理创建人多选
        if (StrUtil.isNotBlank(query.getCreatedByIds())) {
            List<Long> createdByIds = QueryUtils.parseIds(query.getCreatedByIds());
            if (!createdByIds.isEmpty()) {
                wrapper.and(GoodsTransferEntity::getCreatedBy).in(createdByIds);
            }
        }

        // 处理审核人多选
        if (StrUtil.isNotBlank(query.getAuditByIds())) {
            List<Long> auditByIds = QueryUtils.parseIds(query.getAuditByIds());
            if (!auditByIds.isEmpty()) {
                wrapper.and(GoodsTransferEntity::getAuditBy).in(auditByIds);
            }
        }

        // 处理收货人多选
        if (StrUtil.isNotBlank(query.getReceiptByIds())) {
            List<Long> receiptByIds = QueryUtils.parseIds(query.getReceiptByIds());
            if (!receiptByIds.isEmpty()) {
                wrapper.and(GoodsTransferEntity::getReceiptBy).in(receiptByIds);
            }
        }

        // 处理导出
        if (query.getExport() != null && query.getExport() == 1) {
            exportTransfers(wrapper, query);
            return null;
        }

        // 处理打印
        if (query.getPrint() != null && query.getPrint() == 1) {
            return printTransfers(wrapper, query);
        }

        // 执行分页查询
        Page<GoodsTransferEntity> page = this.page(new Page<>(query.getPageNum(), query.getPageSize()), wrapper);
        List<GoodsTransferPageVO> voList = page.getRecords().stream().map(entity -> {
            GoodsTransferPageVO vo = new GoodsTransferPageVO();
            BeanUtils.copyProperties(entity, vo);
            vo.setCounterOutcome(entity.getCounterId()); // 兼容VO字段
            // 设置状态中文名
            vo.setStatusLabel(com.xc.boot.common.base.IBaseEnum.getLabelByValue(entity.getStatus(), GoodsTransferStatusEnum.class));
            return vo;
        }).collect(Collectors.toList());

        // 填充关联数据
        fillTransferPageVOs(voList);

        Page<GoodsTransferPageVO> voPage = new Page<>(page.getPageNumber(), page.getPageSize(), page.getTotalRow());
        voPage.setRecords(voList);
        return voPage;
    }

    /**
     * 导出调拨单列表
     */
    private void exportTransfers(QueryWrapper query, GoodsTransferPageQuery queryParams) {
        // 检查导出数量限制
        long count = this.mapper.selectCountByQuery(query);
        CommonUtils.abortIf(count > CommonUtils.getMaxExportSize(), "导出数量超过限制");

        ExcelUtil.of(this.mapper, query, GoodsTransferPageVO.class, "transfers", "调拨单列表")
                .getData((mapper, wrapper) -> {
                    List<GoodsTransferPageVO> voList = mapper.selectListByQueryAs(wrapper, GoodsTransferPageVO.class);
                    fillTransferPageVOs(voList);
                    return voList;
                })
                .doExport();
    }

    /**
     * 打印调拨单列表
     */
    private Page<GoodsTransferPageVO> printTransfers(QueryWrapper query, GoodsTransferPageQuery queryParams) {
        // 检查打印数量限制
        long count = this.mapper.selectCountByQuery(query);
        CommonUtils.abortIf(count > CommonUtils.getMaxPrintSize(), "打印数量超过限制");

        List<GoodsTransferPageVO> voList = this.mapper.selectListByQueryAs(query, GoodsTransferPageVO.class);
        fillTransferPageVOs(voList);

        // 返回Page对象，pageNum=1, pageSize=voList.size(), total=count
        Page<GoodsTransferPageVO> page = new Page<>(1, voList.size(), count);
        page.setRecords(voList);
        return page;
    }

    /**
     * 填充调拨单VO的关联数据
     */
    private void fillTransferPageVOs(List<GoodsTransferPageVO> voList) {
        if (voList == null || voList.isEmpty()) {
            return;
        }

        // 收集需要填充的ID
        Set<Long> merchantIds = voList.stream()
                .flatMap(vo -> java.util.stream.Stream.of(vo.getMerchantOutcome(), vo.getMerchantIncome()))
                .filter(Objects::nonNull)
                .collect(java.util.stream.Collectors.toSet());
        Set<Integer> counterIds = voList.stream()
                .flatMap(vo -> java.util.stream.Stream.of(vo.getCounterOutcome(), vo.getCounterId()))
                .filter(Objects::nonNull)
                .collect(java.util.stream.Collectors.toSet());
        Set<Long> userIds = voList.stream()
                .flatMap(vo -> java.util.stream.Stream.of(vo.getCreatedBy(), vo.getAuditBy(), vo.getReceiptBy()))
                .filter(Objects::nonNull)
                .collect(java.util.stream.Collectors.toSet());

        // 批量填充关联数据
        ListFillUtil.of(voList)
                .build(listFillService::getMerchantNameById, merchantIds, "merchantOutcome", "merchantOutcomeName")
                .build(listFillService::getMerchantNameById, merchantIds, "merchantIncome", "merchantIncomeName")
                .build(listFillService::getCounterNameById, counterIds, "counterOutcome", "counterOutcomeName")
                .build(listFillService::getCounterNameById, counterIds, "counterId", "counterIdName")
                .build(listFillService::getUserNameByUserId, userIds, "createdBy", "createdByName")
                .build(listFillService::getUserNameByUserId, userIds, "auditBy", "auditByName")
                .build(listFillService::getUserNameByUserId, userIds, "receiptBy", "receiptByName")
                .handle();

        // 处理价格字段转换
        for (GoodsTransferPageVO vo : voList) {
            if (vo.getTotalCostPrice() != null) {
                vo.setTotalCostPrice(vo.getTotalCostPrice()); // 价格字段已经在VO中处理过了,这里不需要再转换
            }
        }
    }

    @Override
    public List<GoodsTransferGoodsVO> queryTransferGoods(GoodsTransferGoodsQuery query) {
        // 参数校验
        if (query.getType() == null || (query.getType() != 1 && query.getType() != 2)) {
            CommonUtils.abort("不支持的调拨类型");
        }
        if (query.getMerchantOutcome() == null || query.getMerchantOutcome() == 0) {
            CommonUtils.abort("请选择调出门店");
        }
        // 门店权限检查
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(query.getMerchantOutcome()), "无权操作其他门店的数据");
        
        boolean goodsSnValid = StringUtils.isNotBlank(query.getGoodsSn());
        boolean counterIdValid = query.getCounterId() != null && query.getCounterId() != 0;
        if (!goodsSnValid && !counterIdValid) {
            CommonUtils.abort("货品条码和柜台必须二选一");
        }
        // 构建查询
        QueryWrapper wrapper = QueryWrapper.create()
                .from(GoodsEntity.class)
                .where(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .and(GoodsEntity::getMerchantId).eq(query.getMerchantOutcome())
                // 门店权限限制
                .and(GoodsEntity::getMerchantId).in(SecurityUtils.getMerchantIds());
        // 调出门店
        if (query.getMerchantOutcome() != null && query.getMerchantOutcome() != 0) {
            wrapper.and(GoodsEntity::getMerchantId).eq(query.getMerchantOutcome());
        }
        // 货品条码优先
        if (StringUtils.isNotBlank(query.getGoodsSn())) {
            wrapper.and(GoodsEntity::getGoodsSn).eq(query.getGoodsSn());
        } else if (query.getCounterId() != null && query.getCounterId() != 0) {
            wrapper.and(GoodsEntity::getCounterId).eq(query.getCounterId());
        }
        // 排除调入柜台货品
        if (query.getCounterIncome() != null && query.getCounterIncome() != 0) {
            wrapper.and(GoodsEntity::getCounterId).ne(query.getCounterIncome());
        }
        // 排除调入门店货品
        if (query.getMerchantIncome() != null && query.getMerchantIncome() != 0) {
            wrapper.and(GoodsEntity::getMerchantId).ne(query.getMerchantIncome());
        }
        // 查询
        List<GoodsEntity> goodsList = goodsMapper.selectListByQuery(wrapper);
        if (goodsList.isEmpty()) return List.of();
        // 转VO
        List<GoodsTransferGoodsVO> voList = new java.util.ArrayList<>();
        for (GoodsEntity entity : goodsList) {
            GoodsTransferGoodsVO vo = new GoodsTransferGoodsVO();
            vo.setId(entity.getId());
            vo.setMerchant(""); // 后续填充
            vo.setGoodsSn(entity.getGoodsSn());
            vo.setName(entity.getName());
            vo.setImages(new java.util.ArrayList<>()); // 后续填充
            vo.setCategory("");
            vo.setSubclass("");
            vo.setCounter("");
            vo.setNum(entity.getStockNum());
            vo.setWeight(entity.getWeight());
            vo.setNetGoldWeight(entity.getNetGoldWeight());
            vo.setNetSilverWeight(entity.getNetSilverWeight());
            vo.setGoldPrice(PriceUtil.fen2yuan(entity.getGoldPrice()));
            vo.setGoldAmount(entity.getGoldPrice() != null && entity.getNetGoldWeight() != null ? PriceUtil.fen2yuan(entity.getGoldPrice()).multiply(entity.getNetGoldWeight()) : null);
            vo.setSilverPrice(PriceUtil.fen2yuan(entity.getSilverPrice()));
            vo.setSilverAmount(entity.getSilverPrice() != null && entity.getNetSilverWeight() != null ? PriceUtil.fen2yuan(entity.getSilverPrice()).multiply(entity.getNetSilverWeight()) : null);
            vo.setWorkPrice(PriceUtil.fen2yuan(entity.getWorkPrice()));
            vo.setWorkAmount(entity.getWorkPrice() != null && entity.getWeight() != null ? PriceUtil.fen2yuan(entity.getWorkPrice()).multiply(entity.getWeight()) : null);
            vo.setCertPrice(PriceUtil.fen2yuan(entity.getCertPrice()));
            vo.setCostPrice(PriceUtil.fen2yuan(entity.getCostPrice()));
            vo.setSalesType(entity.getSalesType() == null ? null : entity.getSalesType().toString());
            vo.setSaleWorkPrice(PriceUtil.fen2yuan(entity.getSaleWorkPrice()));
            vo.setSaleWorkAmount(entity.getSaleWorkPrice() != null && entity.getWeight() != null ? PriceUtil.fen2yuan(entity.getSaleWorkPrice()).multiply(entity.getWeight()) : null);
            vo.setTagPrice(PriceUtil.fen2yuan(entity.getTagPrice()));
            vo.setSupplier("");
            vo.setQuality("");
            vo.setStyle("");
            vo.setBrand("");
            vo.setCircleSize(entity.getCircleSize());
            vo.setTechnology("");
            vo.setCertNo(entity.getCertNo());
            vo.setMainStone("");
            vo.setMainStoneCount(entity.getMainStoneCount());
            vo.setMainStoneWeight(entity.getMainStoneWeight());
            vo.setSubStone("");
            vo.setSubStoneCount(entity.getSubStoneCount());
            vo.setSubStoneWeight(entity.getSubStoneWeight());
            vo.setBatchNo(entity.getBatchNo());
            vo.setMerchantId(entity.getMerchantId());
            vo.setCategoryId(entity.getCategoryId());
            vo.setSubclassId(entity.getSubclassId());
            vo.setCounterId(entity.getCounterId());
            vo.setSupplierId(entity.getSupplierId());
            vo.setQualityId(entity.getQualityId());
            vo.setStyleId(entity.getStyleId());
            vo.setBrandId(entity.getBrandId());
            vo.setTechnologyId(entity.getTechnologyId());
            vo.setMainStoneId(entity.getMainStoneId());
            vo.setSubStoneId(entity.getSubStoneId());
            voList.add(vo);
        }
        // 批量填充名称、图片等
        ListFillUtil.of(voList)
                .build(listFillService::getMerchantNameById, goodsList.stream().map(GoodsEntity::getMerchantId).collect(Collectors.toSet()), "merchantId", "merchant")
                .build(listFillService::getCategoryNameById, goodsList.stream().map(GoodsEntity::getCategoryId).collect(Collectors.toSet()), "categoryId", "category")
                .build(listFillService::getSubclassNameById, goodsList.stream().map(GoodsEntity::getSubclassId).collect(Collectors.toSet()), "subclassId", "subclass")
                .build(listFillService::getCounterNameById, goodsList.stream().map(GoodsEntity::getCounterId).collect(Collectors.toSet()), "counterId", "counter")
                .build(listFillService::getSupplierNameById, goodsList.stream().map(GoodsEntity::getSupplierId).collect(Collectors.toSet()), "supplierId", "supplier")
                .build(listFillService::getQualityNameById, goodsList.stream().map(GoodsEntity::getQualityId).collect(Collectors.toSet()), "qualityId", "quality")
                .build(listFillService::getStyleNameById, goodsList.stream().map(GoodsEntity::getStyleId).collect(Collectors.toSet()), "styleId", "style")
                .build(listFillService::getBrandNameById, goodsList.stream().map(GoodsEntity::getBrandId).collect(Collectors.toSet()), "brandId", "brand")
                .build(listFillService::getTechnologyNameById, goodsList.stream().map(GoodsEntity::getTechnologyId).collect(Collectors.toSet()), "technologyId", "technology")
                .build(listFillService::getJewelryMapperNameById, goodsList.stream().map(GoodsEntity::getMainStoneId).collect(Collectors.toSet()), "mainStoneId", "mainStone")
                .build(listFillService::getJewelryMapperNameById, goodsList.stream().map(GoodsEntity::getSubStoneId).collect(Collectors.toSet()), "subStoneId", "subStone")
                .build(listFillService::getGoodsImgByGoodsId, goodsList.stream().map(GoodsEntity::getId).collect(Collectors.toSet()), "id", "images")
                .build(listFillService::getColumnVosById, voList.stream().map(GoodsTransferGoodsVO::getId).collect(Collectors.toSet()), "id", "customerColumns")
                .handle();
        return voList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTransfer(GoodsTransferCreateDTO dto) {
        // 参数校验
        if (dto.getType() == null || (dto.getType() != 1 && dto.getType() != 2)) {
            CommonUtils.abort("调拨类型必填且只能为1或2");
        }
        if (dto.getMerchantOutcome() == null || dto.getMerchantOutcome() == 0) {
            CommonUtils.abort("调出门店必填");
        }
        if (dto.getMerchantIncome() == null || dto.getMerchantIncome() == 0) {
            CommonUtils.abort("调入门店必填");
        }
        if (dto.getCounterId() == null || (dto.getType() == 2 && dto.getCounterId() == 0)) {
            CommonUtils.abort("调入柜台必填");
        }
        if (dto.getDetails() == null || dto.getDetails().isEmpty()) {
            CommonUtils.abort("明细不能为空");
        }
        
        // 门店权限检查
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(dto.getMerchantOutcome()), "无权操作调出门店");
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(dto.getMerchantIncome()), "无权操作调入门店");
        
        // 创建主表
        GoodsTransferEntity entity = new GoodsTransferEntity();
        entity.setType(dto.getType());
        entity.setMerchantOutcome(dto.getMerchantOutcome());
        entity.setMerchantIncome(dto.getMerchantIncome());
        entity.setCounterId(dto.getCounterId());
        entity.setRemark(dto.getRemark());
        entity.setCompanyId(SecurityUtils.getCompanyId());
        entity.setStatus(GoodsTransferStatusEnum.PENDING.getValue()); // 待审核
        entity.setCreatedBy(SecurityUtils.getUserId());
        entity.setTransferSn(SnUtils.generateTransferCode());
        this.save(entity);
        // 创建明细
        int totalNum = 0;
        BigDecimal totalWeight = BigDecimal.ZERO;
        BigDecimal totalNetGoldWeight = BigDecimal.ZERO;
        BigDecimal totalNetSilverWeight = BigDecimal.ZERO;
        long totalCostPrice = 0L;
        // 批量查询所有货品信息
        Set<Long> goodsIds = dto.getDetails().stream().map(GoodsTransferCreateDTO.Detail::getId).collect(Collectors.toSet());
        Map<Long, GoodsEntity> goodsMap = goodsIds.isEmpty() ? Collections.emptyMap() : goodsMapper.selectListByIds(goodsIds).stream().collect(Collectors.toMap(GoodsEntity::getId, g -> g));

        List<GoodsTransferDetailEntity> detailEntities = new ArrayList<>();
        for (GoodsTransferCreateDTO.Detail d : dto.getDetails()) {
            if (d.getId() == null || d.getNum() == null || d.getNum() <= 0) {
                CommonUtils.abort("明细货品ID和数量必填且数量大于0");
            }
            GoodsEntity goods = goodsMap.get(d.getId());
            if (goods == null) {
                CommonUtils.abort("货品不存在: " + d.getId());
            }
            if (goods.getStockNum() == null || goods.getStockNum() < d.getNum()) {
                CommonUtils.abort("货品[" + goods.getGoodsSn() + "]库存不足，当前库存: " + (goods.getStockNum() == null ? 0 : goods.getStockNum()));
            }
            GoodsTransferDetailEntity detail = new GoodsTransferDetailEntity();
            detail.setCompanyId(entity.getCompanyId());
            detail.setTransferId(entity.getId());
            detail.setGoodsId(d.getId());
            detail.setNum(d.getNum());
            detailEntities.add(detail);

            totalNum += d.getNum();
            // 统计重量、金重、银重、成本价
            if (goods.getWeight() != null) {
                totalWeight = totalWeight.add(goods.getWeight().multiply(new BigDecimal(d.getNum())));
            }
            if (goods.getNetGoldWeight() != null) {
                totalNetGoldWeight = totalNetGoldWeight.add(goods.getNetGoldWeight().multiply(new BigDecimal(d.getNum())));
            }
            if (goods.getNetSilverWeight() != null) {
                totalNetSilverWeight = totalNetSilverWeight.add(goods.getNetSilverWeight().multiply(new BigDecimal(d.getNum())));
            }
            if (goods.getCostPrice() != null) {
                totalCostPrice += goods.getCostPrice() * d.getNum();
            }
        }

        // 批量插入明细
        if (!detailEntities.isEmpty()) {
            goodsTransferDetailMapper.insertBatchSelective(detailEntities);
        }

        // 更新主表总数量及统计字段
        entity.setNum(totalNum);
        entity.setTotalWeight(totalWeight);
        entity.setTotalNetGoldWeight(totalNetGoldWeight);
        entity.setTotalNetSilverWeight(totalNetSilverWeight);
        entity.setTotalCostPrice(totalCostPrice);
        this.updateById(entity);
        // 处理库存冻结
        List<StockNumChangeBO> stockChanges = new ArrayList<>();
        for (GoodsTransferCreateDTO.Detail d : dto.getDetails()) {
            stockChanges.add(StockNumChangeBO.builder()
                .goodsId(d.getId())
                .stockNum(-d.getNum())   // 库存减少
                .frozenNum(d.getNum())   // 冻结增加
                .comment("创建调拨单")
                .build());
        }
        StockUtils.updateStocks(stockChanges);
        // 操作日志
        OpLogUtils.appendOpLog("调拨单-创建", "创建调拨单: " + entity.getId(), dto);

        // 如果关闭了审核, 则继续执行审核逻辑
        if (!CommonUtils.getCompanySettings(SecurityUtils.getCompanyId()).getTransferAuditEnabled()) {
            auditTransfer(entity.getId().toString(), true);
        }

        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTransfer(GoodsTransferUpdateDTO dto) {
        // 1. 校验
        GoodsTransferEntity entity = this.getById(dto.getId());
        if (entity == null) {
            CommonUtils.abort("调拨单不存在");
        }
        if (!Objects.equals(entity.getStatus(), GoodsTransferStatusEnum.PENDING.getValue())) {
            CommonUtils.abort("仅待审核状态的调拨单可以编辑");
        }
        
        // 门店权限检查
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(entity.getMerchantOutcome()), "无权操作调出门店");
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(entity.getMerchantIncome()), "无权操作调入门店");

        // 2. 更新
        entity.setRemark(dto.getRemark());
        boolean success = this.updateById(entity);

        // 3. 日志
        if (success) {
            OpLogUtils.appendOpLog("调拨单-更新", "更新调拨单: " + entity.getTransferSn(), dto);
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTransfer(Long id) {
        if (id == null || id <= 0) {
            CommonUtils.abort("调拨单ID不能为空");
        }
        GoodsTransferEntity entity = this.getById(id);
        if (entity == null) {
            CommonUtils.abort("调拨单不存在");
        }
        if (!Objects.equals(entity.getStatus(), GoodsTransferStatusEnum.PENDING.getValue())) {
            CommonUtils.abort("仅允许删除待审核状态的调拨单");
        }
        
        // 门店权限检查
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(entity.getMerchantOutcome()), "无权操作调出门店");
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(entity.getMerchantIncome()), "无权操作调入门店");
        
        // 查询所有明细
        List<GoodsTransferDetailEntity> details = goodsTransferDetailMapper.selectListByQuery(
            QueryWrapper.create().where(GoodsTransferDetailEntity::getTransferId).eq(id)
        );
        // 恢复冻结数量
        List<StockNumChangeBO> stockChanges = new ArrayList<>();
        for (GoodsTransferDetailEntity detail : details) {
            stockChanges.add(StockNumChangeBO.builder()
                .goodsId(detail.getGoodsId())
                .stockNum(detail.getNum())      // 库存恢复
                .frozenNum(-detail.getNum())    // 冻结减少
                .comment("删除调拨单")
                .build());
        }
        if (!stockChanges.isEmpty()) {
            StockUtils.updateStocks(stockChanges);
        }
        // 删除明细
        goodsTransferDetailMapper.deleteByQuery(
            QueryWrapper.create().where(GoodsTransferDetailEntity::getTransferId).eq(id)
        );
        // 删除主表
        boolean result = this.removeById(id);
        // 操作日志
        OpLogUtils.appendOpLog("调拨单-删除", "删除调拨单: " + id, null);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditTransfer(String ids) {
        return this.auditTransfer(ids, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditTransfer(String ids, boolean skipLog) {
        if (ids == null || ids.isBlank()) {
            CommonUtils.abort("请选择要审核的调拨单");
        }
        String[] idArr = ids.split(",");
        List<Long> idList = new ArrayList<>();
        for (String idStr : idArr) {
            try {
                idList.add(Long.parseLong(idStr.trim()));
            } catch (NumberFormatException e) {
                CommonUtils.abort("参数格式错误: " + idStr);
            }
        }
        if (idList.isEmpty()) {
            CommonUtils.abort("请选择要审核的调拨单");
        }
        // 查询所有调拨单
        List<GoodsTransferEntity> transferList = this.listByIds(idList);
        if (transferList.size() != idList.size()) {
            CommonUtils.abort("部分调拨单不存在");
        }
        // 校验状态和权限
        for (GoodsTransferEntity entity : transferList) {
            if (!Objects.equals(entity.getStatus(), GoodsTransferStatusEnum.PENDING.getValue())) {
                CommonUtils.abort("仅允许审核待审核状态的调拨单，单号:" + entity.getTransferSn());
            }
            // 门店权限检查
            CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(entity.getMerchantOutcome()), "无权操作调出门店，单号:" + entity.getTransferSn());
            CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(entity.getMerchantIncome()), "无权操作调入门店，单号:" + entity.getTransferSn());
        }
        // 批量处理
        for (GoodsTransferEntity entity : transferList) {
            List<GoodsTransferDetailEntity> details = goodsTransferDetailMapper.selectListByQuery(
                QueryWrapper.create().where(GoodsTransferDetailEntity::getTransferId).eq(entity.getId())
            );
            // 1. 更新原货品库存：扣减原始数量和冻结数量
            List<StockNumChangeBO> stockChanges = new ArrayList<>();
            for (GoodsTransferDetailEntity detail : details) {
                stockChanges.add(StockNumChangeBO.builder()
                    .goodsId(detail.getGoodsId())
                    .num(-detail.getNum())           // 原始数量减少
                    .frozenNum(-detail.getNum())     // 冻结减少
                    .comment("审核调拨单")
                    .build());
            }
            if (!stockChanges.isEmpty()) {
                StockUtils.updateStocks(stockChanges);
            }
            
            // 2. 根据调拨类型处理 (1:门店调拨, 2:柜台调拨)
            if (Objects.equals(entity.getType(), 2)) {
                // 柜台调拨：审核后直接完成，处理目标货品
                processReceiptForCounterTransfer(entity, details, skipLog);
            } else {
                // 门店调拨：审核后状态变为调拨中
                entity.setStatus(GoodsTransferStatusEnum.TRANSFERRING.getValue());
                entity.setAuditBy(SecurityUtils.getUserId());
                entity.setAuditAt(new Date());
                this.updateById(entity);
                // 日志
                if (!skipLog) {
                    OpLogUtils.appendOpLog("调拨单-审核", "审核门店调拨单: " + entity.getTransferSn(), null);
                }
            }
        }
        return true;
    }

    private void processReceiptForCounterTransfer(GoodsTransferEntity entity, List<GoodsTransferDetailEntity> details, boolean skipLog) {
        for (GoodsTransferDetailEntity detail : details) {
            // 查询原货品信息
            GoodsEntity originalGoods = goodsMapper.selectOneById(detail.getGoodsId());
            if (originalGoods == null) {
                CommonUtils.abort("原货品不存在: " + detail.getGoodsId());
            }
    
            // 通过条码+商户ID+柜台ID查询目标货品是否存在
            // 柜台调拨，调入门店就是调出门店
            GoodsEntity targetGoods = goodsMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(GoodsEntity::getGoodsSn).eq(originalGoods.getGoodsSn())
                    .and(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(GoodsEntity::getMerchantId).eq(entity.getMerchantIncome())
                    .and(GoodsEntity::getCounterId).eq(entity.getCounterId())
            );
            
            if (targetGoods == null) {
                // 如果不存在，则复制原货品信息，创建新货品
                targetGoods = createNewGoodsFromTransfer(originalGoods, entity, entity.getCounterId(), detail.getNum());
                goodsMapper.insert(targetGoods);
                
                // 复制自定义字段
                copyGoodsColumns(originalGoods.getId(), targetGoods.getId());
                
                // 复制图片
                copyGoodsImages(originalGoods.getId(), targetGoods.getId());
                
                // 更新明细中的目标货品ID
                detail.setTargetGoodsId(targetGoods.getId());
                goodsTransferDetailMapper.update(detail);
            } else {
                // 如果存在，则增加原始数量和库存数量
                StockNumChangeBO stockChange = StockNumChangeBO.builder()
                        .goodsId(targetGoods.getId())
                        .num(detail.getNum())
                        .stockNum(detail.getNum())
                        .comment("调拨单收货: " + entity.getTransferSn())
                        .build();
                StockUtils.updateStocks(List.of(stockChange));

                // 更新明细中的目标货品ID
                detail.setTargetGoodsId(targetGoods.getId());
                goodsTransferDetailMapper.update(detail);
            }
        }
    
        // 更新调拨单状态为已完成
        entity.setStatus(GoodsTransferStatusEnum.COMPLETED.getValue());
        entity.setAuditBy(SecurityUtils.getUserId());
        entity.setAuditAt(new Date());
        entity.setReceiptBy(SecurityUtils.getUserId());
        entity.setReceiptAt(new Date());
        this.updateById(entity);
        // 日志
        if (!skipLog) {
            OpLogUtils.appendOpLog("调拨单-审核", "审核并完成柜台调拨单: " + entity.getTransferSn(), null);
        }
    }

    /**
     * 从调拨单创建新货品
     */
    private GoodsEntity createNewGoodsFromTransfer(GoodsEntity originalGoods, GoodsTransferEntity transfer, Integer counterId, Integer num) {
        GoodsEntity newGoods = new GoodsEntity();
        
        // 使用 BeanUtils 复制基本信息
        BeanUtils.copyProperties(originalGoods, newGoods);
        
        // 设置调拨相关的特殊字段
        newGoods.setId(null); // 清空ID，让数据库自动生成
        newGoods.setCompanyId(SecurityUtils.getCompanyId());
        newGoods.setMerchantId(transfer.getMerchantIncome());
        newGoods.setCounterId(counterId);
        
        // 设置数量：原始数量和库存数量为调拨数量，其他数量设置为零
        newGoods.setNum(num);
        newGoods.setStockNum(num);
        newGoods.setReturnNum(0);
        newGoods.setSoldNum(0);
        newGoods.setTransferNum(0);
        newGoods.setFrozenNum(0);
        newGoods.setTakeStatus(0);
        
        return newGoods;
    }

    /**
     * 复制货品自定义字段
     */
    private void copyGoodsColumns(Long sourceGoodsId, Long targetGoodsId) {
        List<GoodsHasColumnsEntity> sourceColumns = goodsHasColumnsMapper.selectListByQuery(
            QueryWrapper.create().where(GoodsHasColumnsEntity::getGoodsId).eq(sourceGoodsId)
        );
        
        if (!sourceColumns.isEmpty()) {
            List<GoodsHasColumnsEntity> targetColumns = sourceColumns.stream()
                .map(column -> {
                    GoodsHasColumnsEntity newColumn = new GoodsHasColumnsEntity();
                    newColumn.setCompanyId(SecurityUtils.getCompanyId());
                    newColumn.setGoodsId(targetGoodsId);
                    newColumn.setColumnId(column.getColumnId());
                    newColumn.setColumnSign(column.getColumnSign());
                    newColumn.setValue(column.getValue());
                    newColumn.setImageId(column.getImageId());
                    return newColumn;
                })
                .collect(Collectors.toList());
            
            goodsHasColumnsMapper.insertBatch(targetColumns);
        }
    }

    /**
     * 复制货品图片
     */
    private void copyGoodsImages(Long sourceGoodsId, Long targetGoodsId) {
        List<GoodsHasImagesEntity> sourceImages = goodsHasImagesMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GoodsHasImagesEntity::getGoodsId).eq(sourceGoodsId)
                .orderBy(GoodsHasImagesEntity::getSort, true)
        );
        
        if (!sourceImages.isEmpty()) {
            List<GoodsHasImagesEntity> targetImages = sourceImages.stream()
                .map(image -> {
                    GoodsHasImagesEntity newImage = new GoodsHasImagesEntity();
                    newImage.setCompanyId(SecurityUtils.getCompanyId());
                    newImage.setGoodsId(targetGoodsId);
                    newImage.setImageId(image.getImageId());
                    newImage.setUrl(image.getUrl());
                    newImage.setSort(image.getSort());
                    return newImage;
                })
                .collect(Collectors.toList());
            
            goodsHasImagesMapper.insertBatch(targetImages);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean receiptTransfer(GoodsTransferReceiptDTO dto) {
        // 1. 参数校验
        if (dto.getId() == null || dto.getId() <= 0) {
            CommonUtils.abort("调拨单ID不能为空");
        }
        if (dto.getCounterId() == null || dto.getCounterId() <= 0) {
            CommonUtils.abort("调入柜台不能为空");
        }

        // 2. 查询调拨单
        GoodsTransferEntity transfer = this.getById(dto.getId());
        if (transfer == null) {
            CommonUtils.abort("调拨单不存在");
        }
        if (!Objects.equals(transfer.getCompanyId(), SecurityUtils.getCompanyId())) {
            CommonUtils.abort("无权操作其他商户的调拨单");
        }

        // 3. 校验调拨单状态为"调拨中"
        if (!Objects.equals(transfer.getStatus(), GoodsTransferStatusEnum.TRANSFERRING.getValue())) {
            CommonUtils.abort("仅允许收货调拨中状态的调拨单");
        }

        // 4. 校验权限：当前账户是主账号/拥有"调入门店"
        boolean hasPermission = false;
        
        // 检查是否为主账号
        if (SecurityUtils.isMain()) {
            hasPermission = true;
        } else {
            // 检查是否拥有调入门店的权限
            Set<Long> userMerchantIds = SecurityUtils.getMerchantIds();
            if (userMerchantIds.contains(transfer.getMerchantIncome())) {
                hasPermission = true;
            }
        }
        
        if (!hasPermission) {
            CommonUtils.abort("您没有调入门店的权限，无法进行收货操作");
        }

        // 5. 查询调拨单明细
        List<GoodsTransferDetailEntity> details = goodsTransferDetailMapper.selectListByQuery(
            QueryWrapper.create().where(GoodsTransferDetailEntity::getTransferId).eq(transfer.getId())
        );
        if (details.isEmpty()) {
            CommonUtils.abort("调拨单明细不存在");
        }

        // 6. 处理每个明细的货品
        for (GoodsTransferDetailEntity detail : details) {
            // 查询原货品信息
            GoodsEntity originalGoods = goodsMapper.selectOneById(detail.getGoodsId());
            if (originalGoods == null) {
                CommonUtils.abort("原货品不存在: " + detail.getGoodsId());
            }

            // 通过条码+商户ID+柜台ID查询目标货品是否存在
            GoodsEntity targetGoods = goodsMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(GoodsEntity::getGoodsSn).eq(originalGoods.getGoodsSn())
                    .and(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(GoodsEntity::getMerchantId).eq(transfer.getMerchantIncome())
                    .and(GoodsEntity::getCounterId).eq(dto.getCounterId())
            );

            if (targetGoods == null) {
                // 如果不存在，则复制原货品信息，创建新货品
                targetGoods = createNewGoodsFromTransfer(originalGoods, transfer, dto.getCounterId(), detail.getNum());
                goodsMapper.insert(targetGoods);
                
                // 复制自定义字段
                copyGoodsColumns(originalGoods.getId(), targetGoods.getId());
                
                // 复制图片
                copyGoodsImages(originalGoods.getId(), targetGoods.getId());
                
                // 更新明细中的目标货品ID
                detail.setTargetGoodsId(targetGoods.getId());
                goodsTransferDetailMapper.update(detail);
            } else {
                // 如果存在，则增加原始数量和库存数量
                StockNumChangeBO stockChange = StockNumChangeBO.builder()
                        .goodsId(targetGoods.getId())
                        .num(detail.getNum())
                        .stockNum(detail.getNum())
                        .comment("调拨单收货: " + transfer.getTransferSn())
                        .build();
                StockUtils.updateStocks(List.of(stockChange));

                // 更新明细中的目标货品ID
                detail.setTargetGoodsId(targetGoods.getId());
                goodsTransferDetailMapper.update(detail);
            }
        }

        // 8. 更新调拨单状态为已完成
        transfer.setStatus(GoodsTransferStatusEnum.COMPLETED.getValue());
        transfer.setReceiptBy(SecurityUtils.getUserId());
        transfer.setReceiptAt(new Date());
        transfer.setReceiptRemark(dto.getReceiptRemark());
        this.updateById(transfer);

        // 9. 记录操作日志
        OpLogUtils.appendOpLog("调拨单-收货", "收货调拨单: " + transfer.getTransferSn(), dto);

        return true;
    }

    @Override
    public GoodsTransferInfoVO getTransferInfo(GoodsTransferInfoRequest request) {
        GoodsTransferEntity entity = this.getById(request.getId());
        CommonUtils.abortIf(entity == null, "调拨单不存在");
        
        // 门店权限检查
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(entity.getMerchantOutcome()), "无权操作调出门店");
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(entity.getMerchantIncome()), "无权操作调入门店");
        
        GoodsTransferInfoVO vo = new GoodsTransferInfoVO();
        vo.setTransferSn(entity.getTransferSn());
        vo.setTypeLabel(com.xc.boot.common.base.IBaseEnum.getLabelByValue(entity.getType(), GoodsTransferStatusEnum.class));
        vo.setMerchantOutcomeName(listFillService.getMerchantNameById(Set.of(entity.getMerchantOutcome())).getOrDefault(entity.getMerchantOutcome() + "", ""));
        vo.setStatusLabel(com.xc.boot.common.base.IBaseEnum.getLabelByValue(entity.getStatus(), GoodsTransferStatusEnum.class));
        vo.setMerchantIncomeName(listFillService.getMerchantNameById(Set.of(entity.getMerchantIncome())).getOrDefault(entity.getMerchantIncome() + "", ""));
        vo.setCounterIncomeName(listFillService.getCounterNameById(Set.of(entity.getCounterId())).getOrDefault(entity.getCounterId() + "", ""));
        vo.setNum(entity.getNum());
        vo.setTotalWeight(entity.getTotalWeight());
        vo.setTotalNetGoldWeight(entity.getTotalNetGoldWeight());
        vo.setTotalNetSilverWeight(entity.getTotalNetSilverWeight());
        vo.setTotalCostPrice(entity.getTotalCostPrice() == null ? null : com.xc.boot.common.util.PriceUtil.fen2yuan(entity.getTotalCostPrice()));
        vo.setCreatedByName(listFillService.getUserNameByUserId(Set.of(entity.getCreatedBy())).getOrDefault(entity.getCreatedBy() + "", ""));
        vo.setCreatedAt(entity.getCreatedAt());
        vo.setRemark(entity.getRemark());
        return vo;
    }

    /**
     * 批量填充调拨明细VO的名称、图片等
     */
    private void fillTransferDetailVOs(List<GoodsTransferGoodsVO> voList, List<GoodsEntity> goodsList) {
        if (voList == null || voList.isEmpty() || goodsList == null || goodsList.isEmpty()) return;
        Set<Long> merchantIds = new java.util.HashSet<>();
        Set<Integer> categoryIds = new java.util.HashSet<>();
        Set<Integer> subclassIds = new java.util.HashSet<>();
        Set<Integer> counterIds = new java.util.HashSet<>();
        Set<Integer> supplierIds = new java.util.HashSet<>();
        Set<Integer> qualityIds = new java.util.HashSet<>();
        Set<Integer> styleIds = new java.util.HashSet<>();
        Set<Integer> brandIds = new java.util.HashSet<>();
        Set<Integer> technologyIds = new java.util.HashSet<>();
        Set<Integer> mainStoneIds = new java.util.HashSet<>();
        Set<Integer> subStoneIds = new java.util.HashSet<>();
        Set<Long> goodsIds = new java.util.HashSet<>();
        for (GoodsEntity g : goodsList) {
            if (g.getMerchantId() != null) merchantIds.add(g.getMerchantId());
            if (g.getCategoryId() != null) categoryIds.add(g.getCategoryId());
            if (g.getSubclassId() != null) subclassIds.add(g.getSubclassId());
            if (g.getCounterId() != null) counterIds.add(g.getCounterId());
            if (g.getSupplierId() != null) supplierIds.add(g.getSupplierId());
            if (g.getQualityId() != null) qualityIds.add(g.getQualityId());
            if (g.getStyleId() != null) styleIds.add(g.getStyleId());
            if (g.getBrandId() != null) brandIds.add(g.getBrandId());
            if (g.getTechnologyId() != null) technologyIds.add(g.getTechnologyId());
            if (g.getMainStoneId() != null) mainStoneIds.add(g.getMainStoneId());
            if (g.getSubStoneId() != null) subStoneIds.add(g.getSubStoneId());
            if (g.getId() != null) goodsIds.add(g.getId());
        }
        ListFillUtil.of(voList)
                .build(listFillService::getMerchantNameById, merchantIds, "merchantId", "merchant")
                .build(listFillService::getCategoryNameById, categoryIds, "categoryId", "category")
                .build(listFillService::getSubclassNameById, subclassIds, "subclassId", "subclass")
                .build(listFillService::getCounterNameById, counterIds, "counterId", "counter")
                .build(listFillService::getSupplierNameById, supplierIds, "supplierId", "supplier")
                .build(listFillService::getQualityNameById, qualityIds, "qualityId", "quality")
                .build(listFillService::getStyleNameById, styleIds, "styleId", "style")
                .build(listFillService::getBrandNameById, brandIds, "brandId", "brand")
                .build(listFillService::getTechnologyNameById, technologyIds, "technologyId", "technology")
                .build(listFillService::getJewelryMapperNameById, mainStoneIds, "mainStoneId", "mainStone")
                .build(listFillService::getJewelryMapperNameById, subStoneIds, "subStoneId", "subStone")
                .build(listFillService::getGoodsImgByGoodsId, goodsIds, "id", "images")
                .handle();
    }

    @Override
    public Page<GoodsTransferGoodsVO> pageTransferDetail(GoodsTransferDetailPageQuery query) {
        // 查询调拨单并校验权限
        GoodsTransferEntity transfer = this.getById(query.getTransferId());
        CommonUtils.abortIf(transfer == null, "调拨单不存在");
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(transfer.getMerchantOutcome()), "无权操作调出门店");
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(transfer.getMerchantIncome()), "无权操作调入门店");
        
        QueryWrapper wrapper = QueryWrapper.create()
                .from(GoodsTransferDetailEntity.class)
                .where(GoodsTransferDetailEntity::getTransferId).eq(query.getTransferId());

        // 处理导出
        if (query.getExport() != null && query.getExport() == 1) {
            exportTransferDetails(wrapper, query);
            return null;
        }
        // 处理打印
        if (query.getPrint() != null && query.getPrint() == 1) {
            return printTransferDetails(wrapper, query);
        }

        // 分页查询明细
        Page<GoodsTransferDetailEntity> page = goodsTransferDetailMapper.paginate(
                query.getPageNum(), query.getPageSize(), wrapper);
        List<GoodsTransferDetailEntity> details = page.getRecords();
        if (details.isEmpty()) {
            return new Page<>(query.getPageNum(), query.getPageSize(), 0);
        }
        // 批量查货品
        Set<Long> goodsIds = details.stream().map(GoodsTransferDetailEntity::getGoodsId).collect(Collectors.toSet());
        List<GoodsEntity> goodsList = goodsMapper.selectListByIds(goodsIds);
        Map<Long, GoodsEntity> goodsMap = goodsList.stream().collect(Collectors.toMap(GoodsEntity::getId, g -> g));
        // 转VO
        List<GoodsTransferGoodsVO> voList = new ArrayList<>();
        for (GoodsTransferDetailEntity detail : details) {
            GoodsEntity entity = goodsMap.get(detail.getGoodsId());
            if (entity == null) continue;
            GoodsTransferGoodsVO vo = new GoodsTransferGoodsVO();
            BeanUtils.copyProperties(entity, vo);
            vo.setId(detail.getId());
            vo.setGoodsId(entity.getId());
            vo.setNum(detail.getNum());
            voList.add(vo);
        }
        fillTransferDetailVOs(voList, goodsList);
        Page<GoodsTransferGoodsVO> voPage = new Page<>(page.getPageNumber(), page.getPageSize(), page.getTotalRow());
        voPage.setRecords(voList);
        return voPage;
    }

    private void exportTransferDetails(QueryWrapper wrapper, GoodsTransferDetailPageQuery query) {
        long count = goodsTransferDetailMapper.selectCountByQuery(wrapper);
        CommonUtils.abortIf(count > CommonUtils.getMaxExportSize(), "导出数量超过限制");
        ExcelUtil.of(goodsTransferDetailMapper, wrapper, GoodsTransferGoodsVO.class, "transfer_detail", "调拨单明细")
                .getData((mapper, w) -> {
                    List<GoodsTransferDetailEntity> details = goodsTransferDetailMapper.selectListByQuery(w);
                    if (details.isEmpty()) return List.of();
                    Set<Long> goodsIds = details.stream().map(GoodsTransferDetailEntity::getGoodsId).collect(Collectors.toSet());
                    List<GoodsEntity> goodsList = goodsMapper.selectListByIds(goodsIds);
                    Map<Long, GoodsEntity> goodsMap = goodsList.stream().collect(Collectors.toMap(GoodsEntity::getId, g -> g));
                    List<GoodsTransferGoodsVO> voList = new ArrayList<>();
                    for (GoodsTransferDetailEntity detail : details) {
                        GoodsEntity entity = goodsMap.get(detail.getGoodsId());
                        if (entity == null) continue;
                        GoodsTransferGoodsVO vo = new GoodsTransferGoodsVO();
                        BeanUtils.copyProperties(entity, vo);
                        vo.setNum(detail.getNum());
                        voList.add(vo);
                    }
                    fillTransferDetailVOs(voList, goodsList);
                    return voList;
                })
                .doExport();
    }

    private Page<GoodsTransferGoodsVO> printTransferDetails(QueryWrapper wrapper, GoodsTransferDetailPageQuery query) {
        long count = goodsTransferDetailMapper.selectCountByQuery(wrapper);
        CommonUtils.abortIf(count > CommonUtils.getMaxPrintSize(), "打印数量超过限制");
        List<GoodsTransferDetailEntity> details = goodsTransferDetailMapper.selectListByQuery(wrapper);
        if (details.isEmpty()) {
            return new Page<>(1, 0, 0);
        }
        Set<Long> goodsIds = details.stream().map(GoodsTransferDetailEntity::getGoodsId).collect(Collectors.toSet());
        List<GoodsEntity> goodsList = goodsMapper.selectListByIds(goodsIds);
        Map<Long, GoodsEntity> goodsMap = goodsList.stream().collect(Collectors.toMap(GoodsEntity::getId, g -> g));
        List<GoodsTransferGoodsVO> voList = new ArrayList<>();
        for (GoodsTransferDetailEntity detail : details) {
            GoodsEntity entity = goodsMap.get(detail.getGoodsId());
            if (entity == null) continue;
            GoodsTransferGoodsVO vo = new GoodsTransferGoodsVO();
            BeanUtils.copyProperties(entity, vo);
            vo.setNum(detail.getNum());
            voList.add(vo);
        }
        fillTransferDetailVOs(voList, goodsList);
        Page<GoodsTransferGoodsVO> page = new Page<>(1, voList.size(), count);
        page.setRecords(voList);
        return page;
    }

    /**
     * 维护调拨单主表统计字段
     */
    private void updateTransferStats(Long transferId) {
        // 统计明细
        Row stats = goodsTransferDetailMapper.selectOneByQueryAs(
            QueryWrapper.create()
                .select(
                    QueryMethods.sum(GoodsTransferDetailEntity::getNum).as("total_num"),
                    QueryMethods.sum(QueryMethods.column("weight").multiply(QueryMethods.column("num"))).as("total_weight"),
                    QueryMethods.sum(QueryMethods.column("net_gold_weight").multiply(QueryMethods.column("num"))).as("total_net_gold_weight"),
                    QueryMethods.sum(QueryMethods.column("net_silver_weight").multiply(QueryMethods.column("num"))).as("total_net_silver_weight"),
                    QueryMethods.sum(QueryMethods.column("cost_price").multiply(QueryMethods.column("num"))).as("total_cost_price")
                )
                .from(GoodsTransferDetailEntity.class)
                .leftJoin(GoodsEntity.class).on(GoodsTransferDetailEntity::getGoodsId, GoodsEntity::getId)
                .where(GoodsTransferDetailEntity::getTransferId).eq(transferId),
            Row.class
        );
        GoodsTransferEntity transfer = this.getById(transferId);
        if (transfer == null) return;
        transfer.setNum(stats.getInt("total_num"));
        transfer.setTotalWeight(stats.getBigDecimal("total_weight"));
        transfer.setTotalNetGoldWeight(stats.getBigDecimal("total_net_gold_weight"));
        transfer.setTotalNetSilverWeight(stats.getBigDecimal("total_net_silver_weight"));
        transfer.setTotalCostPrice(stats.getLong("total_cost_price"));
        this.updateById(transfer);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTransferDetail(GoodsTransferDetailUpdateDTO dto) {
        // 1. 查询明细
        GoodsTransferDetailEntity detail = goodsTransferDetailMapper.selectOneById(dto.getId());
        CommonUtils.abortIf(detail == null, "调拨单明细不存在");
        // 2. 查询主表
        GoodsTransferEntity transfer = this.getById(detail.getTransferId());
        CommonUtils.abortIf(transfer == null, "调拨单不存在");
        CommonUtils.abortIf(!Objects.equals(transfer.getStatus(), GoodsTransferStatusEnum.PENDING.getValue()), "仅待审核状态的调拨单明细可以编辑");

        // 门店权限检查
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(transfer.getMerchantOutcome()), "无权操作调出门店");
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(transfer.getMerchantIncome()), "无权操作调入门店");

        // 3. 数量校验
        CommonUtils.abortIf(dto.getNum() == null || dto.getNum() <= 0, "数量必须大于零");

        // 4. 检查库存并更新
        GoodsEntity goods = goodsMapper.selectOneById(detail.getGoodsId());
        CommonUtils.abortIf(goods == null, "货品不存在");
        int quantityChange = dto.getNum() - detail.getNum();
        CommonUtils.abortIf(goods.getStockNum() < quantityChange, "货品[" + goods.getGoodsSn() + "]库存不足");

        if (quantityChange != 0) {
            StockNumChangeBO stockChange = StockNumChangeBO.builder()
                    .goodsId(detail.getGoodsId())
                    .stockNum(-quantityChange)
                    .frozenNum(quantityChange)
                    .comment("更新调拨单明细: " + transfer.getTransferSn())
                    .build();
            StockUtils.updateStocks(List.of(stockChange));
        }

        // 5. 更新明细数量
        detail.setNum(dto.getNum());
        boolean success = goodsTransferDetailMapper.update(detail) > 0;
        // 6. 维护主表统计字段
        updateTransferStats(transfer.getId());
        // 7. 日志
        if (success) {
            OpLogUtils.appendOpLog("调拨单明细-编辑", "编辑调拨单明细: " + detail.getId(), dto);
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTransferDetail(Long id) {
        // 1. 查询明细
        GoodsTransferDetailEntity detail = goodsTransferDetailMapper.selectOneById(id);
        CommonUtils.abortIf(detail == null, "调拨单明细不存在");
        // 2. 查询主表
        GoodsTransferEntity transfer = this.getById(detail.getTransferId());
        CommonUtils.abortIf(transfer == null, "调拨单不存在");
        CommonUtils.abortIf(!Objects.equals(transfer.getStatus(), GoodsTransferStatusEnum.PENDING.getValue()), "仅待审核状态的调拨单明细可以删除");

        // 门店权限检查
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(transfer.getMerchantOutcome()), "无权操作调出门店");
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(transfer.getMerchantIncome()), "无权操作调入门店");

        // 检查是否是最后一条明细
        Long detailCount = goodsTransferDetailMapper.selectCountByQuery(
                QueryWrapper.create().where(GoodsTransferDetailEntity::getTransferId).eq(detail.getTransferId()));
        CommonUtils.abortIf(detailCount <= 1, "当前调拨单仅剩最后一条明细，请直接删除主单");

        // 3. 恢复冻结库存
        StockNumChangeBO stockChange = StockNumChangeBO.builder()
                .goodsId(detail.getGoodsId())
                .stockNum(detail.getNum())
                .frozenNum(-detail.getNum())
                .comment("删除调拨单明细: " + transfer.getTransferSn())
                .build();
        StockUtils.updateStocks(List.of(stockChange));

        // 4. 删除明细
        boolean success = goodsTransferDetailMapper.deleteById(id) > 0;
        // 5. 维护主表统计字段
        updateTransferStats(transfer.getId());
        // 6. 日志
        if (success) {
            OpLogUtils.appendOpLog("调拨单明细-删除", "删除调拨单明细: " + id, id);
        }
        return success;
    }
} 