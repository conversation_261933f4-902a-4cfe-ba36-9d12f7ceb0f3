package com.xc.boot.modules.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateChain;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.enums.PriceColumEnum;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.util.*;
import com.xc.boot.common.util.excel.ExcelUtil;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtil;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.mapper.GoodsOutcomeDetailMapper;
import com.xc.boot.modules.goods.mapper.GoodsOutcomeMapper;
import com.xc.boot.modules.goods.model.bo.StockNumChangeBO;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.goods.model.entity.GoodsOutcomeDetailEntity;
import com.xc.boot.modules.goods.model.entity.GoodsOutcomeEntity;
import com.xc.boot.modules.goods.model.form.*;
import com.xc.boot.modules.goods.model.vo.ReturnDetailPageVo;
import com.xc.boot.modules.goods.model.vo.ReturnGoodsPageVo;
import com.xc.boot.modules.goods.model.vo.ReturnReceiptPageVo;
import com.xc.boot.modules.goods.service.ReturnOutcomeService;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.system.model.vo.CompanySettingsVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.xc.boot.modules.goods.model.entity.table.GoodsOutcomeDetailTableDef.GOODS_OUTCOME_DETAIL;
import static com.xc.boot.modules.goods.model.entity.table.GoodsOutcomeTableDef.GOODS_OUTCOME;
import static com.xc.boot.modules.goods.model.entity.table.GoodsTableDef.GOODS;
import static com.xc.boot.modules.income.model.entity.table.GoodsIncomeDetailTableDef.GOODS_INCOME_DETAIL;

/**
 * <AUTHOR>
 * @ClassName ReturnOutcomeServiceImpl
 * @Date: 2025/6/14 15:56
 * @Description: 描述
 */
@Service
@RequiredArgsConstructor
public class ReturnOutcomeServiceImpl extends ServiceImpl<GoodsOutcomeMapper, GoodsOutcomeEntity> implements ReturnOutcomeService {
    private final GoodsOutcomeDetailMapper outcomeDetailMapper;
    private final GoodsMapper goodsMapper;
    private final ListFillService fillService;

    @Override
    public Page<ReturnGoodsPageVo> selectOutGoods(ReturnGoodsForm form) {
        QueryWrapper wrapper = buildGoodsQueryWrapper(form);
        wrapper.where(GOODS.STOCK_NUM.gt(0));
        wrapper.select(GOODS.ALL_COLUMNS,
                GOODS.STOCK_NUM.as("maxNum"));
        Page<ReturnGoodsPageVo> page = goodsMapper.paginateAs(new Page<>(form.getPageNum(), form.getPageSize()), wrapper, ReturnGoodsPageVo.class);
        fillList(page.getRecords());
        return page;
    }

    @Override
    public Page<ReturnReceiptPageVo> receiptPage(ReturnReceiptForm form) {
        QueryWrapper wrapper = buildReceiptQueryWrapper(form);
        wrapper.orderBy(GoodsOutcomeEntity::getId).desc();
        if (form.getExport() == 1) {
            doExportReceipt(wrapper);
            return new Page<>();
        }
        int pageNum = form.getPageNum();
        int pageSize = form.getPageSize();
        if (form.getPrint() == 1) {
            long count = this.count(wrapper);
            Assert.isTrue(count <= CommonUtils.getMaxPrintSize(), String.format("打印数据条数超出最大限制%d条", CommonUtils.getMaxPrintSize()));
            pageNum = 1;
            pageSize = CommonUtils.getMaxPrintSize();
        }
        Page<ReturnReceiptPageVo> voPage = this.mapper.paginateAs(pageNum, pageSize, wrapper, ReturnReceiptPageVo.class);
        fillReceiptList(voPage.getRecords());
        return voPage;
    }

    @Override
    @Transactional
    public void delete(Long id) {
        GoodsOutcomeEntity goodsOutcome = this.mapper.selectOneByQuery(QueryWrapper.create()
                .where(GOODS_OUTCOME.ID.eq(id))
                .where(GOODS_OUTCOME.COMPANY_ID.eq(SecurityUtils.getCompanyId())));
        Assert.notNull(goodsOutcome, "采购退出库单不存在");
        Assert.isTrue(goodsOutcome.getStatus().equals(0), "采购退出库单已审核，不能删除");
        // 删除出库单
        this.mapper.deleteById(id);

        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(GOODS_OUTCOME_DETAIL.RECEIVE_ID.eq(id))
                .where(GOODS_OUTCOME_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()));
        List<GoodsOutcomeDetailEntity> detailEntities = outcomeDetailMapper.selectListByQuery(queryWrapper);
        // 回退库存
        List<StockNumChangeBO> changeList = new ArrayList<>();
        for (GoodsOutcomeDetailEntity entity : detailEntities) {
            StockNumChangeBO changeBO = StockNumChangeBO.builder()
                    .comment("删除采购退出库单")
                    .frozenNum(-entity.getNum())
                    .stockNum(entity.getNum())
                    .goodsId(entity.getGoodsId())
                    .build();
            changeList.add(changeBO);
            GoodsEntity goodsEntity = new GoodsEntity()
                    .setGoodsSn(entity.getGoodsSn())
                    .setCompanyId(entity.getCompanyId())
                    .setMerchantId(entity.getMerchantId());
            goodsEntity.setId(entity.getGoodsId());
            OpLogUtils.appendGoodsLog("采购退出库-删除出库单", "删除出库单", String.format("""
                    货品sn: %s,
                    冻结数量减少: %d
                    在仓数量增加: %d
                    """, entity.getGoodsSn(), entity.getNum(), entity.getNum()), goodsEntity);
        }
        StockUtils.updateStocks(changeList);
        // 删除出库单详情
        outcomeDetailMapper.deleteByQuery(queryWrapper);
        OpLogUtils.appendOpLog("采购退出库单管理-删除出库单",
                "删除出库单", "出库单号:" + goodsOutcome.getOutcomeCode());
    }

    @Override
    @Transactional
    public void audit(List<Long> ids) {
        List<GoodsOutcomeEntity> outcomeReceipts = this.mapper.selectListByQuery(QueryWrapper.create()
                .where(GOODS_OUTCOME.ID.in(ids))
                .where(GOODS_OUTCOME.MERCHANT_ID.in(SecurityUtils.getMerchantIds(), !SecurityUtils.isMain()))
                .where(GOODS_OUTCOME.COMPANY_ID.eq(SecurityUtils.getCompanyId())));
        Assert.isTrue(CollectionUtil.isNotEmpty(outcomeReceipts), "采购退出库单不存在");
        Date curTime = new Date();
        for (GoodsOutcomeEntity entity : outcomeReceipts) {
            Assert.isTrue(entity.getStatus().equals(0), String.format("采购退出库单: %s已审核，不能重复审核", entity.getOutcomeCode()));
            entity.setAuditBy(SecurityUtils.getUserId());
            entity.setAuditAt(curTime);
            entity.setStatus(1);
        }
        List<Long> receiptIds = outcomeReceipts.stream().map(GoodsOutcomeEntity::getId).toList();
        List<GoodsOutcomeDetailEntity> outcomeDetails = outcomeDetailMapper.selectListByQuery(QueryWrapper.create()
                .where(GOODS_OUTCOME_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOODS_OUTCOME_DETAIL.RECEIVE_ID.in(receiptIds)));
        Map<Long, GoodsOutcomeDetailEntity> goodsIdOutDetailMap = outcomeDetails.stream()
                .collect(Collectors.toMap(GoodsOutcomeDetailEntity::getGoodsId, v -> v));
        Set<Long> goodsIds = goodsIdOutDetailMap.keySet();
        List<GoodsEntity> goodsEntities = goodsMapper.selectListByQuery(QueryWrapper.create()
                .where(GOODS.ID.in(goodsIds))
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId())));
        Assert.isTrue(goodsEntities.size() == outcomeDetails.size(), "未查询到货品");
        List<StockNumChangeBO> changes = new ArrayList<>();
        for (GoodsEntity goodsEntity : goodsEntities) {
            GoodsOutcomeDetailEntity outcomeDetail = goodsIdOutDetailMap.get(goodsEntity.getId());
            Assert.isTrue(outcomeDetail.getNum() <= goodsEntity.getFrozenNum(), "货品冻结数量不足,无法出库");
            StockNumChangeBO changeBO = StockNumChangeBO.builder()
                    .returnNum(outcomeDetail.getNum())
                    .frozenNum(-outcomeDetail.getNum())
                    .goodsId(goodsEntity.getId())
                    .comment("采购退出库")
                    .build();
            changes.add(changeBO);
            OpLogUtils.appendGoodsLog("采购退出库-审核出库单", "审核出库单", String.format("""
                    货品sn: %s,
                    采购退数量增加: %d
                    冻结数量减少: %d
                    """, goodsEntity.getGoodsSn(), outcomeDetail.getNum(), outcomeDetail.getNum()), goodsEntity);
        }
        // 更新库存
        StockUtils.updateStocks(changes);
        // 修改出库单状态
        this.updateBatch(outcomeReceipts);
        // 修改出库明细状态
        UpdateChain.of(GoodsOutcomeDetailEntity.class)
                .set(GOODS_OUTCOME_DETAIL.STATUS, 1)
                .set(GOODS_OUTCOME_DETAIL.AUDIT_BY, SecurityUtils.getUserId())
                .set(GOODS_OUTCOME_DETAIL.AUDIT_AT, curTime)
                .where(GOODS_OUTCOME_DETAIL.RECEIVE_ID.in(receiptIds))
                .where(GOODS_OUTCOME_DETAIL.COMPANY_ID.in(SecurityUtils.getCompanyId()))
                .update();
        OpLogUtils.appendOpLog("采购退出库单管理-审核出库单",
                "审核出库单", "出库单号:" + outcomeReceipts.stream().map(GoodsOutcomeEntity::getOutcomeCode).collect(Collectors.joining(",")));
    }

    @Override
    @Transactional
    public void create(ReturnCreateForm form) {
        List<ReturnCreateForm.ReturnGoods> goods = form.getGoods();
        Map<Long, Integer> goodsIdNumMap = goods.stream().collect(Collectors.toMap(ReturnCreateForm.ReturnGoods::getGoodsId, ReturnCreateForm.ReturnGoods::getNum));
        List<GoodsEntity> goodsList = goodsMapper.selectListByQuery(QueryWrapper.create()
                .where(GOODS.ID.in(goodsIdNumMap.keySet()))
                .where(GOODS.MERCHANT_ID.in(SecurityUtils.getMerchantIds(), !SecurityUtils.isMain()))
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId())));
        Assert.isTrue(goodsList.size() == goods.size(), "未查询到货品");

        List<StockNumChangeBO> changes = new ArrayList<>();
        List<GoodsOutcomeDetailEntity> details = new ArrayList<>();
        BigDecimal totalCostPrice = BigDecimal.ZERO;
        BigDecimal totalWeight = BigDecimal.ZERO;
        BigDecimal totalNetGoldWeight = BigDecimal.ZERO;
        BigDecimal totalNetSilverWeight = BigDecimal.ZERO;
        for (GoodsEntity goodsEntity : goodsList) {
            Integer num = goodsIdNumMap.get(goodsEntity.getId());
            Assert.isTrue(num <= goodsEntity.getStockNum(), "货品库存不足,无法出库");
            // 统计价格、重量
            totalCostPrice = totalCostPrice.add(new BigDecimal(goodsEntity.getCostPrice()).multiply(new BigDecimal(num)));
            totalWeight = totalWeight.add(goodsEntity.getWeight().multiply(new BigDecimal(num)));
            totalNetGoldWeight = totalNetGoldWeight.add(goodsEntity.getNetGoldWeight().multiply(new BigDecimal(num)));
            totalNetSilverWeight = totalNetSilverWeight.add(goodsEntity.getNetSilverWeight().multiply(new BigDecimal(num)));
            // 添加详情列表
            GoodsOutcomeDetailEntity detailEntity = BeanUtil.copyProperties(goodsEntity, GoodsOutcomeDetailEntity.class);
            detailEntity.setStatus(0).setGoodsId(goodsEntity.getId()).setNum(num).setId(null);
            details.add(detailEntity);
            StockNumChangeBO changeBO = StockNumChangeBO.builder()
                    .comment("采购退出库单生成")
                    .frozenNum(num)
                    .stockNum(-num)
                    .goodsId(goodsEntity.getId())
                    .build();
            changes.add(changeBO);
            OpLogUtils.appendGoodsLog("采购退出库-生成出库单", "生成出库单", String.format("""
                    货品sn: %s,
                    冻结数量增加: %d,
                    在仓数量减少: %d
                    """, goodsEntity.getGoodsSn(), num, num), goodsEntity);
        }
        // 生成采购退货单code
        String code = SnUtils.generateOutcomeCode();
        // 保存采购退货单
        int sum = goods.stream().mapToInt(ReturnCreateForm.ReturnGoods::getNum).sum();
        GoodsOutcomeEntity goodsOutcome = new GoodsOutcomeEntity()
                .setStatus(0)
                .setCompanyId(SecurityUtils.getCompanyId())
                .setNum(sum)
                .setMerchantId(form.getMerchantId())
                .setSupplierId(form.getSupplierId())
                .setOutcomeCode(code)
                .setTotalCostPrice(totalCostPrice.longValue())
                .setTotalWeight(totalWeight)
                .setTotalNetGoldWeight(totalNetGoldWeight)
                .setTotalNetSilverWeight(totalNetSilverWeight)
                .setRemark(form.getRemark());
        this.mapper.insertSelective(goodsOutcome);
        // 保存采购退货单详情
        details.forEach(item -> {
            item.setReceiveId(goodsOutcome.getId())
                .setOutcomeCode(code);
        });
        outcomeDetailMapper.insertBatchSelective(details);
        // 更新库存
        StockUtils.updateStocks(changes);
        // 操作日志
        OpLogUtils.appendOpLog("采购退出库单管理-生成出库单",
                "生成出库单", String.format("""
                        出库单号: %s,
                        货品sn: %s
                        """, code, goodsList.stream().map(GoodsEntity::getGoodsSn).collect(Collectors.joining(","))));

        // 如果未启用审核，则直接审核出库单
        CompanySettingsVO settings = CommonUtils.getCompanySettings(SecurityUtils.getCompanyId());
        if (settings != null && !settings.getReturnAuditEnabled()) {
            audit(List.of(goodsOutcome.getId()));
        }
    }

    @Override
    @Transactional
    public void edit(ReturnEditForm form) {
        GoodsOutcomeDetailEntity detail = outcomeDetailMapper.selectOneByQuery(QueryWrapper.create()
                .where(GOODS_OUTCOME_DETAIL.ID.eq(form.getId()))
                .where(GOODS_OUTCOME_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOODS_OUTCOME_DETAIL.MERCHANT_ID.in(SecurityUtils.getMerchantIds(), !SecurityUtils.isMain())));
        if (Objects.isNull(detail)) {
            throw new BusinessException("未查询到采购退货单详情");
        }
        Assert.isTrue(detail.getStatus().equals(0), "该采购退货单已审核,请勿重复操作");
        GoodsEntity goods = goodsMapper.selectOneByQuery(QueryWrapper.create()
                .where(GOODS.ID.eq(detail.getGoodsId()))
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOODS.MERCHANT_ID.in(SecurityUtils.getMerchantIds(), !SecurityUtils.isMain())));
        if (Objects.isNull(goods)) {
            throw new BusinessException("未查询到货品");
        }
        if (form.getNum() > detail.getNum()) {
            Assert.isTrue(goods.getStockNum() >= form.getNum() - detail.getNum(), "货品库存不足,无法修改");
        }else if (form.getNum() < detail.getNum()) {
            Assert.isTrue(goods.getFrozenNum() >= detail.getNum() - form.getNum(), "货品冻结数量不足,无法修改");
        }else {
            throw new BusinessException("修改前后数量相同");
        }
        Integer changeNum = form.getNum() - detail.getNum();
        detail.setNum(form.getNum());
        StockNumChangeBO changeBO = StockNumChangeBO.builder()
                .comment("采购退出库单修改")
                .frozenNum(changeNum)
                .stockNum(-changeNum)
                .goodsId(detail.getGoodsId())
                .build();
        StockUtils.updateStocks(List.of(changeBO));
        outcomeDetailMapper.update(detail);
        // 修改出库单总数
        UpdateChain.of(GoodsOutcomeEntity.class)
                .where(GOODS_OUTCOME.ID.eq(detail.getReceiveId()))
                .where(GOODS_OUTCOME.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .set(GOODS_OUTCOME.NUM, GOODS_OUTCOME.NUM.add(changeNum))
                .set(GOODS_OUTCOME.TOTAL_COST_PRICE, GOODS_OUTCOME.TOTAL_COST_PRICE.add(changeNum * detail.getCostPrice()))
                .set(GOODS_OUTCOME.TOTAL_WEIGHT, GOODS_OUTCOME.TOTAL_WEIGHT.add(new BigDecimal(changeNum).multiply(detail.getWeight())))
                .set(GOODS_OUTCOME.TOTAL_NET_GOLD_WEIGHT, GOODS_OUTCOME.TOTAL_NET_GOLD_WEIGHT.add(new BigDecimal(changeNum).multiply(detail.getNetGoldWeight())))
                .set(GOODS_OUTCOME.TOTAL_NET_SILVER_WEIGHT, GOODS_OUTCOME.TOTAL_NET_SILVER_WEIGHT.add(new BigDecimal(changeNum).multiply(detail.getNetSilverWeight())))
                .update();
        // 操作日志
        OpLogUtils.appendOpLog("采购退出库单管理-修改出库单详情",
                "修改出库单详情", String.format("""
                        出库单号: %s,
                        货品sn: %s,
                        冻结数量修改: %d,
                        在仓数量修改: %d
                        """, detail.getOutcomeCode(), detail.getGoodsSn(), changeNum, -changeNum));
        // 货品日志
        OpLogUtils.appendGoodsLog("采购退出库-修改出库单详情", "修改出库单详情", String.format("""
                    货品sn: %s,
                    冻结数量修改: %d,
                    在仓数量修改: %d
                    """, detail.getGoodsSn(), changeNum, -changeNum), goods);
    }

    @Override
    public Page<ReturnDetailPageVo> detailPage(ReturnReceiptDetailForm form) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .leftJoin(GOODS).on(GOODS.ID.eq(GOODS_OUTCOME_DETAIL.GOODS_ID))
                .where(GOODS_OUTCOME_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOODS_OUTCOME_DETAIL.MERCHANT_ID.in(SecurityUtils.getMerchantIds(), !SecurityUtils.isMain()))
                .where(GOODS_OUTCOME_DETAIL.RECEIVE_ID.eq(form.getId()))
                .select(GOODS_OUTCOME_DETAIL.ALL_COLUMNS,
                        GOODS.STOCK_NUM.as("stockNum"));
        if (CollectionUtil.isNotEmpty(form.getIds())) {
            queryWrapper.where(GOODS_OUTCOME_DETAIL.ID.in(form.getIds()));
        }
        if (form.getExport().equals(1)) {
            doExportDetail(queryWrapper);
            return new Page<>();
        }
        int pageNum = form.getPageNum();
        int pageSize = form.getPageSize();
        if (form.getPrint().equals(1)) {
            long count = outcomeDetailMapper.selectCountByQuery(queryWrapper);
            Assert.isTrue(count <= CommonUtils.getMaxPrintSize(), String.format("打印数据条数超出最大限制%d条", CommonUtils.getMaxPrintSize()));
            pageNum = 1;
            pageSize = CommonUtils.getMaxPrintSize();
        }
        Page<ReturnDetailPageVo> page = outcomeDetailMapper.paginateAs(pageNum, pageSize, queryWrapper, ReturnDetailPageVo.class);
        List<ReturnDetailPageVo> records = page.getRecords();
        fillDetailList(records);
        return page;
    }

    @Override
    public void deleteDetail(Long id) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(GOODS_OUTCOME_DETAIL.ID.eq(id))
                .where(GOODS_OUTCOME_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId()));
        GoodsOutcomeDetailEntity entity = outcomeDetailMapper.selectOneByQuery(queryWrapper);
        Assert.notNull(entity, "明细不存在");
        long count = outcomeDetailMapper.selectCountByQuery(QueryWrapper.create()
                .where(GOODS_OUTCOME_DETAIL.RECEIVE_ID.eq(entity.getReceiveId()))
                .where(GOODS_OUTCOME_DETAIL.COMPANY_ID.eq(SecurityUtils.getCompanyId())));
        Assert.isTrue(count > 1, "该出库单下已无其他明细,无法删除");
        // 回退库存
        List<StockNumChangeBO> changeList = new ArrayList<>();
        Assert.isTrue(entity.getStatus().equals(0), "该明细已审核无法删除");
        StockNumChangeBO changeBO = StockNumChangeBO.builder()
                .comment("删除采购退出库单")
                .frozenNum(-entity.getNum())
                .stockNum(entity.getNum())
                .goodsId(entity.getGoodsId())
                .build();
        changeList.add(changeBO);
        GoodsEntity goodsEntity = new GoodsEntity()
                .setGoodsSn(entity.getGoodsSn())
                .setCompanyId(entity.getCompanyId())
                .setMerchantId(entity.getMerchantId());
        goodsEntity.setId(entity.getGoodsId());
        OpLogUtils.appendGoodsLog("采购退出库-删除出库单明细", "删除出库单明细", String.format("""
                货品sn: %s,
                冻结数量减少: %d
                在仓数量增加: %d
                """, entity.getGoodsSn(), entity.getNum(), entity.getNum()), goodsEntity);
        StockUtils.updateStocks(changeList);
        // 删除出库单详情
        outcomeDetailMapper.deleteByQuery(queryWrapper);
        OpLogUtils.appendOpLog("采购退出库单管理-删除出库单明细",
                "删除出库单明细", "货品条码:" + entity.getGoodsSn());
    }

    @Override
    public ReturnReceiptPageVo receiptDetail(Long id) {
        ReturnReceiptPageVo vo = this.mapper.selectOneByQueryAs(QueryWrapper.create()
                .where(GOODS_OUTCOME.ID.eq(id))
                .where(GOODS_OUTCOME.COMPANY_ID.eq(SecurityUtils.getCompanyId())), ReturnReceiptPageVo.class);
        Asserts.notNull(vo, "未找到采购退出库单");
        fillReceiptList(List.of(vo));
        return vo;
    }


    private QueryWrapper buildReceiptQueryWrapper(ReturnReceiptForm form) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(GOODS_OUTCOME.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOODS_OUTCOME.MERCHANT_ID.in(SecurityUtils.getMerchantIds(), !SecurityUtils.isMain()));

        queryWrapper.where(GOODS_OUTCOME.STATUS.eq(form.getStatus(), form.getStatus() != null));
        queryWrapper.where(GOODS_OUTCOME.OUTCOME_CODE.eq(form.getOutcomeCode(), StringUtils.isNotBlank(form.getOutcomeCode())));
        if (StringUtils.isNotBlank(form.getCreatedBy())) {
            queryWrapper.where(GOODS_OUTCOME.CREATED_BY.in(List.of(form.getCreatedBy().split(","))));
        }
        if (StringUtils.isNotBlank(form.getAuditBy())) {
            queryWrapper.where(GOODS_OUTCOME.AUDIT_BY.in(List.of(form.getAuditBy().split(","))));
        }
        if (StringUtils.isNotBlank(form.getSupplierIds())) {
            queryWrapper.where(GOODS_OUTCOME.SUPPLIER_ID.in(List.of(form.getSupplierIds().split(","))));
        }
        if (StringUtils.isNotBlank(form.getMerchantIds())) {
            queryWrapper.where(GOODS_OUTCOME.MERCHANT_ID.in(List.of(form.getMerchantIds().split(","))));
        }
        if (form.getCreatedTimeRange() != null) {
            queryWrapper.where(GOODS_OUTCOME.CREATED_AT.between(form.getCreatedTimeRange()));
        }
        if (form.getAuditTimeRange() != null) {
            queryWrapper.where(GOODS_OUTCOME.AUDIT_AT.between(form.getAuditTimeRange()));
        }
        if (CollectionUtil.isNotEmpty(form.getIds())) {
            queryWrapper.where(GOODS_OUTCOME.ID.in(form.getIds()));
        }
        return queryWrapper;
    }

    private QueryWrapper buildGoodsQueryWrapper(ReturnGoodsForm form) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(GOODS.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOODS.MERCHANT_ID.eq(form.getMerchantId()))
                .where(GOODS.SUPPLIER_ID.eq(form.getSupplierId()))
                .where(GOODS.STOCK_NUM.gt(0));
        if (form.getType().equals(1)) {
            queryWrapper.where(GOODS.GOODS_SN.eq(form.getKeyword()));
        } else if (form.getType().equals(2)) {
            queryWrapper.where(GOODS.NAME.like(form.getKeyword()));
        } else if (form.getType().equals(3)) {
            queryWrapper.where(QueryMethods.exists(QueryWrapper.create().from(GOODS_INCOME_DETAIL)
                    .where(GOODS_INCOME_DETAIL.GOODS_ID.eq(GOODS.ID))
                    .where(GOODS_INCOME_DETAIL.INCOME_CODE.eq(form.getKeyword()))
                    .where(GOODS_INCOME_DETAIL.STATUS.eq(1))));
        }
        return queryWrapper;
    }

    private void fillList(List<ReturnGoodsPageVo> list) {
        if (list.isEmpty()) {
            return;
        }
        Set<Long> brandIds = new HashSet<>(List.of(0L));
        Set<Long> counterIds = new HashSet<>(List.of(0L));
        Set<Long> supplierIds = new HashSet<>(List.of(0L));
        Set<Long> merchantIds = new HashSet<>(List.of(0L));
        Set<Long> styleIds = new HashSet<>(List.of(0L));
        Set<Long> subclassIds = new HashSet<>(List.of(0L));
        Set<Long> qualityIds = new HashSet<>(List.of(0L));
        Set<Long> technologyIds = new HashSet<>(List.of(0L));
        Set<Long> mainStoneIds = new HashSet<>(List.of(0L));
        Set<Long> subStoneIds = new HashSet<>(List.of(0L));
        Set<Long> ids = new HashSet<>(List.of(0L));
        for (ReturnGoodsPageVo vo : list) {
            ids.add(vo.getId());
            merchantIds.add(vo.getMerchantId());
            counterIds.add(vo.getCounterId());
            supplierIds.add(vo.getSupplierId());
            subclassIds.add(vo.getSubclassId());
            brandIds.add(vo.getBrandId());
            styleIds.add(vo.getStyleId());
            qualityIds.add(vo.getQualityId());
            technologyIds.add(vo.getTechnologyId());
            mainStoneIds.add(vo.getMainStoneId());
            subStoneIds.add(vo.getSubStoneId());
        }
        ListFillUtil.of(list)
                .build(fillService::getMerchantNameById, merchantIds, "merchantId", "merchant")
                .build(fillService::getCounterNameById, counterIds, "counterId", "counter")
                .build(fillService::getSupplierNameById, supplierIds, "supplierId", "supplier")
                .build(fillService::getCategoryNameById, null, "categoryId", "category")
                .build(fillService::getSubclassNameById, subclassIds, "subclassId", "subclass")
                .build(fillService::getBrandNameById, brandIds, "brandId", "brand")
                .build(fillService::getStyleNameById, styleIds, "styleId", "style")
                .build(fillService::getQualityNameById, qualityIds, "qualityId", "quality")
                .build(fillService::getTechnologyNameById, technologyIds, "technologyId", "technology")
                .build(fillService::getJewelryMapperNameById, mainStoneIds, "mainStoneId", "mainStone")
                .build(fillService::getJewelryMapperNameById, subStoneIds, "subStoneId", "subStone")
                .build(fillService::getColumnVosById, ids, "id", "customerColumns")
                .build(fillService::getGoodsImgByGoodsId, ids, "id", "image")
                .peek(obj -> {
                    ReturnGoodsPageVo vo = (ReturnGoodsPageVo) obj;
                    vo.setNum(vo.getMaxNum());
                    vo.setCostPrice(PriceUtil.fen2yuan(vo.getCostPrice()));
                    vo.setGoldPrice(PriceUtil.fen2yuan(vo.getGoldPrice()));
                    vo.setSilverPrice(PriceUtil.fen2yuan(vo.getSilverPrice()));
                    vo.setWorkPrice(PriceUtil.fen2yuan(vo.getWorkPrice()));
                    vo.setCertPrice(PriceUtil.fen2yuan(vo.getCertPrice()));
                    vo.setSaleWorkPrice(PriceUtil.fen2yuan(vo.getSaleWorkPrice()));
                    vo.setTagPrice(PriceUtil.fen2yuan(vo.getTagPrice()));
                    if (vo.getImage() == null) {
                        vo.setImage(new ArrayList<>());
                    }
                    if (vo.getCustomerColumns() == null) {
                        vo.setCustomerColumns(new ArrayList<>());
                    }
                    String salesType = vo.getSalesType();
                    if (StringUtils.isNotBlank(salesType)) {
                        vo.setSalesType(salesType.equals("1") ? "按重量" : "按数量");
                    }
                })
                .handle();
    }

    private void fillReceiptList(List<ReturnReceiptPageVo> records) {
        if (records.isEmpty()) {
            return;
        }
        Set<Long> userIds = new HashSet<>(List.of(0L));
        Set<Long> merchantIds = new HashSet<>(List.of(0L));
        Set<Long> supplierIds = new HashSet<>(List.of(0L));
        for (ReturnReceiptPageVo vo : records) {
            userIds.add(vo.getAuditBy().longValue());
            userIds.add(vo.getCreatedBy().longValue());
            merchantIds.add(vo.getMerchantId());
            supplierIds.add(vo.getSupplierId());
        }

        ListFillUtil.of(records)
                .build(fillService::getUserNameByUserId, userIds, "auditBy", "auditByName")
                .build(fillService::getUserNameByUserId, userIds, "createdBy", "createdByName")
                .build(fillService::getMerchantNameById, merchantIds, "merchantId", "merchant")
                .build(fillService::getSupplierNameById, supplierIds, "supplierId", "supplier")
                .peek(obj -> {
                    ReturnReceiptPageVo vo = (ReturnReceiptPageVo) obj;
                    vo.setTotalCostPrice(PriceUtil.fen2yuanString(vo.getTotalCostPrice()));
                    GoodsColumnEntity column = CommonUtils.getGoodsColumnsBySign(PriceColumEnum.COST_PRICE.getSign());
                    vo.setTotalCostPrice(ColumnEncryptUtil.handleEncryptPrice(vo.getTotalCostPrice(), column));
                })
                .handle();
    }

    private void fillDetailList(List<ReturnDetailPageVo> records) {
        if (records.isEmpty()) {
            return;
        }
        Set<Long> brandIds = new HashSet<>(List.of(0L));
        Set<Long> counterIds = new HashSet<>(List.of(0L));
        Set<Long> supplierIds = new HashSet<>(List.of(0L));
        Set<Long> merchantIds = new HashSet<>(List.of(0L));
        Set<Long> styleIds = new HashSet<>(List.of(0L));
        Set<Long> subclassIds = new HashSet<>(List.of(0L));
        Set<Long> qualityIds = new HashSet<>(List.of(0L));
        Set<Long> technologyIds = new HashSet<>(List.of(0L));
        Set<Long> mainStoneIds = new HashSet<>(List.of(0L));
        Set<Long> subStoneIds = new HashSet<>(List.of(0L));
        Set<Long> goodsIds = new HashSet<>(List.of(0L));
        for (ReturnDetailPageVo vo : records) {
            goodsIds.add(vo.getGoodsId());
            merchantIds.add(vo.getMerchantId());
            counterIds.add(vo.getCounterId());
            supplierIds.add(vo.getSupplierId());
            subclassIds.add(vo.getSubclassId());
            brandIds.add(vo.getBrandId());
            styleIds.add(vo.getStyleId());
            qualityIds.add(vo.getQualityId());
            technologyIds.add(vo.getTechnologyId());
            mainStoneIds.add(vo.getMainStoneId());
            subStoneIds.add(vo.getSubStoneId());
        }
        ListFillUtil.of(records)
                .build(fillService::getMerchantNameById, merchantIds, "merchantId", "merchant")
                .build(fillService::getCounterNameById, counterIds, "counterId", "counter")
                .build(fillService::getSupplierNameById, supplierIds, "supplierId", "supplier")
                .build(fillService::getCategoryNameById, null, "categoryId", "category")
                .build(fillService::getSubclassNameById, subclassIds, "subclassId", "subclass")
                .build(fillService::getBrandNameById, brandIds, "brandId", "brand")
                .build(fillService::getStyleNameById, styleIds, "styleId", "style")
                .build(fillService::getQualityNameById, qualityIds, "qualityId", "quality")
                .build(fillService::getTechnologyNameById, technologyIds, "technologyId", "technology")
                .build(fillService::getJewelryMapperNameById, mainStoneIds, "mainStoneId", "mainStone")
                .build(fillService::getJewelryMapperNameById, subStoneIds, "subStoneId", "subStone")
                .build(fillService::getColumnVosById, goodsIds, "goodsId", "customerColumns")
                .build(fillService::getGoodsImgByGoodsId, goodsIds, "goodsId", "image")
                .peek(obj -> {
                    ReturnDetailPageVo vo = (ReturnDetailPageVo) obj;
                    vo.setCostPrice(PriceUtil.fen2yuan(vo.getCostPrice()));
                    vo.setGoldPrice(PriceUtil.fen2yuan(vo.getGoldPrice()));
                    vo.setSilverPrice(PriceUtil.fen2yuan(vo.getSilverPrice()));
                    vo.setWorkPrice(PriceUtil.fen2yuan(vo.getWorkPrice()));
                    vo.setCertPrice(PriceUtil.fen2yuan(vo.getCertPrice()));
                    vo.setSaleWorkPrice(PriceUtil.fen2yuan(vo.getSaleWorkPrice()));
                    vo.setTagPrice(PriceUtil.fen2yuan(vo.getTagPrice()));
                    vo.setMaxNum(vo.getNum() + vo.getStockNum());
                    if (vo.getImage() == null) {
                        vo.setImage(new ArrayList<>());
                    }
                    if (vo.getCustomerColumns() == null) {
                        vo.setCustomerColumns(new ArrayList<>());
                    }
                    if (StringUtils.isNotBlank(vo.getSalesType())) {
                        vo.setSalesType(vo.getSalesType().equals("1") ? "按重量" : "按数量");
                    }
                })
                .handle();
    }

    private void doExportReceipt(QueryWrapper wrapper) {
        ExcelUtil.of(this.mapper, wrapper, ReturnReceiptPageVo.class, "return_outcome_receipt",  "采购退出库单")
                .getData((mapper, queryWrapper) -> {
                    if (mapper instanceof GoodsOutcomeMapper outcomeMapper) {
                        List<ReturnReceiptPageVo> voList = outcomeMapper.selectListByQueryAs(queryWrapper, ReturnReceiptPageVo.class);
                        fillReceiptList(voList);
                        return voList;
                    }
                    return new ArrayList<>();
                })
                .doExport();
    }

    private void doExportDetail(QueryWrapper queryWrapper) {
        ExcelUtil.of(outcomeDetailMapper, queryWrapper, ReturnDetailPageVo.class, "return_outcome_detail",  "采购退出库单明细")
                .getData((mapper, wrapper) -> {
                    List<ReturnDetailPageVo> vos = mapper.selectListByQueryAs(wrapper, ReturnDetailPageVo.class);
                    fillDetailList(vos);
                    List<JSONObject> encrypt = ColumnEncryptUtil.encrypt(vos, ReturnDetailPageVo.class, "customerColumns");
                    ColumnEncryptUtil.handleJsonImageExport(encrypt);
                    return encrypt;
                }).doExport();
    }
}
