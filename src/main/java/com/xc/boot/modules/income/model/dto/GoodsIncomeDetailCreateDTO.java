package com.xc.boot.modules.income.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

import com.xc.boot.common.base.CustomColumnItemDTO;
import com.xc.boot.common.base.FileItemDTO;

@Data
@Schema(description = "入库单明细创建DTO")
public class GoodsIncomeDetailCreateDTO {
    
    @Schema(description = "货品ID")
    @NotNull(message = "货品ID不能为空")
    private Integer goodsId;
    
    @Schema(description = "所属柜台ID")
    @NotNull(message = "所属柜台ID不能为空")
    private Integer counterId;
    
    @Schema(description = "所属大类ID")
    @NotNull(message = "所属大类ID不能为空")
    private Integer categoryId;
    
    @Schema(description = "所属小类ID")
    @NotNull(message = "所属小类ID不能为空")
    private Integer subclassId;
    
    @Schema(description = "品牌ID")
    private Integer brandId;
    
    @Schema(description = "款式ID")
    private Integer styleId;
    
    @Schema(description = "成色ID")
    @NotNull(message = "成色ID不能为空")
    private Integer qualityId;
    
    @Schema(description = "工艺ID")
    private Integer technologyId;
    
    @Schema(description = "主石ID")
    private Integer mainStoneId;
    
    @Schema(description = "辅石ID")
    private Integer subStoneId;
    
    @Schema(description = "货品条码")
    @NotNull(message = "货品条码不能为空")
    private String goodsSn;
    
    @Schema(description = "货品名称")
    @NotNull(message = "货品名称不能为空")
    private String name;
    
    @Schema(description = "销售方式(1:按重量,2:按数量)")
    @NotNull(message = "销售方式不能为空")
    private Integer salesType;
    
    @Schema(description = "批次号")
    private String batchNo;
    
    @Schema(description = "证书号")
    private String certNo;
    
    @Schema(description = "备注")
    private String remark;
    
    @Schema(description = "重量(g)")
    private BigDecimal weight;
    
    @Schema(description = "净金重(g)")
    private BigDecimal netGoldWeight;
    
    @Schema(description = "净银重(g)")
    private BigDecimal netSilverWeight;
    
    @Schema(description = "主石数")
    private Integer mainStoneCount;
    
    @Schema(description = "主石重(ct)")
    private BigDecimal mainStoneWeight;
    
    @Schema(description = "辅石数")
    private Integer subStoneCount;
    
    @Schema(description = "辅石重(ct)")
    private BigDecimal subStoneWeight;
    
    @Schema(description = "圈口")
    private String circleSize;
    
    @Schema(description = "成本单价(元)")
    @NotNull(message = "成本单价不能为空")
    private BigDecimal costPrice;
    
    @Schema(description = "金进单价(元)")
    private BigDecimal goldPrice;
    
    @Schema(description = "银进单价(元)")
    private BigDecimal silverPrice;
    
    @Schema(description = "进工费单价(元)")
    private BigDecimal workPrice;
    
    @Schema(description = "证书费(元)")
    private BigDecimal certPrice;
    
    @Schema(description = "工费单价(元)")
    private BigDecimal saleWorkPrice;
    
    @Schema(description = "标签单价(元)")
    private BigDecimal tagPrice;
    
    @Schema(description = "数量")
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量必须大于零")
    private Integer num;

    @Schema(description = "自定义列")
    @Valid
    private List<CustomColumnItemDTO> customColumn;

    @Schema(description = "图片")
    @Valid
    private List<FileItemDTO> image;
} 