package com.xc.boot.modules.income.model.vo;

import com.xc.boot.common.annotation.GoodsColumn;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

import com.xc.boot.common.base.CustomColumnItemDTO;
import com.xc.boot.common.base.FileItemDTO;

@Data
@Schema(description = "入库单明细VO")
public class GoodsIncomeDetailVO {
    @Schema(description = "入库单明细ID")
    @GoodsColumn(value = "id")
    private Integer id;
    
    @Schema(description = "货品ID")
    @GoodsColumn(value = "goods_id")
    private Integer goodsId;
    
    @Schema(description = "所属柜台ID")
    @GoodsColumn(value = "counter_id")
    private Integer counterId;
    
    @Schema(description = "所属大类ID")
    @GoodsColumn(value = "category_id")
    private Integer categoryId;
    
    @Schema(description = "所属小类ID")
    @GoodsColumn(value = "subclass_id")
    private Integer subclassId;
    
    @Schema(description = "品牌ID")
    @GoodsColumn(value = "brand_id")
    private Integer brandId;
    
    @Schema(description = "款式ID")
    @GoodsColumn(value = "style_id")
    private Integer styleId;
    
    @Schema(description = "成色ID")
    @GoodsColumn(value = "quality_id")
    private Integer qualityId;
    
    @Schema(description = "工艺ID")
    @GoodsColumn(value = "technology_id")
    private Integer technologyId;
    
    @Schema(description = "主石ID")
    @GoodsColumn(value = "main_stone_id")
    private Integer mainStoneId;
    
    @Schema(description = "辅石ID")
    @GoodsColumn(value = "sub_stone_id")
    private Integer subStoneId;
    
    @Schema(description = "货品条码")
    @GoodsColumn(value = "goods_sn")
    private String goodsSn;
    
    @Schema(description = "货品名称")
    @GoodsColumn(value = "name")
    private String name;
    
    @Schema(description = "销售方式(1:按重量,2:按数量)")
    @GoodsColumn(value = "sales_type")
    private String salesType;

    @Schema(description = "销售方式")
    private String salesTypeName;

    // getter
    public String getSalesTypeName() {
        return salesType.equals("1") ? "按重量" : "按数量";
    }
    
    @Schema(description = "批次号")
    @GoodsColumn(value = "batch_no")
    private String batchNo;
    
    @Schema(description = "证书号")
    @GoodsColumn(value = "cert_no")
    private String certNo;
    
    @Schema(description = "备注")
    @GoodsColumn(value = "remark")
    private String remark;
    
    @Schema(description = "重量(g)")
    @GoodsColumn(value = "weight")
    private BigDecimal weight;
    
    @Schema(description = "净金重(g)")
    @GoodsColumn(value = "net_gold_weight")
    private BigDecimal netGoldWeight;
    
    @Schema(description = "净银重(g)")
    @GoodsColumn(value = "net_silver_weight")
    private BigDecimal netSilverWeight;
    
    @Schema(description = "主石数")
    @GoodsColumn(value = "main_stone_count")
    private Integer mainStoneCount;
    
    @Schema(description = "主石重(ct)")
    @GoodsColumn(value = "main_stone_weight")
    private BigDecimal mainStoneWeight;
    
    @Schema(description = "辅石数")
    @GoodsColumn(value = "sub_stone_count")
    private Integer subStoneCount;
    
    @Schema(description = "辅石重(ct)")
    @GoodsColumn(value = "sub_stone_weight")
    private BigDecimal subStoneWeight;
    
    @Schema(description = "圈口")
    @GoodsColumn(value = "circle_size")
    private String circleSize;
    
    @Schema(description = "成本单价(元)")
    @GoodsColumn(value = "cost_price")
    private BigDecimal costPrice;
    
    @Schema(description = "金进单价(元)")
    @GoodsColumn(value = "gold_price")
    private BigDecimal goldPrice;
    
    @Schema(description = "银进单价(元)")
    @GoodsColumn(value = "silver_price")
    private BigDecimal silverPrice;
    
    @Schema(description = "进工费单价(元)")
    @GoodsColumn(value = "work_price")
    private BigDecimal workPrice;
    
    @Schema(description = "证书费(元)")
    @GoodsColumn(value = "cert_price")
    private BigDecimal certPrice;
    
    @Schema(description = "工费单价(元)")
    @GoodsColumn(value = "sale_work_price")
    private BigDecimal saleWorkPrice;
    
    @Schema(description = "标签单价(元)")
    @GoodsColumn(value = "tag_price")
    private BigDecimal tagPrice;
    
    @Schema(description = "数量")
    @GoodsColumn(value = "num")
    private Integer num;

    @Schema(description = "自定义列")
    private List<CustomColumnItemDTO> customColumn;

    @Schema(description = "图片")
    @GoodsColumn(value = "image")
    private List<FileItemDTO> images;
} 