package com.xc.boot.modules.income.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import com.xc.boot.common.annotation.GoodsColumn;

/**
 * 入库单分页结果对象
 */
@Data
@Schema(description = "入库单分页结果对象")
public class GoodsIncomePageVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "所属门店ID")
    private Integer merchantId;

    @Schema(description = "所属门店")
    @GoodsColumn(value = "merchant_id")
    private String merchantName;

    @Schema(description = "入库单号")
    private String incomeCode;

    @Schema(description = "供应商ID")
    private Integer supplierId;

    @Schema(description = "供应商")
    @GoodsColumn(value = "supplier_id")
    private String supplierName;

    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "总重量(g)")
    @GoodsColumn(value = "weight")
    private BigDecimal totalWeight;

    @Schema(description = "总金重(g)")
    @GoodsColumn(value = "net_gold_weight")
    private BigDecimal totalNetGoldWeight;

    @Schema(description = "总银重(g)")
    @GoodsColumn(value = "net_silver_weight")
    private BigDecimal totalNetSilverWeight;

    @Schema(description = "总成本价")
    @GoodsColumn(value = "cost_price")
    private BigDecimal totalCostPrice;

    @Schema(description = "状态(0:待审核,1:已审核)")
    private Integer status;

    @Schema(description = "入库模板ID")
    private Integer templateId;

    @Schema(description = "入库模板")
    private String templateName;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建人ID")
    private Integer createdBy;

    @Schema(description = "创建人")
    private String createdByName;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "审核人ID")
    private Integer auditBy;

    @Schema(description = "审核人")
    private String auditByName;

    @Schema(description = "审核时间")
    private Date auditAt;
} 