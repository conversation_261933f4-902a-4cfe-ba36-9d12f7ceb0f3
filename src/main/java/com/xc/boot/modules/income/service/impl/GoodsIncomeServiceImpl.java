package com.xc.boot.modules.income.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.util.ColumnEncryptUtil;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.excel.ExcelUtil;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.common.util.SnUtils;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtil;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.income.mapper.GoodsIncomeMapper;
import com.xc.boot.modules.income.mapper.GoodsIncomeDetailMapper;
import com.xc.boot.modules.income.mapper.GoodsIncomeHasColumnsMapper;
import com.xc.boot.modules.income.mapper.GoodsIncomeHasImagesMapper;
import com.xc.boot.modules.income.model.dto.GoodsIncomeCreateDTO;
import com.xc.boot.modules.income.model.dto.GoodsIncomeDetailCreateDTO;
import com.xc.boot.modules.income.model.dto.GoodsIncomeDetailUpdateDTO;
import com.xc.boot.modules.income.model.dto.GoodsSnCheckDTO;
import com.xc.boot.modules.income.model.entity.GoodsIncomeDetailEntity;
import com.xc.boot.modules.income.model.entity.GoodsIncomeEntity;
import com.xc.boot.modules.income.model.entity.GoodsIncomeHasColumnsEntity;
import com.xc.boot.modules.income.model.entity.GoodsIncomeHasImagesEntity;
import com.xc.boot.modules.income.model.query.GoodsIncomePageQuery;
import com.xc.boot.modules.income.model.query.GoodsIncomeDetailPageQuery;
import com.xc.boot.modules.income.model.vo.GoodsIncomePageVO;
import com.xc.boot.modules.income.model.vo.GoodsIncomeInfoVO;
import com.xc.boot.modules.income.model.vo.GoodsIncomeDetailPageVO;
import com.xc.boot.modules.income.model.vo.GoodsSnCheckVO;
import com.xc.boot.modules.income.service.GoodsIncomeAuditService;
import com.xc.boot.modules.income.service.GoodsIncomeService;
import com.xc.boot.modules.goods.mapper.GoodsHasColumnsMapper;
import com.xc.boot.modules.goods.mapper.GoodsHasImagesMapper;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.goods.model.entity.GoodsHasColumnsEntity;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import com.xc.boot.common.base.FileItemDTO;
import com.xc.boot.modules.merchant.mapper.CounterMapper;
import com.xc.boot.modules.merchant.mapper.GoodsIncomeTemplateDetailMapper;
import com.xc.boot.modules.merchant.model.entity.CounterEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateDetailEntity;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import com.mybatisflex.core.row.Row;
import com.xc.boot.common.util.QueryUtils;

import static com.xc.boot.modules.merchant.model.entity.table.CounterTableDef.COUNTER;

@Service
@RequiredArgsConstructor
public class GoodsIncomeServiceImpl extends ServiceImpl<GoodsIncomeMapper, GoodsIncomeEntity>
        implements GoodsIncomeService {

    private final GoodsIncomeDetailMapper goodsIncomeDetailMapper;
    private final GoodsIncomeHasColumnsMapper goodsIncomeHasColumnsMapper;
    private final GoodsIncomeHasImagesMapper goodsIncomeHasImagesMapper;
    private final ListFillService listFillService;
    private final GoodsMapper goodsMapper;
    private final GoodsIncomeAuditService goodsIncomeAuditService;
    private final GoodsHasColumnsMapper goodsHasColumnsMapper;
    private final GoodsHasImagesMapper goodsHasImagesMapper;
    private final CounterMapper counterMapper;
    private final GoodsIncomeTemplateDetailMapper goodsIncomeTemplateDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer create(GoodsIncomeCreateDTO dto) {
        try {
            // 0. 检查模板字段权限
            checkTemplateFieldPermissions(dto.getTemplateId().intValue());

            // 1. 检查柜台与门店归属关系
            checkCounterAndMerchantMatch(dto.getMerchantId(), dto.getDetails());

            // 2. 检查入库明细中已入库货品的信息一致性
            checkGoodsConsistency(dto.getDetails());

            // 3. 校验价格逻辑
            validatePriceLogic(dto.getDetails());

            // 4. 生成入库单号
            String incomeCode = SnUtils.generateIncomeCode();

            // 5. 创建入库单
            GoodsIncomeEntity income = new GoodsIncomeEntity();
            income.setIncomeCode(incomeCode);
            income.setCompanyId(SecurityUtils.getCompanyId().intValue());
            income.setMerchantId(dto.getMerchantId());
            income.setSupplierId(dto.getSupplierId());
            income.setTemplateId(dto.getTemplateId() != null ? dto.getTemplateId().intValue() : null);
            income.setRemark(dto.getRemark());
            income.setStatus(0); // 待审核
            income.setCreatedBy(SecurityUtils.getUserId().intValue());
            income.setAuditBy(0);

            // 6. 保存入库单
            boolean success = this.save(income);
            CommonUtils.abortIf(!success, "保存入库单失败");

            // 7. 创建入库单明细
            List<GoodsIncomeDetailEntity> details = new ArrayList<>();
            BigDecimal totalWeight = BigDecimal.ZERO;
            BigDecimal totalNetGoldWeight = BigDecimal.ZERO;
            BigDecimal totalNetSilverWeight = BigDecimal.ZERO;
            BigDecimal totalCostPrice = BigDecimal.ZERO;
            int totalNum = 0;

            for (GoodsIncomeDetailCreateDTO detailDTO : dto.getDetails()) {
                GoodsIncomeDetailEntity detail = new GoodsIncomeDetailEntity();
                detail.setReceiveId(income.getId().intValue());
                detail.setIncomeCode(incomeCode);
                detail.setCompanyId(income.getCompanyId());
                detail.setMerchantId(income.getMerchantId());
                detail.setSupplierId(income.getSupplierId());

                // 设置明细信息
                detail.setGoodsId(detailDTO.getGoodsId());
                detail.setCounterId(detailDTO.getCounterId());
                detail.setCategoryId(detailDTO.getCategoryId());
                detail.setSubclassId(detailDTO.getSubclassId());
                detail.setBrandId(detailDTO.getBrandId());
                detail.setStyleId(detailDTO.getStyleId());
                detail.setQualityId(detailDTO.getQualityId());
                detail.setTechnologyId(detailDTO.getTechnologyId());
                detail.setMainStoneId(detailDTO.getMainStoneId());
                detail.setSubStoneId(detailDTO.getSubStoneId());
                detail.setGoodsSn(StrUtil.isBlank(detailDTO.getGoodsSn()) ? SnUtils.generateGoodsSn() : detailDTO.getGoodsSn());
                detail.setName(detailDTO.getName());
                detail.setSalesType(detailDTO.getSalesType());
                detail.setBatchNo(detailDTO.getBatchNo());
                detail.setCertNo(detailDTO.getCertNo());
                detail.setRemark(detailDTO.getRemark());
                detail.setWeight(detailDTO.getWeight());
                detail.setNetGoldWeight(detailDTO.getNetGoldWeight());
                detail.setNetSilverWeight(detailDTO.getNetSilverWeight());
                detail.setMainStoneCount(detailDTO.getMainStoneCount());
                detail.setMainStoneWeight(detailDTO.getMainStoneWeight());
                detail.setSubStoneCount(detailDTO.getSubStoneCount());
                detail.setSubStoneWeight(detailDTO.getSubStoneWeight());
                detail.setCircleSize(detailDTO.getCircleSize());
                // 价格字段保存时转换为分
                detail.setCostPrice(
                        detailDTO.getCostPrice() != null ? PriceUtil.yuan2fen(detailDTO.getCostPrice()).longValue()
                                : null);
                detail.setGoldPrice(
                        detailDTO.getGoldPrice() != null ? PriceUtil.yuan2fen(detailDTO.getGoldPrice()).longValue()
                                : null);
                detail.setSilverPrice(
                        detailDTO.getSilverPrice() != null ? PriceUtil.yuan2fen(detailDTO.getSilverPrice()).longValue()
                                : null);
                detail.setWorkPrice(
                        detailDTO.getWorkPrice() != null ? PriceUtil.yuan2fen(detailDTO.getWorkPrice()).longValue()
                                : null);
                detail.setCertPrice(
                        detailDTO.getCertPrice() != null ? PriceUtil.yuan2fen(detailDTO.getCertPrice()).longValue()
                                : null);
                detail.setSaleWorkPrice(detailDTO.getSaleWorkPrice() != null
                        ? PriceUtil.yuan2fen(detailDTO.getSaleWorkPrice()).longValue()
                        : null);
                detail.setTagPrice(
                        detailDTO.getTagPrice() != null ? PriceUtil.yuan2fen(detailDTO.getTagPrice()).longValue()
                                : null);
                detail.setNum(detailDTO.getNum());
                detail.setStatus(0); // 待审核
                detail.setAuditBy(0);

                details.add(detail);

                BigDecimal num = new BigDecimal(detailDTO.getNum());

                // 累加汇总数据
                if (detailDTO.getWeight() != null) {
                    totalWeight = totalWeight.add(detailDTO.getWeight().multiply(num));
                }
                if (detailDTO.getNetGoldWeight() != null) {
                    totalNetGoldWeight = totalNetGoldWeight.add(detailDTO.getNetGoldWeight().multiply(num));
                }
                if (detailDTO.getNetSilverWeight() != null) {
                    totalNetSilverWeight = totalNetSilverWeight.add(detailDTO.getNetSilverWeight().multiply(num));
                }
                if (detailDTO.getCostPrice() != null) {
                    totalCostPrice = totalCostPrice.add(detailDTO.getCostPrice().multiply(num));
                }
                totalNum += detailDTO.getNum();
            }

            // 8. 保存入库单明细
            int rows = goodsIncomeDetailMapper.insertBatchSelective(details);
            CommonUtils.abortIf(rows <= 0, "保存入库单明细失败");

            // 9. 处理自定义字段和图片
            List<GoodsIncomeHasColumnsEntity> allColumns = new ArrayList<>();
            List<GoodsIncomeHasImagesEntity> allImages = new ArrayList<>();
            List<Long> allImageIds = new ArrayList<>();

            // 重新查询入库单明细以获取ID
            List<GoodsIncomeDetailEntity> savedDetails = goodsIncomeDetailMapper.selectListByQuery(
                    QueryWrapper.create().where(GoodsIncomeDetailEntity::getIncomeCode).eq(incomeCode));

            for (int i = 0; i < savedDetails.size(); i++) {
                GoodsIncomeDetailEntity detail = savedDetails.get(i);
                GoodsIncomeDetailCreateDTO detailDTO = dto.getDetails().get(i);

                // 处理自定义字段
                if (detailDTO.getCustomColumn() != null && !detailDTO.getCustomColumn().isEmpty()) {
                    for (var column : detailDTO.getCustomColumn()) {
                        // 检查字段权限，如果是加密字段则跳过
                        if (!ColumnEncryptUtil.signCanEdit(column.getColumnSign())) {
                            continue;
                        }

                        GoodsIncomeHasColumnsEntity columnEntity = new GoodsIncomeHasColumnsEntity();
                        columnEntity.setCompanyId(income.getCompanyId());
                        columnEntity.setIncomeId(income.getId().intValue());
                        columnEntity.setIncomeDetailId(detail.getId().intValue());
                        columnEntity.setColumnId(column.getColumnId());
                        columnEntity.setColumnSign(column.getColumnSign());
                        columnEntity.setValue(column.getValueStr());
                        columnEntity.setImageId(column.getImageId() == null ? 0 : column.getImageId());
                        allColumns.add(columnEntity);
                    }
                }

                // 处理图片
                if (detailDTO.getImage() != null && !detailDTO.getImage().isEmpty()) {
                    for (int j = 0; j < detailDTO.getImage().size(); j++) {
                        var image = detailDTO.getImage().get(j);

                        // 如果没有 url 则视为错误数据, 跳过
                        if(StrUtil.isBlank(image.getUrl())) {
                            continue;
                        }

                        Long imageId = image.getImageId() == null ? image.getId() : image.getImageId();

                        GoodsIncomeHasImagesEntity imageEntity = new GoodsIncomeHasImagesEntity();
                        imageEntity.setCompanyId(income.getCompanyId());
                        imageEntity.setIncomeId(income.getId().intValue());
                        imageEntity.setIncomeDetailId(detail.getId().intValue());
                        imageEntity.setImageId(imageId.intValue());
                        imageEntity.setUrl(image.getUrl());
                        imageEntity.setSort(j + 1);
                        allImages.add(imageEntity);
                        allImageIds.add(imageId);
                    }
                }
            }

            // 批量保存自定义字段
            if (!allColumns.isEmpty()) {
                goodsIncomeHasColumnsMapper.insertBatch(allColumns);
            }

            // 批量保存图片
            if (!allImages.isEmpty()) {
                goodsIncomeHasImagesMapper.insertBatch(allImages);
                // 批量更新文件状态
                CommonUtils.batchUpdateFileStatus(allImageIds, 1);
            }

            // 10. 更新入库单汇总数据
            income.setNum(totalNum);
            income.setTotalWeight(totalWeight);
            income.setTotalNetGoldWeight(totalNetGoldWeight);
            income.setTotalNetSilverWeight(totalNetSilverWeight);
            income.setTotalCostPrice(PriceUtil.yuan2fen(totalCostPrice).longValue());

            success = this.updateById(income);
            CommonUtils.abortIf(!success, "更新入库单汇总数据失败");

            OpLogUtils.appendOpLog("创建入库单", "创建入库单: " + incomeCode + ", 明细数: " + details.size(), dto);

            // 如果关闭了审核, 则继续执行审核逻辑
            if (!CommonUtils.getCompanySettings(SecurityUtils.getCompanyId()).getIncomeAuditEnabled()) {
                goodsIncomeAuditService.audit(income.getId().toString());
            }

            return income.getId().intValue();
        } catch (Exception e) {
            // 同步最大序号到Redis
            SnUtils.syncMaxSequence();
            // 抛出业务异常
            CommonUtils.abort("创建入库单失败：" + e.getMessage());
            return null;
        }
    }

    /**
     * 检查入库明细中的柜台是否属于指定的门店
     * @param merchantId 门店ID
     * @param details 入库明细
     */
    private void checkCounterAndMerchantMatch(Integer merchantId, List<GoodsIncomeDetailCreateDTO> details) {
        if (merchantId == null || CollectionUtil.isEmpty(details)) {
            return;
        }

        Set<Integer> counterIds = details.stream()
            .map(GoodsIncomeDetailCreateDTO::getCounterId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        if (counterIds.isEmpty()) {
            return;
        }

        QueryWrapper query = QueryWrapper.create()
            .select(COUNTER.ID, COUNTER.MERCHANT_ID, COUNTER.NAME)
            .where(COUNTER.ID.in(counterIds))
            .and(COUNTER.COMPANY_ID.eq(SecurityUtils.getCompanyId()));

        List<CounterEntity> counters = counterMapper.selectListByQuery(query);

        Map<Integer, CounterEntity> counterMap = counters.stream()
                .collect(Collectors.toMap(c -> c.getId().intValue(), Function.identity()));

        for (Integer counterId : counterIds) {
            CounterEntity counter = counterMap.get(counterId);
            if (counter == null) {
                CommonUtils.abort("ID为 " + counterId + " 的柜台不存在");
            }
            if (!merchantId.equals(counter.getMerchantId())) {
                CommonUtils.abort("柜台 [" + counter.getName() + "] 不属于当前入库单选择的门店");
            }
        }
    }

    /**
     * 校验价格逻辑
     * 1. 校验成本单价 = 金进金额 + 银进金额 + 进工费 + 证书费
     * 2. 证书费不能大于成本单价
     * 
     * @param details 入库明细列表
     */
    private void validatePriceLogic(List<GoodsIncomeDetailCreateDTO> details) {
        if (details == null || details.isEmpty()) {
            return;
        }

        for (int i = 0; i < details.size(); i++) {
            GoodsIncomeDetailCreateDTO detail = details.get(i);
            String goodsSn = detail.getGoodsSn();
            String detailInfo = StrUtil.isNotBlank(goodsSn) ? "条码[" + goodsSn + "]" : "第" + (i + 1) + "条明细";

            // 计算各分项金额
            BigDecimal goldAmount = BigDecimal.ZERO;
            BigDecimal silverAmount = BigDecimal.ZERO;
            BigDecimal workAmount = BigDecimal.ZERO;
            BigDecimal certAmount = BigDecimal.ZERO;

            // 金进金额 = 金进单价 × 净金重
            if (detail.getGoldPrice() != null && detail.getNetGoldWeight() != null) {
                goldAmount = detail.getGoldPrice().multiply(detail.getNetGoldWeight()).setScale(2, RoundingMode.HALF_UP);
            }

            // 银进金额 = 银进单价 × 净银重
            if (detail.getSilverPrice() != null && detail.getNetSilverWeight() != null) {
                silverAmount = detail.getSilverPrice().multiply(detail.getNetSilverWeight()).setScale(2, RoundingMode.HALF_UP);
            }

            // 进工费单价
            if (detail.getWorkPrice() != null) {
                workAmount = detail.getWorkPrice().setScale(2, RoundingMode.HALF_UP);
            }

            // 证书费
            if (detail.getCertPrice() != null) {
                certAmount = detail.getCertPrice().setScale(2, RoundingMode.HALF_UP);
            }

            // 计算分项金额总和
            BigDecimal totalComponentAmount = goldAmount.add(silverAmount).add(workAmount).add(certAmount);

            // 校验1: 成本单价 = 金进金额 + 银进金额 + 进工费 + 证书费
            if (detail.getCostPrice() != null) {
                // 使用BigDecimal比较，允许0.01元的误差
                BigDecimal costPrice = detail.getCostPrice();
                BigDecimal difference = costPrice.subtract(totalComponentAmount).abs().setScale(2, RoundingMode.HALF_UP);
                
                if (difference.compareTo(new BigDecimal("0.01")) > 0) {
                    CommonUtils.abort(detailInfo + " 成本单价与分项金额不匹配");
                }
            }

            // 校验2: 证书费不能大于成本单价
            if (detail.getCertPrice() != null && detail.getCostPrice() != null) {
                if (detail.getCertPrice().compareTo(detail.getCostPrice()) > 0) {
                    CommonUtils.abort(detailInfo + " 证书费不能大于成本单价");
                }
            }
        }
    }

    @Override
    public Page<GoodsIncomePageVO> getIncomePage(GoodsIncomePageQuery queryParams) {
        // 构建查询条件
        QueryWrapper query = QueryWrapper.create()
                .select(
                        QueryMethods.column(GoodsIncomeEntity::getId),
                        QueryMethods.column(GoodsIncomeEntity::getMerchantId),
                        QueryMethods.column(GoodsIncomeEntity::getIncomeCode),
                        QueryMethods.column(GoodsIncomeEntity::getSupplierId),
                        QueryMethods.column(GoodsIncomeEntity::getNum),
                        QueryMethods.column(GoodsIncomeEntity::getTotalWeight),
                        QueryMethods.column(GoodsIncomeEntity::getTotalNetGoldWeight),
                        QueryMethods.column(GoodsIncomeEntity::getTotalNetSilverWeight),
                        QueryMethods.column(GoodsIncomeEntity::getTotalCostPrice),
                        QueryMethods.column(GoodsIncomeEntity::getStatus),
                        QueryMethods.column(GoodsIncomeEntity::getTemplateId),
                        QueryMethods.column(GoodsIncomeEntity::getRemark),
                        QueryMethods.column(GoodsIncomeEntity::getCreatedBy),
                        QueryMethods.column(GoodsIncomeEntity::getCreatedAt),
                        QueryMethods.column(GoodsIncomeEntity::getAuditBy),
                        QueryMethods.column(GoodsIncomeEntity::getAuditAt))
                .where(GoodsIncomeEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .and(GoodsIncomeEntity::getMerchantId).in(SecurityUtils.getMerchantIds());

        // 添加查询条件
        if (StrUtil.isNotBlank(queryParams.getMerchantIds())) {
            List<Integer> merchantIds = QueryUtils.parseIntegerIds(queryParams.getMerchantIds());
            if (!merchantIds.isEmpty()) {
                query.and(GoodsIncomeEntity::getMerchantId).in(merchantIds);
            }
        }
        if (StrUtil.isNotBlank(queryParams.getIncomeCode())) {
            query.and(GoodsIncomeEntity::getIncomeCode).like(queryParams.getIncomeCode()); // 入库单号模糊查询
        }
        if (StrUtil.isNotBlank(queryParams.getSupplierIds())) {
            List<Integer> supplierIds = QueryUtils.parseIntegerIds(queryParams.getSupplierIds());
            if (!supplierIds.isEmpty()) {
                query.and(GoodsIncomeEntity::getSupplierId).in(supplierIds);
            }
        }
        if (queryParams.getStatus() != null) {
            query.and(GoodsIncomeEntity::getStatus).eq(queryParams.getStatus()); // 入库单状态查询
        }
        if (StrUtil.isNotBlank(queryParams.getCreatedByIds())) {
            List<Integer> createdByIds = QueryUtils.parseIntegerIds(queryParams.getCreatedByIds());
            if (!createdByIds.isEmpty()) {
                query.and(GoodsIncomeEntity::getCreatedBy).in(createdByIds);
            }
        }
        if (StrUtil.isNotBlank(queryParams.getAuditByIds())) {
            List<Integer> auditByIds = QueryUtils.parseIntegerIds(queryParams.getAuditByIds());
            if (!auditByIds.isEmpty()) {
                query.and(GoodsIncomeEntity::getAuditBy).in(auditByIds);
            }
        }

        // 处理创建时间范围查询
        if (queryParams.getCreatedAtRange() != null && !queryParams.getCreatedAtRange().isEmpty()) {
            if (queryParams.getCreatedAtRange().size() >= 2) {
                // 范围查询
                Date startTime = DateUtil.parseDateTime(queryParams.getCreatedAtRange().get(0));
                Date endTime = DateUtil.parseDateTime(queryParams.getCreatedAtRange().get(1));
                query.and(GoodsIncomeEntity::getCreatedAt).between(startTime, endTime);
            }
        }

        // 处理审核时间范围查询
        if (queryParams.getAuditAtRange() != null && !queryParams.getAuditAtRange().isEmpty()) {
            if (queryParams.getAuditAtRange().size() >= 2) {
                // 范围查询
                Date startTime = DateUtil.parseDateTime(queryParams.getAuditAtRange().get(0));
                Date endTime = DateUtil.parseDateTime(queryParams.getAuditAtRange().get(1));
                query.and(GoodsIncomeEntity::getAuditAt).between(startTime, endTime);
            }
        }

        // 按创建时间倒序排序
        query.orderBy(GoodsIncomeEntity::getId, false);

        // 处理导出
        if (queryParams.getExport() != null && queryParams.getExport() == 1) {
            exportIncomes(query, queryParams);
            return new Page<>();
        }

        // 处理打印
        if (queryParams.getPrint() != null && queryParams.getPrint() == 1) {
            // 打印直接返回列表数据
            return printIncomes(query, queryParams);
        }

        // 执行分页查询
        Page<GoodsIncomePageVO> page = this.mapper.paginateAs(queryParams.getPageNum(), queryParams.getPageSize(),
                query, GoodsIncomePageVO.class);

        // 数据填充
        fillIncomePageVOs(page.getRecords());

        return page;
    }

    /**
     * 导出入库单列表
     */
    private void exportIncomes(QueryWrapper query, GoodsIncomePageQuery queryParams) {
        // 检查导出数量限制
        long count = this.mapper.selectCountByQuery(query);
        CommonUtils.abortIf(count > CommonUtils.getMaxExportSize(), "导出数量超过限制");

        ExcelUtil.of(this.mapper, query, GoodsIncomePageVO.class, "incomes", "入库单列表")
                .getData((mapper, wrapper) -> {
                    List<GoodsIncomePageVO> voList = mapper.selectListByQueryAs(wrapper, GoodsIncomePageVO.class);
                    fillIncomePageVOs(voList);
                    return (List<?>)ColumnEncryptUtil.process(voList);
                })
                .doExport();
    }

    /**
     * 打印入库单列表，返回填充后的列表数据（不分页）
     */
    private Page<GoodsIncomePageVO> printIncomes(QueryWrapper query, GoodsIncomePageQuery queryParams) {
        // 检查打印数量限制
        long count = this.mapper.selectCountByQuery(query);
        CommonUtils.abortIf(count > CommonUtils.getMaxPrintSize(), "打印数量超过限制");

        List<GoodsIncomePageVO> voList = this.mapper.selectListByQueryAs(query, GoodsIncomePageVO.class);
        fillIncomePageVOs(voList);
        // 返回Page对象，pageNum=1, pageSize=voList.size(), total=count
        Page<GoodsIncomePageVO> page = new Page<>(1, voList.size(), count);
        page.setRecords(voList);
        return page;
    }

    /**
     * 填充入库单VO的关联数据和价格转换
     */
    private void fillIncomePageVOs(List<GoodsIncomePageVO> voList) {
        if (voList == null || voList.isEmpty()) {
            return;
        }
        Set<Integer> merchantIds = new HashSet<>();
        Set<Integer> supplierIds = new HashSet<>();
        Set<Integer> templateIds = new HashSet<>();
        Set<Integer> userIds = new HashSet<>();
        for (GoodsIncomePageVO vo : voList) {
            if (vo.getMerchantId() != null) {
                merchantIds.add(vo.getMerchantId());
            }
            if (vo.getSupplierId() != null) {
                supplierIds.add(vo.getSupplierId());
            }
            if (vo.getTemplateId() != null) {
                templateIds.add(vo.getTemplateId());
            }
            if (vo.getCreatedBy() != null) {
                userIds.add(vo.getCreatedBy());
            }
            if (vo.getAuditBy() != null) {
                userIds.add(vo.getAuditBy());
            }
            // 价格字段分转元转换
            if (vo.getTotalCostPrice() != null) {
                vo.setTotalCostPrice(PriceUtil.fen2yuan(vo.getTotalCostPrice()));
            }
        }
        ListFillUtil.of(voList)
                .build(listFillService::getMerchantNameById, merchantIds, "merchantId", "merchantName")
                .build(listFillService::getSupplierNameById, supplierIds, "supplierId", "supplierName")
                .build(listFillService::getTemplateNameById, templateIds, "templateId", "templateName")
                .build(listFillService::getUserNameByUserId, userIds, "createdBy", "createdByName")
                .build(listFillService::getUserNameByUserId, userIds, "auditBy", "auditByName")
                .handle();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteIncome(Long id) {
        // 1. 查询入库单信息
        GoodsIncomeEntity income = this.getById(id);
        CommonUtils.abortIf(income == null, "入库单不存在");

        // 2. 检查入库单状态，只有未审核的入库单才能删除
        CommonUtils.abortIf(income.getStatus() != 0, "只能删除未审核的入库单");

        // 3. 查询入库单明细，获取图片ID列表
        List<GoodsIncomeDetailEntity> details = goodsIncomeDetailMapper.selectListByQuery(
                QueryWrapper.create().where(GoodsIncomeDetailEntity::getReceiveId).eq(id));

        // 4. 查询入库单图片关联，获取图片ID列表
        List<GoodsIncomeHasImagesEntity> images = goodsIncomeHasImagesMapper.selectListByQuery(
                QueryWrapper.create().where(GoodsIncomeHasImagesEntity::getIncomeId).eq(id));

        // 收集所有图片ID
        List<Long> imageIds = images.stream()
                .map(img -> img.getImageId().longValue())
                .collect(Collectors.toList());

        // 5. 删除入库单图片关联
        if (!images.isEmpty()) {
            goodsIncomeHasImagesMapper.deleteByQuery(
                    QueryWrapper.create().where(GoodsIncomeHasImagesEntity::getIncomeId).eq(id));
        }

        // 6. 删除入库单字段关联
        goodsIncomeHasColumnsMapper.deleteByQuery(
                QueryWrapper.create().where(GoodsIncomeHasColumnsEntity::getIncomeId).eq(id));

        // 7. 删除入库单明细
        if (!details.isEmpty()) {
            goodsIncomeDetailMapper.deleteByQuery(
                    QueryWrapper.create().where(GoodsIncomeDetailEntity::getReceiveId).eq(id));
        }

        // 8. 删除入库单主表
        boolean result = this.removeById(id);

        // 9. 更新文件状态为未使用
        if (!imageIds.isEmpty()) {
            CommonUtils.batchUpdateFileStatus(imageIds, 0);
        }

        // 10. 记录操作日志
        if (result) {
            OpLogUtils.appendOpLog("入库单管理-删除入库单",
                    "删除入库单: " + income.getIncomeCode() + ", 明细数: " + details.size(),
                    Map.of("入库单", income, "明细数", details.size()));
        }

        return result;
    }

    /**
     * 检查入库明细中已入库货品的信息一致性
     * 
     * @param details 入库明细列表
     */
    private void checkGoodsConsistency(List<GoodsIncomeDetailCreateDTO> details) {
        if (details == null || details.isEmpty()) {
            return;
        }

        // 收集所有货品条码
        List<String> goodsSns = details.stream()
                .map(GoodsIncomeDetailCreateDTO::getGoodsSn)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (goodsSns.isEmpty()) {
            return;
        }

        // 检查是否存在重复的货品条码
        Set<String> goodsSnSet = new HashSet<>(goodsSns);
        if (goodsSnSet.size() != goodsSns.size()) {
            CommonUtils.abort("货品条码不能重复");
        }

        // 批量查询已入库的货品信息（stockNum > 0）
        List<GoodsEntity> existingGoods = goodsMapper.selectListByQuery(
                QueryWrapper.create()
                        .where(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                        .and(GoodsEntity::getGoodsSn).in(goodsSns)
                        .and(GoodsEntity::getStockNum).gt(0));

        if (existingGoods.isEmpty()) {
            return;
        }

        // 构建已入库货品的Map，便于快速查找
        Map<String, GoodsEntity> existingGoodsMap = existingGoods.stream()
                .collect(Collectors.toMap(GoodsEntity::getGoodsSn, goods -> goods));

        // 检查每个入库明细
        for (GoodsIncomeDetailCreateDTO detail : details) {
            String goodsSn = detail.getGoodsSn();
            if (StrUtil.isBlank(goodsSn)) {
                continue;
            }

            GoodsEntity existingGood = existingGoodsMap.get(goodsSn);
            if (existingGood != null) {
                // 检查信息一致性
                String inconsistentMessage = buildInconsistentFieldsMessage(detail, existingGood);
                if (StrUtil.isNotBlank(inconsistentMessage)) {
                    CommonUtils.abort(inconsistentMessage);
                }
            }
        }
    }

    /**
     * 构建不一致字段的错误信息
     * 
     * @param detail       入库明细
     * @param existingGood 已入库货品
     * @return 不一致字段的错误信息，如果一致则返回空字符串
     */
    private String buildInconsistentFieldsMessage(GoodsIncomeDetailCreateDTO detail, GoodsEntity existingGood) {
        List<String> inconsistentFields = new ArrayList<>();

        // 检查基础信息
        if (!Objects.equals(detail.getName(), existingGood.getName())) {
            inconsistentFields.add("货品名称");
        }

        if (!Objects.equals(detail.getCategoryId(), existingGood.getCategoryId())) {
            inconsistentFields.add("所属大类");
        }

        if (!Objects.equals(detail.getSubclassId(), existingGood.getSubclassId())) {
            inconsistentFields.add("所属小类");
        }

        if (!Objects.equals(detail.getBrandId(), existingGood.getBrandId())) {
            inconsistentFields.add("品牌");
        }

        if (!Objects.equals(detail.getStyleId(), existingGood.getStyleId())) {
            inconsistentFields.add("款式");
        }

        if (!Objects.equals(detail.getQualityId(), existingGood.getQualityId())) {
            inconsistentFields.add("成色");
        }

        if (!Objects.equals(detail.getTechnologyId(), existingGood.getTechnologyId())) {
            inconsistentFields.add("工艺");
        }

        if (!Objects.equals(detail.getMainStoneId(), existingGood.getMainStoneId())) {
            inconsistentFields.add("主石");
        }

        if (!Objects.equals(detail.getSubStoneId(), existingGood.getSubStoneId())) {
            inconsistentFields.add("辅石");
        }

        if (!Objects.equals(detail.getSalesType(), existingGood.getSalesType())) {
            inconsistentFields.add("销售方式");
        }

        // 检查重量信息（使用BigDecimal比较）
        if (detail.getWeight() != null && existingGood.getWeight() != null) {
            if (detail.getWeight().compareTo(existingGood.getWeight()) != 0) {
                inconsistentFields.add("重量");
            }
        } else if (detail.getWeight() != null || existingGood.getWeight() != null) {
            inconsistentFields.add("重量");
        }

        if (detail.getNetGoldWeight() != null && existingGood.getNetGoldWeight() != null) {
            if (detail.getNetGoldWeight().compareTo(existingGood.getNetGoldWeight()) != 0) {
                inconsistentFields.add("净金重");
            }
        } else if (detail.getNetGoldWeight() != null || existingGood.getNetGoldWeight() != null) {
            inconsistentFields.add("净金重");
        }

        if (detail.getNetSilverWeight() != null && existingGood.getNetSilverWeight() != null) {
            if (detail.getNetSilverWeight().compareTo(existingGood.getNetSilverWeight()) != 0) {
                inconsistentFields.add("净银重");
            }
        } else if (detail.getNetSilverWeight() != null || existingGood.getNetSilverWeight() != null) {
            inconsistentFields.add("净银重");
        }

        if (!Objects.equals(detail.getMainStoneCount(), existingGood.getMainStoneCount())) {
            inconsistentFields.add("主石数");
        }

        if (detail.getMainStoneWeight() != null && existingGood.getMainStoneWeight() != null) {
            if (detail.getMainStoneWeight().compareTo(existingGood.getMainStoneWeight()) != 0) {
                inconsistentFields.add("主石重");
            }
        } else if (detail.getMainStoneWeight() != null || existingGood.getMainStoneWeight() != null) {
            inconsistentFields.add("主石重");
        }

        if (!Objects.equals(detail.getSubStoneCount(), existingGood.getSubStoneCount())) {
            inconsistentFields.add("辅石数");
        }

        if (detail.getSubStoneWeight() != null && existingGood.getSubStoneWeight() != null) {
            if (detail.getSubStoneWeight().compareTo(existingGood.getSubStoneWeight()) != 0) {
                inconsistentFields.add("辅石重");
            }
        } else if (detail.getSubStoneWeight() != null || existingGood.getSubStoneWeight() != null) {
            inconsistentFields.add("辅石重");
        }

        if (!Objects.equals(detail.getCircleSize(), existingGood.getCircleSize())) {
            inconsistentFields.add("圈口");
        }

        if (!Objects.equals(detail.getBatchNo(), existingGood.getBatchNo())) {
            inconsistentFields.add("批次号");
        }

        if (!Objects.equals(detail.getCertNo(), existingGood.getCertNo())) {
            inconsistentFields.add("证书号");
        }

        // 如果有不一致的字段，构建错误信息
        if (!inconsistentFields.isEmpty()) {
            StringBuilder errorMessage = new StringBuilder();
            errorMessage.append("货品条码 [").append(detail.getGoodsSn()).append("] 已入库，但信息不一致：\n");
            for (String field : inconsistentFields) {
                errorMessage.append("- ").append(field).append("\n");
            }
            return errorMessage.toString();
        }

        return "";
    }

    /**
     * 检查模板字段权限
     * 如果模板中包含用户无权编辑的字段，则不允许创建入库单
     *
     * @param templateId 模板ID
     */
    private void checkTemplateFieldPermissions(Integer templateId) {
        if (templateId == null) {
            return;
        }

        // 查询模板详情
        List<GoodsIncomeTemplateDetailEntity> templateDetails = goodsIncomeTemplateDetailMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GoodsIncomeTemplateDetailEntity::getTemplateId).eq(templateId)
                .and(GoodsIncomeTemplateDetailEntity::getEnabled).eq(1) // 只检查启用的字段
        );

        if (templateDetails.isEmpty()) {
            return;
        }

        // 检查每个字段的权限
        for (GoodsIncomeTemplateDetailEntity detail : templateDetails) {
            String sign = detail.getSign();

            // 检查字段是否可编辑
            if (!ColumnEncryptUtil.signCanEdit(sign)) {
                // 获取字段信息用于错误提示
                GoodsColumnEntity column = CommonUtils.getGoodsColumnsBySign(sign);
                String fieldName = column != null ? column.getName() : sign;
                CommonUtils.abort("当前用户无权编辑字段 [" + fieldName + "]，无法使用此模板创建入库单");
            }
        }
    }

    /**
     * 根据字段权限更新明细字段
     * 如果用户无权编辑某个字段，则跳过该字段的更新
     *
     * @param detail 原明细实体
     * @param dto 更新DTO
     */
    private void updateDetailFieldsWithPermissionCheck(GoodsIncomeDetailEntity detail, GoodsIncomeDetailUpdateDTO dto) {
        // 基础字段权限检查和更新
        if (ColumnEncryptUtil.signCanEdit("counter_id")) {
            detail.setCounterId(dto.getCounterId());
        }
        if (ColumnEncryptUtil.signCanEdit("category_id")) {
            detail.setCategoryId(dto.getCategoryId());
        }
        if (ColumnEncryptUtil.signCanEdit("subclass_id")) {
            detail.setSubclassId(dto.getSubclassId());
        }
        if (ColumnEncryptUtil.signCanEdit("brand_id")) {
            detail.setBrandId(dto.getBrandId());
        }
        if (ColumnEncryptUtil.signCanEdit("style_id")) {
            detail.setStyleId(dto.getStyleId());
        }
        if (ColumnEncryptUtil.signCanEdit("quality_id")) {
            detail.setQualityId(dto.getQualityId());
        }
        if (ColumnEncryptUtil.signCanEdit("technology_id")) {
            detail.setTechnologyId(dto.getTechnologyId());
        }
        if (ColumnEncryptUtil.signCanEdit("main_stone_id")) {
            detail.setMainStoneId(dto.getMainStoneId());
        }
        if (ColumnEncryptUtil.signCanEdit("sub_stone_id")) {
            detail.setSubStoneId(dto.getSubStoneId());
        }
        if (ColumnEncryptUtil.signCanEdit("goods_sn")) {
            detail.setGoodsSn(StrUtil.isBlank(dto.getGoodsSn()) ? detail.getGoodsSn() : dto.getGoodsSn());
        }
        if (ColumnEncryptUtil.signCanEdit("name")) {
            detail.setName(dto.getName());
        }
        if (ColumnEncryptUtil.signCanEdit("sales_type")) {
            detail.setSalesType(dto.getSalesType());
        }
        if (ColumnEncryptUtil.signCanEdit("batch_no")) {
            detail.setBatchNo(dto.getBatchNo());
        }
        if (ColumnEncryptUtil.signCanEdit("cert_no")) {
            detail.setCertNo(dto.getCertNo());
        }
        if (ColumnEncryptUtil.signCanEdit("remark")) {
            detail.setRemark(dto.getRemark());
        }
        if (ColumnEncryptUtil.signCanEdit("weight")) {
            detail.setWeight(dto.getWeight());
        }
        if (ColumnEncryptUtil.signCanEdit("net_gold_weight")) {
            detail.setNetGoldWeight(dto.getNetGoldWeight());
        }
        if (ColumnEncryptUtil.signCanEdit("net_silver_weight")) {
            detail.setNetSilverWeight(dto.getNetSilverWeight());
        }
        if (ColumnEncryptUtil.signCanEdit("main_stone_count")) {
            detail.setMainStoneCount(dto.getMainStoneCount());
        }
        if (ColumnEncryptUtil.signCanEdit("main_stone_weight")) {
            detail.setMainStoneWeight(dto.getMainStoneWeight());
        }
        if (ColumnEncryptUtil.signCanEdit("sub_stone_count")) {
            detail.setSubStoneCount(dto.getSubStoneCount());
        }
        if (ColumnEncryptUtil.signCanEdit("sub_stone_weight")) {
            detail.setSubStoneWeight(dto.getSubStoneWeight());
        }
        if (ColumnEncryptUtil.signCanEdit("circle_size")) {
            detail.setCircleSize(dto.getCircleSize());
        }

        // 价格字段权限检查和更新（转换为分）
        if (ColumnEncryptUtil.signCanEdit("cost_price")) {
            detail.setCostPrice(dto.getCostPrice() != null ? PriceUtil.yuan2fen(dto.getCostPrice()).longValue() : null);
        }
        if (ColumnEncryptUtil.signCanEdit("gold_price")) {
            detail.setGoldPrice(dto.getGoldPrice() != null ? PriceUtil.yuan2fen(dto.getGoldPrice()).longValue() : null);
        }
        if (ColumnEncryptUtil.signCanEdit("silver_price")) {
            detail.setSilverPrice(dto.getSilverPrice() != null ? PriceUtil.yuan2fen(dto.getSilverPrice()).longValue() : null);
        }
        if (ColumnEncryptUtil.signCanEdit("work_price")) {
            detail.setWorkPrice(dto.getWorkPrice() != null ? PriceUtil.yuan2fen(dto.getWorkPrice()).longValue() : null);
        }
        if (ColumnEncryptUtil.signCanEdit("cert_price")) {
            detail.setCertPrice(dto.getCertPrice() != null ? PriceUtil.yuan2fen(dto.getCertPrice()).longValue() : null);
        }
        if (ColumnEncryptUtil.signCanEdit("sale_work_price")) {
            detail.setSaleWorkPrice(dto.getSaleWorkPrice() != null ? PriceUtil.yuan2fen(dto.getSaleWorkPrice()).longValue() : null);
        }
        if (ColumnEncryptUtil.signCanEdit("tag_price")) {
            detail.setTagPrice(dto.getTagPrice() != null ? PriceUtil.yuan2fen(dto.getTagPrice()).longValue() : null);
        }
        if (ColumnEncryptUtil.signCanEdit("num")) {
            detail.setNum(dto.getNum());
        }
    }

    @Override
    public GoodsIncomeInfoVO getIncomeInfo(Long id) {
        // 1. 构建查询条件
        QueryWrapper query = QueryWrapper.create()
                .select(
                        QueryMethods.column(GoodsIncomeEntity::getId),
                        QueryMethods.column(GoodsIncomeEntity::getIncomeCode),
                        QueryMethods.column(GoodsIncomeEntity::getMerchantId),
                        QueryMethods.column(GoodsIncomeEntity::getSupplierId),
                        QueryMethods.column(GoodsIncomeEntity::getStatus),
                        QueryMethods.column(GoodsIncomeEntity::getNum),
                        QueryMethods.column(GoodsIncomeEntity::getTotalWeight),
                        QueryMethods.column(GoodsIncomeEntity::getTotalNetGoldWeight),
                        QueryMethods.column(GoodsIncomeEntity::getTotalNetSilverWeight),
                        QueryMethods.column(GoodsIncomeEntity::getTotalCostPrice),
                        QueryMethods.column(GoodsIncomeEntity::getCreatedBy),
                        QueryMethods.column(GoodsIncomeEntity::getCreatedAt),
                        QueryMethods.column(GoodsIncomeEntity::getAuditBy),
                        QueryMethods.column(GoodsIncomeEntity::getAuditAt),
                        QueryMethods.column(GoodsIncomeEntity::getTemplateId),
                        QueryMethods.column(GoodsIncomeEntity::getRemark))
                .where(GoodsIncomeEntity::getId).eq(id)
                .and(GoodsIncomeEntity::getCompanyId).eq(SecurityUtils.getCompanyId());

        // 2. 查询入库单信息
        GoodsIncomeInfoVO info = this.mapper.selectOneByQueryAs(query, GoodsIncomeInfoVO.class);
        CommonUtils.abortIf(info == null, "入库单不存在");

        // 3. 填充关联数据
        ListFillUtil.of(Collections.singletonList(info))
                .build(listFillService::getMerchantNameById, Collections.singleton(info.getMerchantId()), "merchantId",
                        "storeName")
                .build(listFillService::getSupplierNameById, Collections.singleton(info.getSupplierId()), "supplierId",
                        "supplierName")
                .build(listFillService::getUserNameByUserId, Collections.singleton(info.getCreatedBy()), "createdBy",
                        "creator")
                .build(listFillService::getUserNameByUserId, Collections.singleton(info.getAuditBy()), "auditBy",
                        "auditor")
                .build(listFillService::getTemplateNameById, Collections.singleton(info.getTemplateId()), "templateId",
                        "templateName")
                .handle();

        // 4. 处理价格字段转换
        if (info.getTotalCostPrice() != null) {
            info.setTotalCostPrice(PriceUtil.fen2yuan(info.getTotalCostPrice()));
        }

        return info;
    }

    @Override
    public Page<GoodsIncomeDetailPageVO> getIncomeDetailPage(GoodsIncomeDetailPageQuery queryParams) {
        // 构建查询条件
        QueryWrapper query = QueryWrapper.create()
                .where(GoodsIncomeDetailEntity::getReceiveId).eq(queryParams.getId())
                .and(GoodsIncomeDetailEntity::getCompanyId).eq(SecurityUtils.getCompanyId());

        // 处理ids参数
        if (CollectionUtil.isNotEmpty(queryParams.getIds())) {
            query.in(GoodsIncomeDetailEntity::getId, queryParams.getIds());
        }

        // 处理导出
        if (queryParams.getExport() != null && queryParams.getExport() == 1) {
            exportIncomeDetails(query, queryParams);
            return new Page<>();
        }

        // 处理打印
        if (queryParams.getPrint() != null && queryParams.getPrint() == 1) {
            return printIncomeDetails(query, queryParams);
        }

        // 执行分页查询
        Page<GoodsIncomeDetailPageVO> page = goodsIncomeDetailMapper.paginateAs(
                queryParams.getPageNum(),
                queryParams.getPageSize(),
                query,
                GoodsIncomeDetailPageVO.class);

        // 填充关联数据
        if (!page.getRecords().isEmpty()) {
            fillIncomeDetailPageVOs(page.getRecords());
        }

        return page;
    }

    /**
     * 导出入库单明细
     */
    private void exportIncomeDetails(QueryWrapper query, GoodsIncomeDetailPageQuery queryParams) {
        ExcelUtil.of(goodsIncomeDetailMapper, query, GoodsIncomeDetailPageVO.class, "income_details", "入库单明细")
                .getData((mapper, wrapper) -> {
                    List<GoodsIncomeDetailPageVO> voList = goodsIncomeDetailMapper.selectListByQueryAs(wrapper,
                            GoodsIncomeDetailPageVO.class);
                    if (!voList.isEmpty()) {
                        fillIncomeDetailPageVOs(voList);
                    }

                    List<JSONObject> columns = ColumnEncryptUtil.encrypt(voList, GoodsIncomeDetailPageVO.class, "customColumn");
                    ColumnEncryptUtil.handleJsonImageExport(columns);

                    // 处理数量字段格式化
                    columns.forEach(item -> {
                        // 处理销售方式显示
                        if (item.containsKey("salesType") && item.get("salesType") != null) {
                            String salesType = item.getStr("salesType");
                            item.set("salesType", "1".equals(salesType) ? "按重量" : "按数量");
                        }
                    });

                    return columns;
                })
                .doExport();
    }

    /**
     * 打印入库单明细
     */
    private Page<GoodsIncomeDetailPageVO> printIncomeDetails(QueryWrapper query,
            GoodsIncomeDetailPageQuery queryParams) {
        // 检查打印数量
        CommonUtils.abortIf(
                goodsIncomeDetailMapper.selectCountByQuery(query) > CommonUtils.getMaxPrintSize(),
                "打印数量超过限制");

        // 查询数据
        List<GoodsIncomeDetailPageVO> voList = goodsIncomeDetailMapper.selectListByQueryAs(query,
                GoodsIncomeDetailPageVO.class);
        if (!voList.isEmpty()) {
            fillIncomeDetailPageVOs(voList);
        }

        // 构建分页结果
        Page<GoodsIncomeDetailPageVO> page = new Page<>();
        page.setRecords(voList);
        page.setPageNumber(1);
        page.setPageSize(voList.size());
        page.setTotalRow(voList.size());
        return page;
    }

    /**
     * 填充入库单明细关联数据
     */
    private void fillIncomeDetailPageVOs(List<GoodsIncomeDetailPageVO> records) {
        // 一次循环获取所有ID
        Set<Long> counterIds = new HashSet<>();
        Set<Long> categoryIds = new HashSet<>();
        Set<Long> subclassIds = new HashSet<>();
        Set<Long> brandIds = new HashSet<>();
        Set<Long> styleIds = new HashSet<>();
        Set<Long> qualityIds = new HashSet<>();
        Set<Long> technologyIds = new HashSet<>();
        Set<Long> mainStoneIds = new HashSet<>();
        Set<Long> subStoneIds = new HashSet<>();
        Set<Long> goodsIds = new HashSet<>();
        Set<Long> incomeDetailIds = new HashSet<>();

        for (GoodsIncomeDetailPageVO vo : records) {
            vo.setSalesTypeName(vo.getSalesType().equals("1") ? "按重量" : "按数量");
            if (vo.getCounterId() != null) {
                counterIds.add(Long.valueOf(vo.getCounterId()));
            }
            if (vo.getCategoryId() != null) {
                categoryIds.add(Long.valueOf(vo.getCategoryId()));
            }
            if (vo.getSubclassId() != null) {
                subclassIds.add(Long.valueOf(vo.getSubclassId()));
            }
            if (vo.getBrandId() != null) {
                brandIds.add(Long.valueOf(vo.getBrandId()));
            }
            if (vo.getStyleId() != null) {
                styleIds.add(Long.valueOf(vo.getStyleId()));
            }
            if (vo.getQualityId() != null) {
                qualityIds.add(Long.valueOf(vo.getQualityId()));
            }
            if (vo.getTechnologyId() != null) {
                technologyIds.add(Long.valueOf(vo.getTechnologyId()));
            }
            if (vo.getMainStoneId() != null) {
                mainStoneIds.add(Long.valueOf(vo.getMainStoneId()));
            }
            if (vo.getSubStoneId() != null) {
                subStoneIds.add(Long.valueOf(vo.getSubStoneId()));
            }
            if (vo.getGoodsId() != null) {
                goodsIds.add(Long.valueOf(vo.getGoodsId()));
            }
            if (vo.getId() != null) {
                incomeDetailIds.add(Long.valueOf(vo.getId()));
            }
        }

        // 填充关联数据
        ListFillUtil.of(records)
                .build(listFillService::getCounterNameById, counterIds, "counterId", "counterName")
                .build(listFillService::getCategoryNameById, categoryIds, "categoryId", "categoryName")
                .build(listFillService::getSubclassNameById, subclassIds, "subclassId", "subclassName")
                .build(listFillService::getBrandNameById, brandIds, "brandId", "brandName")
                .build(listFillService::getStyleNameById, styleIds, "styleId", "styleName")
                .build(listFillService::getQualityNameById, qualityIds, "qualityId", "qualityName")
                .build(listFillService::getTechnologyNameById, technologyIds, "technologyId", "technologyName")
                .build(listFillService::getJewelryMapperNameById, mainStoneIds, "mainStoneId", "mainStoneName")
                .build(listFillService::getJewelryMapperNameById, subStoneIds, "subStoneId", "subStoneName")
                .build(listFillService::getGoodsImgByGoodsId, goodsIds, "goodsId", "images")
                .build(listFillService::getIncomeImgByIncomeDetailId, incomeDetailIds, "id", "image")
                .build(listFillService::getIncomeColumnVosById, incomeDetailIds, "id", "customColumn")
                .peek(obj -> {
                    GoodsIncomeDetailPageVO vo = (GoodsIncomeDetailPageVO) obj;
                    BigDecimal num = BigDecimal.valueOf(vo.getNum());
                    // 计算金额
                    if (vo.getGoldPrice() != null && vo.getNetGoldWeight() != null) {
                        vo.setGoldAmount(PriceUtil.fen2yuan(vo.getGoldPrice().multiply(vo.getNetGoldWeight()).multiply(num)));
                    }
                    if (vo.getSilverPrice() != null && vo.getNetSilverWeight() != null) {
                        vo.setSilverAmount(PriceUtil.fen2yuan(vo.getSilverPrice().multiply(vo.getNetSilverWeight()).multiply(num)));
                    }
                    if (vo.getWorkPrice() != null && vo.getWeight() != null) {
                        vo.setWorkAmount(PriceUtil.fen2yuan(vo.getWorkPrice().multiply(num)));
                    }
                    if (vo.getSaleWorkPrice() != null && vo.getWeight() != null) {
                        vo.setSaleWorkAmount(PriceUtil.fen2yuan(vo.getSaleWorkPrice().multiply(num)));
                    }

                    // 价格字段分转元
                    vo.setCostPrice(PriceUtil.fen2yuan(vo.getCostPrice()));
                    vo.setTagPrice(PriceUtil.fen2yuan(vo.getTagPrice()));
                    vo.setGoldPrice(PriceUtil.fen2yuan(vo.getGoldPrice()));
                    vo.setSilverPrice(PriceUtil.fen2yuan(vo.getSilverPrice()));
                    vo.setWorkPrice(PriceUtil.fen2yuan(vo.getWorkPrice()));
                    vo.setSaleWorkPrice(PriceUtil.fen2yuan(vo.getSaleWorkPrice()));
                    vo.setCertPrice(PriceUtil.fen2yuan(vo.getCertPrice()));
                })
                .handle();
    }

    /**
     * 计算入库单统计数据
     * @param incomeId 入库单ID
     * @param companyId 公司ID
     * @return 统计数据
     */
    private Row calculateIncomeStats(Integer incomeId, Integer companyId) {
        return goodsIncomeDetailMapper.selectOneByQueryAs(
            QueryWrapper.create()
                .select(
                    QueryMethods.sum(QueryMethods.column(GoodsIncomeDetailEntity::getWeight)
                        .multiply(QueryMethods.column(GoodsIncomeDetailEntity::getNum))).as("total_weight"),
                    QueryMethods.sum(QueryMethods.column(GoodsIncomeDetailEntity::getNetGoldWeight)
                        .multiply(QueryMethods.column(GoodsIncomeDetailEntity::getNum))).as("total_net_gold_weight"),
                    QueryMethods.sum(QueryMethods.column(GoodsIncomeDetailEntity::getNetSilverWeight)
                        .multiply(QueryMethods.column(GoodsIncomeDetailEntity::getNum))).as("total_net_silver_weight"),
                    QueryMethods.sum(QueryMethods.column(GoodsIncomeDetailEntity::getCostPrice)
                        .multiply(QueryMethods.column(GoodsIncomeDetailEntity::getNum))).as("total_cost_price")
                )
                .where(GoodsIncomeDetailEntity::getReceiveId).eq(incomeId)
                .and(GoodsIncomeDetailEntity::getCompanyId).eq(companyId),
            Row.class
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteIncomeDetail(Long id) {
        // 获取明细信息
        GoodsIncomeDetailEntity detail = goodsIncomeDetailMapper.selectOneById(id);
        CommonUtils.abortIf(detail == null, "入库单明细不存在");
        CommonUtils.abortIf(!detail.getCompanyId().equals(SecurityUtils.getCompanyId().intValue()), "无权操作其他商户的数据");

        // 检查是否是最后一条明细
        Long detailCount = goodsIncomeDetailMapper.selectCountByQuery(
            QueryWrapper.create()
                .where(GoodsIncomeDetailEntity::getReceiveId).eq(detail.getReceiveId())
                .and(GoodsIncomeDetailEntity::getCompanyId).eq(detail.getCompanyId())
        );
        CommonUtils.abortIf(detailCount <= 1, "当前入库单仅剩最后一条明细，请前往入库单列表操作");

        // 获取关联的图片
        List<GoodsIncomeHasImagesEntity> images = goodsIncomeHasImagesMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GoodsIncomeHasImagesEntity::getIncomeDetailId).eq(detail.getId())
                .and(GoodsIncomeHasImagesEntity::getCompanyId).eq(detail.getCompanyId())
        );

        // 更新图片状态为未使用
        if (!images.isEmpty()) {
            List<Long> imageIds = images.stream()
                .map(image -> image.getImageId().longValue())
                .collect(Collectors.toList());
            CommonUtils.batchUpdateFileStatus(imageIds, 0);
        }

        // 删除关联的图片
        goodsIncomeHasImagesMapper.deleteByQuery(
            QueryWrapper.create()
                .where(GoodsIncomeHasImagesEntity::getIncomeDetailId).eq(detail.getId())
                .and(GoodsIncomeHasImagesEntity::getCompanyId).eq(detail.getCompanyId())
        );

        // 删除关联的自定义字段
        goodsIncomeHasColumnsMapper.deleteByQuery(
            QueryWrapper.create()
                .where(GoodsIncomeHasColumnsEntity::getIncomeDetailId).eq(detail.getId())
                .and(GoodsIncomeHasColumnsEntity::getCompanyId).eq(detail.getCompanyId())
        );

        // 删除明细
        goodsIncomeDetailMapper.deleteById(id);

        // 更新入库单统计信息
        GoodsIncomeEntity income = mapper.selectOneById(detail.getReceiveId());
        if (income != null) {
            // 查询统计数据
            Row stats = calculateIncomeStats(income.getId().intValue(), income.getCompanyId());

            // 更新入库单
            income.setTotalWeight(stats.getBigDecimal("total_weight"));
            income.setTotalNetGoldWeight(stats.getBigDecimal("total_net_gold_weight"));
            income.setTotalNetSilverWeight(stats.getBigDecimal("total_net_silver_weight"));
            income.setTotalCostPrice(stats.getLong("total_cost_price"));
            mapper.update(income);

            // 记录操作日志
            OpLogUtils.appendOpLog(income.getIncomeCode(), "删除入库单明细", null);
        }

        return true;
    }

    @Override
    public GoodsSnCheckVO checkGoodsSn(GoodsSnCheckDTO dto) {
        // 1. 如果指定了入库单明细，检查该入库单下是否存在相同条码
        if (dto.getIncomeDetailId() != null && dto.getIncomeDetailId() > 0) {
            // 获取明细信息
            GoodsIncomeDetailEntity detail = goodsIncomeDetailMapper.selectOneById(dto.getIncomeDetailId());
            if (detail != null) {
                // 查询同一入库单下的其他明细
                List<GoodsIncomeDetailEntity> otherDetails = goodsIncomeDetailMapper.selectListByQuery(
                    QueryWrapper.create()
                        .where(GoodsIncomeDetailEntity::getReceiveId).eq(detail.getReceiveId())
                        .and(GoodsIncomeDetailEntity::getId).ne(detail.getId())
                        .and(GoodsIncomeDetailEntity::getGoodsSn).eq(dto.getGoodsSn())
                        .and(GoodsIncomeDetailEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                );
                
                // 如果存在相同条码的其他明细，抛出异常
                if (!otherDetails.isEmpty()) {
                    CommonUtils.abort("该入库单下已存在相同条码的明细");
                }
            }
        }

        // 2. 查询商品信息
        GoodsEntity goods = goodsMapper.selectOneByQuery(
            QueryWrapper.create()
                .where(GoodsEntity::getGoodsSn).eq(dto.getGoodsSn())
                .and(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                // 门店权限限制
                // .and(GoodsEntity::getMerchantId).in(SecurityUtils.getMerchantIds())
        );
        
        if (goods == null) {
            return null;
        }
        
        // 3. 查询商品字段信息
        List<GoodsHasColumnsEntity> columns = goodsHasColumnsMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GoodsHasColumnsEntity::getGoodsId).eq(goods.getId())
                .and(GoodsHasColumnsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );
        
        // 4. 查询商品图片信息
        List<GoodsHasImagesEntity> images = goodsHasImagesMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GoodsHasImagesEntity::getGoodsId).eq(goods.getId())
                .and(GoodsHasImagesEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .orderBy(GoodsHasImagesEntity::getSort, true)
        );
        
        // 5. 组装返回数据
        GoodsSnCheckVO vo = new GoodsSnCheckVO();
        
        // 创建价格转换后的商品VO
        GoodsSnCheckVO.GoodsWithYuanPriceVO goodsWithYuanPrice = new GoodsSnCheckVO.GoodsWithYuanPriceVO();
        
        // 使用BeanUtils复制基础字段
        BeanUtils.copyProperties(goods, goodsWithYuanPrice);
        
        // 价格字段分转元处理
        if (goods.getCostPrice() != null) {
            goodsWithYuanPrice.setCostPrice(PriceUtil.fen2yuan(goods.getCostPrice()));
        }
        if (goods.getGoldPrice() != null) {
            goodsWithYuanPrice.setGoldPrice(PriceUtil.fen2yuan(goods.getGoldPrice()));
        }
        if (goods.getSilverPrice() != null) {
            goodsWithYuanPrice.setSilverPrice(PriceUtil.fen2yuan(goods.getSilverPrice()));
        }
        if (goods.getWorkPrice() != null) {
            goodsWithYuanPrice.setWorkPrice(PriceUtil.fen2yuan(goods.getWorkPrice()));
        }
        if (goods.getCertPrice() != null) {
            goodsWithYuanPrice.setCertPrice(PriceUtil.fen2yuan(goods.getCertPrice()));
        }
        if (goods.getSaleWorkPrice() != null) {
            goodsWithYuanPrice.setSaleWorkPrice(PriceUtil.fen2yuan(goods.getSaleWorkPrice()));
        }
        if (goods.getTagPrice() != null) {
            goodsWithYuanPrice.setTagPrice(PriceUtil.fen2yuan(goods.getTagPrice()));
        }
        
        vo.setGoods(goodsWithYuanPrice);
        vo.setColumns(columns);
        vo.setImages(images);
        vo.setNewColumns(columns.stream().map((GoodsHasColumnsEntity i) -> {
            Map<String, Object> map = new HashMap<>();
            map.put("companyId", i.getCompanyId());
            map.put("goodsId", i.getGoodsId());
            map.put("columnId", i.getColumnId());
            map.put("columnSign", i.getColumnSign());
            map.put("imageId", i.getImageId());
            map.put("id", i.getId());
            map.put("value", i.getValue());

            GoodsColumnEntity column = CommonUtils.getGoodsColumnsBySign(i.getColumnSign());

            map.put("type", column.getType());
            map.put("isMultiple", column.getIsMultiple());

            if(column != null){
                // 单独处理图片字段的格式
                if(column.getType().equals(6)){
                    if(StrUtil.isNotBlank(i.getValue())){
                        Map<String, Object> image = new HashMap<>();

                        image.put("id", i.getImageId());
                        image.put("imageId", i.getImageId());
                        image.put("url", i.getValue());

                        map.put("value", JSONUtil.toJsonStr(List.of(image)));
                    }else{
                        map.put("value", new ArrayList<>());
                    }
                }
            }

            return map;
        }).collect(Collectors.toList()));
        
        // 6. 填充大类名称
        if (goods.getCategoryId() != null) {
            vo.setCategoryId(goods.getCategoryId());
            String categoryName = listFillService.getCategoryNameById(Set.of(goods.getCategoryId())).getOrDefault(goods.getCategoryId() + "", "");
            vo.setCategoryName(categoryName);
        }
        
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateIncomeDetail(Long id, GoodsIncomeDetailUpdateDTO dto) {
        // 1. 获取明细信息
        GoodsIncomeDetailEntity detail = goodsIncomeDetailMapper.selectOneById(id);
        CommonUtils.abortIf(detail == null, "入库单明细不存在");
        CommonUtils.abortIf(!detail.getCompanyId().equals(SecurityUtils.getCompanyId().intValue()), "无权操作其他商户的数据");

        // 2. 获取入库单信息
        GoodsIncomeEntity income = mapper.selectOneById(detail.getReceiveId());
        CommonUtils.abortIf(income == null, "入库单不存在");
        CommonUtils.abortIf(income.getStatus() != 0, "只能编辑未审核的入库单");

        // 校验价格逻辑
        GoodsIncomeDetailCreateDTO forValidation = new GoodsIncomeDetailCreateDTO();
        BeanUtils.copyProperties(dto, forValidation);
        validatePriceLogic(Collections.singletonList(forValidation));

        // 3. 检查货品条码唯一性
        if (StrUtil.isNotBlank(dto.getGoodsSn()) && !dto.getGoodsSn().equals(detail.getGoodsSn())) {
            // 检查条码是否已存在
            GoodsEntity existingGoods = goodsMapper.selectOneByQuery(
                QueryWrapper.create()
                    .where(GoodsEntity::getGoodsSn).eq(dto.getGoodsSn())
                    .and(GoodsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
            );
            CommonUtils.abortIf(existingGoods != null, "货品条码已存在");
        }

        // 4. 更新明细基本信息（根据字段权限）
        updateDetailFieldsWithPermissionCheck(detail, dto);

        // 5. 更新明细
        boolean success = goodsIncomeDetailMapper.update(detail) > 0;
        CommonUtils.abortIf(!success, "更新入库单明细失败");

        // 收集所有需要更新状态的图片ID
        Set<Long> oldImageIds = new HashSet<>();
        Set<Long> newImageIds = new HashSet<>();

        // 6. 处理自定义字段
        if (dto.getCustomColumn() != null) {
            // 获取原有自定义字段
            List<GoodsIncomeHasColumnsEntity> oldColumns = goodsIncomeHasColumnsMapper.selectListByQuery(
                QueryWrapper.create()
                    .where(GoodsIncomeHasColumnsEntity::getIncomeDetailId).eq(detail.getId())
                    .and(GoodsIncomeHasColumnsEntity::getCompanyId).eq(detail.getCompanyId())
            );

            // 收集原有自定义字段中的图片ID
            oldColumns.stream()
                .filter(column -> column.getImageId() != null)
                .forEach(column -> oldImageIds.add(column.getImageId().longValue()));

            // 收集新自定义字段中的图片ID
            dto.getCustomColumn().stream()
                .filter(column -> column.getImageId() != null)
                .forEach(column -> newImageIds.add(column.getImageId().longValue()));

            // 删除原有自定义字段
            goodsIncomeHasColumnsMapper.deleteByQuery(
                QueryWrapper.create()
                    .where(GoodsIncomeHasColumnsEntity::getIncomeDetailId).eq(detail.getId())
                    .and(GoodsIncomeHasColumnsEntity::getCompanyId).eq(detail.getCompanyId())
            );

            // 添加新的自定义字段（根据权限过滤）
            if (!dto.getCustomColumn().isEmpty()) {
                List<GoodsIncomeHasColumnsEntity> columns = dto.getCustomColumn().stream()
                    .filter(column -> ColumnEncryptUtil.signCanEdit(column.getColumnSign())) // 权限检查
                    .map(column -> {
                        GoodsIncomeHasColumnsEntity columnEntity = new GoodsIncomeHasColumnsEntity();
                        columnEntity.setCompanyId(detail.getCompanyId());
                        columnEntity.setIncomeId(detail.getReceiveId());
                        columnEntity.setIncomeDetailId(detail.getId().intValue());
                        columnEntity.setColumnId(column.getColumnId());
                        columnEntity.setColumnSign(column.getColumnSign());
                        columnEntity.setValue(column.getValueStr());
                        columnEntity.setImageId(column.getImageId() == null ? 0 : column.getImageId());
                        return columnEntity;
                    })
                    .collect(Collectors.toList());
                if (!columns.isEmpty()) {
                    goodsIncomeHasColumnsMapper.insertBatch(columns);
                }
            }
        }

        // 7. 处理图片
        if (dto.getImage() != null) {
            // 获取原有图片
            List<GoodsIncomeHasImagesEntity> oldImages = goodsIncomeHasImagesMapper.selectListByQuery(
                QueryWrapper.create()
                    .where(GoodsIncomeHasImagesEntity::getIncomeDetailId).eq(detail.getId())
                    .and(GoodsIncomeHasImagesEntity::getCompanyId).eq(detail.getCompanyId())
            );

            // 收集原有图片ID
            oldImages.forEach(img -> oldImageIds.add(img.getImageId().longValue()));

            // 收集新图片ID
            dto.getImage().forEach(img -> newImageIds.add(img.getId()));

            // 删除原有图片关联
            goodsIncomeHasImagesMapper.deleteByQuery(
                QueryWrapper.create()
                    .where(GoodsIncomeHasImagesEntity::getIncomeDetailId).eq(detail.getId())
                    .and(GoodsIncomeHasImagesEntity::getCompanyId).eq(detail.getCompanyId())
            );

            // 添加新的图片关联
            if (!dto.getImage().isEmpty()) {
                List<GoodsIncomeHasImagesEntity> images = new ArrayList<>();
                for (int i = 0; i < dto.getImage().size(); i++) {
                    FileItemDTO image = dto.getImage().get(i);
                    GoodsIncomeHasImagesEntity imageEntity = new GoodsIncomeHasImagesEntity();
                    imageEntity.setCompanyId(detail.getCompanyId());
                    imageEntity.setIncomeId(detail.getReceiveId());
                    imageEntity.setIncomeDetailId(detail.getId().intValue());
                    imageEntity.setImageId(image.getId().intValue());
                    imageEntity.setUrl(image.getUrl());
                    imageEntity.setSort(i + 1);
                    images.add(imageEntity);
                }
                goodsIncomeHasImagesMapper.insertBatch(images);
            }
        }

        // 8. 一次性更新所有图片状态
        // 将不再使用的图片状态设为未使用
        List<Long> unusedImageIds = oldImageIds.stream()
            .filter(imageId -> !newImageIds.contains(imageId))
            .collect(Collectors.toList());
        if (!unusedImageIds.isEmpty()) {
            CommonUtils.batchUpdateFileStatus(unusedImageIds, 0);
        }
        // 将新使用的图片状态设为已使用
        List<Long> newImageIdsList = new ArrayList<>(newImageIds);
        if (!newImageIdsList.isEmpty()) {
            CommonUtils.batchUpdateFileStatus(newImageIdsList, 1);
        }

        // 9. 更新入库单统计信息
        Row stats = calculateIncomeStats(income.getId().intValue(), income.getCompanyId());

        // 更新入库单
        income.setTotalWeight(stats.getBigDecimal("total_weight"));
        income.setTotalNetGoldWeight(stats.getBigDecimal("total_net_gold_weight"));
        income.setTotalNetSilverWeight(stats.getBigDecimal("total_net_silver_weight"));
        income.setTotalCostPrice(stats.getLong("total_cost_price"));
        mapper.update(income);

        // 10. 记录操作日志
        OpLogUtils.appendOpLog(income.getIncomeCode(), "更新入库单明细", null);

        return true;
    }
}