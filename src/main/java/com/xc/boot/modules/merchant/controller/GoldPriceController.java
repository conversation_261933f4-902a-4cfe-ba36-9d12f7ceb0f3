package com.xc.boot.modules.merchant.controller;

import cn.hutool.core.lang.Assert;
import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Update;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.modules.merchant.model.dto.GoldPriceChangeDTO;
import com.xc.boot.modules.merchant.model.dto.GoldPriceDTO;
import com.xc.boot.modules.merchant.model.query.GoldPricePageQuery;
import com.xc.boot.modules.merchant.model.vo.GoldPricePageVO;
import com.xc.boot.modules.merchant.model.vo.GoldPriceVo;
import com.xc.boot.modules.merchant.service.GoldPriceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @ClassName GoldPriceController
 * @Date: 2025/6/9 16:48
 * @Description: 实时金价接口
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "系统配置-金价设置")
@RequestMapping("/api/goldPrice")
public class GoldPriceController {
    private final GoldPriceService goldPriceService;


    @Operation(summary = "同步金价")
    @GetMapping("/sync")
    public Result<GoldPriceVo> sync() {
        GoldPriceVo goldPriceVo = goldPriceService.syncGoldPrice();
        return Result.success(goldPriceVo);
    }

    @Operation(summary = "当前金价")
    @GetMapping("/realtime")
    public Result<GoldPriceVo> realtime() {
        GoldPriceVo goldPriceVo = goldPriceService.realtime();
        return Result.success(goldPriceVo);
    }

    @Operation(summary = "金价列表")
    @PostMapping("/list")
    public PageResult<GoldPricePageVO> pageList(@RequestBody GoldPricePageQuery queryParams) {
        Page<GoldPricePageVO> page = goldPriceService.pageList(queryParams);
        return PageResult.success(page);
    }

    @Operation(summary = "新增金价")
    @PostMapping
    public Result<?> addGoldPrice(@RequestBody @Validated(Create.class) GoldPriceDTO goldPriceDTO) {
        Assert.isTrue(goldPriceDTO.getSalePrice().compareTo(goldPriceDTO.getRecyclePrice()) >= 0,  "销售价不能小于回收价");
        Boolean result = goldPriceService.saveGoldPrice(goldPriceDTO);
        return Result.judge(result);
    }

    @Operation(summary = "修改金价")
    @PutMapping
    public Result<?> editGoldPrice(@RequestBody @Validated(Update.class) GoldPriceDTO goldPriceDTO) {
        Assert.isTrue(goldPriceDTO.getSalePrice().compareTo(goldPriceDTO.getRecyclePrice()) >= 0,  "销售价不能小于回收价");
        Boolean result = goldPriceService.saveGoldPrice(goldPriceDTO);
        return Result.judge(result);
    }

    @Operation(summary = "启用/停用金价")
    @GetMapping("/status")
    public Result<?> updateGoldPriceStatus(@RequestParam @Validated @NotNull(message = "金价ID不能为空") Long id) {
        Boolean result = goldPriceService.updateGoldPriceStatus(id);
        return Result.judge(result);
    }

    @Operation(summary = "批量调整金价")
    @PostMapping("/change")
    public Result<?> changePrice(@RequestBody @Validated GoldPriceChangeDTO changeDTO) {
        Boolean result = goldPriceService.changePrice(changeDTO);
        return Result.judge(result);
    }


}
