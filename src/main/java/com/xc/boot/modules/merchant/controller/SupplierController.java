package com.xc.boot.modules.merchant.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.modules.merchant.model.entity.SupplierEntity;
import com.xc.boot.modules.merchant.model.query.SupplierPageQuery;
import com.xc.boot.modules.merchant.model.vo.SupplierPageVO;
import com.xc.boot.modules.merchant.service.SupplierService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

import jakarta.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 供应商管理控制器
 */
@Tag(name = "系统配置-供应商管理")
@RestController
@RequestMapping("/api/merchant/supplier")
@RequiredArgsConstructor
public class SupplierController {

    private final SupplierService supplierService;

    @Operation(summary = "分页查询供应商")
    @GetMapping("/page")
    public PageResult<SupplierPageVO> getSupplierPage(SupplierPageQuery queryParams) {
        return PageResult.success(supplierService.getSupplierPage(queryParams));
    }

    @Operation(summary = "新增供应商")
    @PostMapping
    public Result<Boolean> addSupplier(@Validated @RequestBody SupplierEntity entity) {
        return Result.judge(supplierService.saveSupplier(entity));
    }

    @Operation(summary = "修改供应商")
    @PutMapping
    public Result<Boolean> updateSupplier(@Validated @RequestBody SupplierEntity entity) {
        return Result.judge(supplierService.saveSupplier(entity));
    }

    @Operation(summary = "修改供应商状态")
    @PutMapping("/status")
    public Result<Boolean> updateSupplierStatus(@RequestParam Long id, @RequestParam Integer status) {
        return Result.judge(supplierService.updateSupplierStatus(id, status));
    }

    @Operation(summary = "删除供应商")
    @DeleteMapping
    public Result<?> deleteSupplier(@RequestBody @Valid DeleteRequest request) {
        return Result.judge(supplierService.deleteSupplier(request.getId()));
    }
} 