package com.xc.boot.modules.merchant.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 小类表单数据传输对象
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "子类表单数据对象")
public class SubclassFormDTO {

    /**
     * 小类ID
     */
    @Schema(description = "ID")
    private Long id;

    /**
     * 小类名称
     */
    @Schema(description = "子类名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "小类名称不能为空")
    @Size(max = 50, message = "小类名称长度不能超过50个字符")
    private String name;

    /**
     * 所属大类ID
     */
    @NotNull(message = "所属大类不能为空")
    private Integer categoryId;

    /**
     * 状态(0:禁用|1:启用)
     */
    @Schema(description = "状态(0-禁用 1-启用)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 排序
     */
    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    @Min(value = 0, message = "排序值不能小于0")
    @Max(value = 999, message = "排序值不能大于999")
    private Integer sort;

    /**
     * 说明
     */
    @Schema(description = "备注")
    @Size(max = 200, message = "备注长度不能超过200个字符")
    private String remark;
}