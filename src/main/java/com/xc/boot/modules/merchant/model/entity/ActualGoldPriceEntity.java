package com.xc.boot.modules.merchant.model.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.github.xiaoymin.knife4j.annotations.Ignore;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import com.xc.boot.common.listener.CreatedByListenerFlag;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


@Getter
@Setter
@Accessors(chain = true)
@Builder
@Table(value = "actual_gold_price")
public class ActualGoldPriceEntity extends BaseEntity implements CreatedByListenerFlag {

    /**
     * 公司id
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 黄金价格
     */
    @Column(value = "gold_price")
    private BigDecimal goldPrice;

    /**
     * 白金价格
     */
    @Column(value = "platinum_price")
    private BigDecimal platinumPrice;

    /**
     * 白银价格
     */
    @Column(value = "silver_price")
    private BigDecimal silverPrice;

    /**
     * 创建人
     */
    @Column(value = "created_by")
    private Long createdBy;

    @JsonIgnore
    @Column(ignore = true)
    private BigDecimal goldPriceSale;
    @JsonIgnore
    @Column(ignore = true)
    private BigDecimal platinumPriceSale;
    @JsonIgnore
    @Column(ignore = true)
    private BigDecimal silverPriceSale;
    @JsonIgnore
    @Column(ignore = true)
    private BigDecimal goldPriceRecycle;
    @JsonIgnore
    @Column(ignore = true)
    private BigDecimal platinumPriceRecycle;
    @JsonIgnore
    @Column(ignore = true)
    private BigDecimal silverPriceRecycle;

}
