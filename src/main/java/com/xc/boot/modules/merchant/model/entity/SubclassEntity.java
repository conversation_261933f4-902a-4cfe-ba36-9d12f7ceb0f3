package com.xc.boot.modules.merchant.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 小类实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "subclass")
public class SubclassEntity extends BaseEntity {

    /**
     * 商户ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 小类名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 所属大类ID
     */
    @Column(value = "category_id")
    private Integer categoryId;

    /**
     * 状态(0:禁用|1:启用)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 排序
     */
    @Column(value = "sort")
    private Integer sort;

    /**
     * 说明
     */
    @Column(value = "remark")
    private String remark;
}