package com.xc.boot.modules.merchant.service;

import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.merchant.model.dto.GoodsColumnFormDTO;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.modules.merchant.model.query.GoodsColumnQuery;
import com.xc.boot.modules.merchant.model.vo.GoodsColumnVO;

import java.util.List;

/**
 * 商品字段服务接口
 */
public interface GoodsColumnService extends IService<GoodsColumnEntity> {

    /**
     * 获取商品字段列表
     *
     * @param query 查询参数
     * @return 商品字段列表
     */
    List<GoodsColumnVO> getGoodsColumnList(GoodsColumnQuery query);

    /**
     * 保存商品字段
     *
     * @param form 商品字段表单数据
     * @return 是否成功
     */
    boolean saveGoodsColumn(GoodsColumnFormDTO form);

    /**
     * 删除商品字段
     *
     * @param id 商品字段ID
     * @return 是否成功
     */
    boolean deleteGoodsColumn(Long id);

    /**
     * 初始化默认字段
     * 
     * 当商户首次创建时，需要初始化默认的商品字段配置。
     * 该方法会检查当前商户是否已存在默认字段，如果不存在则创建默认字段。
     */
    void initDefaultColumns();

    /**
     * 获取商户字段列表
     * 
     * @return 商户字段列表
     */
    List<GoodsColumnEntity> getGoodsColumnListByCompanyId(Long companyId);
} 