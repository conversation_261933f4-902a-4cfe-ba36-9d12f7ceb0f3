package com.xc.boot.modules.merchant.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.merchant.model.dto.SubclassFormDTO;
import com.xc.boot.modules.merchant.model.entity.SubclassEntity;
import com.xc.boot.modules.merchant.model.query.SubclassPageQuery;
import com.xc.boot.modules.merchant.model.vo.SubclassPageVO;

/**
 * 小类服务接口
 */
public interface SubclassService extends IService<SubclassEntity> {

    /**
     * 获取小类分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<SubclassPageVO> getSubclassPage(SubclassPageQuery queryParams);

    /**
     * 保存小类
     *
     * @param form 小类表单数据
     * @return 是否成功
     */
    boolean saveSubclass(SubclassFormDTO form);

    /**
     * 删除小类
     *
     * @param id 小类ID
     * @return 是否成功
     */
    boolean deleteSubclass(Long id);
}