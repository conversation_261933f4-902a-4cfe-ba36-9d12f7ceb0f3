package com.xc.boot.modules.merchant.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.http.Header;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateChain;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.constant.RedisConstants;
import com.xc.boot.common.enums.CategoryEnum;
import com.xc.boot.common.enums.QualityEnum;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.result.ResultCode;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.common.util.excel.ExcelUtil;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtil;
import com.xc.boot.config.property.GoldPriceProperties;
import com.xc.boot.core.security.model.SysUserDetails;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.merchant.mapper.ActualGoldPriceMapper;
import com.xc.boot.modules.merchant.mapper.GoldPriceMapper;
import com.xc.boot.modules.merchant.mapper.QualityMapper;
import com.xc.boot.modules.merchant.model.dto.GoldPriceChangeDTO;
import com.xc.boot.modules.merchant.model.dto.GoldPriceDTO;
import com.xc.boot.modules.merchant.model.entity.ActualGoldPriceEntity;
import com.xc.boot.modules.merchant.model.entity.GoldPriceEntity;
import com.xc.boot.modules.merchant.model.entity.QualityEntity;
import com.xc.boot.modules.merchant.model.query.GoldPricePageQuery;
import com.xc.boot.modules.merchant.model.vo.GoldPricePageVO;
import com.xc.boot.modules.merchant.model.vo.GoldPriceVo;
import com.xc.boot.modules.merchant.service.GoldPriceService;
import com.xc.boot.system.model.entity.SysExportEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.xc.boot.modules.merchant.model.entity.table.ActualGoldPriceTableDef.ACTUAL_GOLD_PRICE;
import static com.xc.boot.modules.merchant.model.entity.table.GoldPriceTableDef.GOLD_PRICE;
import static com.xc.boot.modules.merchant.model.entity.table.QualityTableDef.QUALITY;

/**
 * <AUTHOR>
 * @ClassName GoldPriceServiceImpl
 * @Date: 2025/6/9 17:04
 * @Description: 描述
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoldPriceServiceImpl extends ServiceImpl<GoldPriceMapper, GoldPriceEntity> implements GoldPriceService {
    private final static String AUTH_PRE = "APPCODE ";
    private final GoldPriceProperties goldPriceProperties;
    private final ActualGoldPriceMapper actualGoldPriceMapper;
    private final ListFillService fillService;
    private final QualityMapper qualityMapper;
    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    @Transactional
    public GoldPriceVo syncGoldPrice() {
        SysUserDetails userDetails = SecurityUtils.getUser().orElse(new SysUserDetails());
        String redisKey = RedisConstants.GOLD_PRICE_PLUSH_PREFIX + userDetails.getUsername();
        if (redisTemplate.hasKey(redisKey)) {
            throw new BusinessException("30分钟内仅能同步一次金价,请稍候再试");
        }
        BigDecimal goldPrice;
        BigDecimal goldPriceBuy;
        BigDecimal goldPriceSell;
        BigDecimal platinumPrice;
        BigDecimal platinumPriceBuy;
        BigDecimal platinumPriceSell;
        BigDecimal silverPrice;
        BigDecimal silverPriceBuy;
        BigDecimal silverPriceSell;
        try (HttpResponse responseGold = HttpUtil.createGet(goldPriceProperties.getUrlGold())
                .header(Header.AUTHORIZATION, AUTH_PRE + goldPriceProperties.getAppCodeGold())
                .execute();
             HttpResponse responseSilver = HttpUtil.createGet(goldPriceProperties.getUrlSilver())
                     .header(Header.AUTHORIZATION, AUTH_PRE + goldPriceProperties.getAppCodeSilver())
                     .execute()) {
            // 金价
            JSONObject responseObj = JSONUtil.parseObj(responseGold.body());
            Integer code = responseObj.getInt("code");
            Assert.isTrue(responseGold.isOk() && code != null && code == 1, "请求金价接口失败");
            JSONObject jsonObject = responseObj.getJSONObject("data").getJSONObject("list");
            goldPrice = jsonObject.getJSONObject("Au9999").getBigDecimal("price");
            goldPriceBuy = jsonObject.getJSONObject("Au9999").getBigDecimal("buyprice");
            goldPriceSell = jsonObject.getJSONObject("Au9999").getBigDecimal("sellprice");
            if (goldPrice == null || goldPrice.compareTo(BigDecimal.ZERO) == 0) {
                throw new BusinessException("金价数据异常,暂停同步");
            }
            if (goldPriceSell == null || goldPriceSell.compareTo(BigDecimal.ZERO) == 0) {
                goldPriceSell = goldPrice;
            }
            if (goldPriceBuy == null || goldPriceBuy.compareTo(BigDecimal.ZERO) == 0) {
                goldPriceBuy = goldPriceSell;
            }

            platinumPrice = jsonObject.getJSONObject("PT9995").getBigDecimal("price");
            platinumPriceBuy = jsonObject.getJSONObject("PT9995").getBigDecimal("buyprice");
            platinumPriceSell = jsonObject.getJSONObject("PT9995").getBigDecimal("sellprice");
            if (platinumPrice == null || platinumPrice.compareTo(BigDecimal.ZERO) == 0) {
                throw new BusinessException("白金数据异常,暂停同步");
            }
            if (platinumPriceSell == null || platinumPriceSell.compareTo(BigDecimal.ZERO) == 0) {
                platinumPriceSell = platinumPrice;
            }
            if (platinumPriceBuy == null || platinumPriceBuy.compareTo(BigDecimal.ZERO) == 0) {
                platinumPriceBuy = platinumPriceSell;
            }
            // 银价
            responseObj = JSONUtil.parseObj(responseSilver.body());
            code = responseObj.getInt("code");
            Assert.isTrue(responseSilver.isOk() && code != null && code == 1, "请求银价接口失败");
            JSONArray array = responseObj.getJSONObject("data").getJSONArray("list");
            silverPrice = array.getJSONObject(1).getBigDecimal("price");
            silverPriceBuy = array.getJSONObject(1).getBigDecimal("minprice");
            silverPriceSell = array.getJSONObject(1).getBigDecimal("maxprice");
            if (silverPrice == null || silverPrice.compareTo(BigDecimal.ZERO) == 0) {
                throw new BusinessException("银价数据异常,暂停同步");
            }
            silverPrice = silverPrice.divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP);
            if (silverPriceSell == null || silverPriceSell.compareTo(BigDecimal.ZERO) == 0) {
                silverPriceSell = silverPrice;
            }else {
                silverPriceSell = silverPriceSell.divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP);
            }
            if (silverPriceBuy == null || silverPriceBuy.compareTo(BigDecimal.ZERO) == 0) {
                silverPriceBuy = silverPriceSell;
            }else {
                silverPriceBuy = silverPriceBuy.divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP);
            }
        }catch (Exception e) {
            log.error("同步金价失败", e);
            throw new BusinessException("同步金价失败");
        }
        // 保存金价同步结果
        ActualGoldPriceEntity priceEntity = ActualGoldPriceEntity.builder()
                .goldPrice(PriceUtil.yuan2fen(goldPriceSell))
                .platinumPrice(PriceUtil.yuan2fen(platinumPriceSell))
                .silverPrice(PriceUtil.yuan2fen(silverPriceSell))
                .goldPriceSale(PriceUtil.yuan2fen(goldPriceSell))
                .goldPriceRecycle(PriceUtil.yuan2fen(goldPriceBuy))
                .platinumPriceSale(PriceUtil.yuan2fen(platinumPriceSell))
                .platinumPriceRecycle(PriceUtil.yuan2fen(platinumPriceBuy))
                .silverPriceSale(PriceUtil.yuan2fen(silverPriceSell))
                .silverPriceRecycle(PriceUtil.yuan2fen(silverPriceBuy))
                .companyId(userDetails.getCompanyId())
                .build();
        priceEntity.setCreatedAt(new Date());
        actualGoldPriceMapper.insert(priceEntity);

        // 如果没有生效金价，创建生效金价
        initGoldPrice(priceEntity);

        redisTemplate.opsForValue().set(redisKey, 0, 30, TimeUnit.MINUTES);
        OpLogUtils.appendOpLog("金价设置-同步金价", "同步上海黄金交易所价格", null);
        return realtime();
    }

    @Override
    public GoldPriceVo realtime() {
        Long companyId = SecurityUtils.getCompanyId();
        List<GoldPriceVo> goldPriceVos = actualGoldPriceMapper.selectListByQueryAs(QueryWrapper.create()
                .where(ACTUAL_GOLD_PRICE.COMPANY_ID.eq(companyId))
                .select(ACTUAL_GOLD_PRICE.ID,
                        ACTUAL_GOLD_PRICE.COMPANY_ID,
                        ACTUAL_GOLD_PRICE.GOLD_PRICE.divide(100).as("goldPrice"),
                        ACTUAL_GOLD_PRICE.PLATINUM_PRICE.divide(100).as("platinumPrice"),
                        ACTUAL_GOLD_PRICE.SILVER_PRICE.divide(100).as("silverPrice"),
                        ACTUAL_GOLD_PRICE.CREATED_AT)
                .orderBy(ACTUAL_GOLD_PRICE.CREATED_AT, false)
                .limit(2), GoldPriceVo.class);
        if (CollectionUtil.isEmpty(goldPriceVos)) {
            return new GoldPriceVo();
        }
        GoldPriceVo goldPriceVo = goldPriceVos.getFirst();
        GoldPriceVo lastVo = goldPriceVos.size() > 1 ? goldPriceVos.get(1) : new GoldPriceVo();
        goldPriceVo.setGoldPriceChange(goldPriceVo.getGoldPrice().subtract(lastVo.getGoldPrice()));
        goldPriceVo.setPlatinumPriceChange(goldPriceVo.getPlatinumPrice().subtract(lastVo.getPlatinumPrice()));
        goldPriceVo.setSilverPriceChange(goldPriceVo.getSilverPrice().subtract(lastVo.getSilverPrice()));
        return goldPriceVo;
    }

    @Override
    public Page<GoldPricePageVO> pageList(GoldPricePageQuery queryParams) {
        QueryWrapper queryWrapper = QueryWrapper.create().where(GOLD_PRICE.COMPANY_ID.eq(SecurityUtils.getCompanyId()));
        if (StringUtils.isNotBlank(queryParams.getCategoryIds())) {
            queryWrapper.where(GOLD_PRICE.CATEGORY_ID.in(List.of(queryParams.getCategoryIds().split(","))));
        }
        queryWrapper.where(GOLD_PRICE.QUALITY_ID.eq(queryParams.getQualityId(), queryParams.getQualityId() != null));
        queryWrapper.where(GOLD_PRICE.ACTIVE_TIME.between(queryParams.getTimeRange(), queryParams.getTimeRange() != null));
        queryWrapper.orderBy(GOLD_PRICE.STATUS, false).orderBy(GOLD_PRICE.UPDATED_AT, false);

        // 导出
        if (queryParams.getExport().equals(1)) {
            doExport(queryWrapper, queryParams);
            return new Page<>();
        }
        Page<GoldPricePageVO> goldPricePage = this.pageAs(new Page<>(queryParams.getPageNum(), queryParams.getPageSize()), queryWrapper, GoldPricePageVO.class);
        fillList(goldPricePage.getRecords());
        return goldPricePage;
    }

    private void doExport(QueryWrapper queryWrapper, GoldPricePageQuery queryParams) {
        if (queryParams.getIds() != null && !queryParams.getIds().isEmpty()) {
            queryWrapper.in(GoldPriceEntity::getId, queryParams.getIds());
        }
        ExcelUtil.of(this.mapper, queryWrapper, GoldPricePageVO.class, "gold_price", "金价设置")
                .getData((mapper, wrapper) -> {
                    if (mapper instanceof GoldPriceMapper goldPriceMapper) {
                        List<GoldPricePageVO> list = goldPriceMapper.selectListByQueryAs(wrapper, GoldPricePageVO.class);
                        fillList(list);
                        return list;
                    }
                    return new ArrayList<>();
                }).doExport();
    }

    private void fillList(List<GoldPricePageVO> records) {
        if (records.isEmpty()) {
            return;
        }
        Set<Long> userIds = new HashSet<>(List.of(0L));
        Set<Long> qualityIds = new HashSet<>(List.of(0L));
        records.forEach(goldPricePageVO -> {
            userIds.add(goldPricePageVO.getCreatedBy());
            userIds.add(goldPricePageVO.getUpdatedBy());
            qualityIds.add(goldPricePageVO.getQualityId());
        });
        ListFillUtil.of(records)
                .peek(obj -> {
                    GoldPricePageVO vo = (GoldPricePageVO) obj;
                    vo.setSalePrice(PriceUtil.fen2yuan(vo.getSalePrice()));
                    vo.setRecyclePrice(PriceUtil.fen2yuan(vo.getRecyclePrice()));
                })
                .build(fillService::getUserNameByUserId, userIds, "createdBy", "createdByName")
                .build(fillService::getUserNameByUserId, userIds, "updatedBy", "updatedByName")
                .build(fillService::getQualityNameById, qualityIds, "qualityId", "quality")
                .build(fillService::getCategoryNameById, null, "categoryId", "category")
                .handle();
    }

    private void initGoldPrice(ActualGoldPriceEntity priceEntity) {
        initPrice(CategoryEnum.GOLD.getValue(), priceEntity.getGoldPriceSale(), priceEntity.getGoldPriceRecycle(), QualityEnum.GOLD.getName(), QualityEnum.GOLD.getContent());
        initPrice(CategoryEnum.SILVER.getValue(), priceEntity.getSilverPriceSale(), priceEntity.getSilverPriceRecycle(), QualityEnum.SILVER.getName(), QualityEnum.SILVER.getContent());
        initPrice(CategoryEnum.PLATINUM.getValue(), priceEntity.getPlatinumPriceSale(), priceEntity.getPlatinumPriceRecycle(), QualityEnum.PLATINUM.getName(), QualityEnum.PLATINUM.getContent());
    }

    private QualityEntity initQuality(String name, String content, Long categoryId) {
        QualityEntity quality = qualityMapper.selectOneByQuery(QueryWrapper.create()
                .where(QUALITY.CATEGORY_ID.eq(SecurityUtils.getCompanyId()))
                .where(QUALITY.NAME.eq(name)));
        if (quality != null && quality.getStatus().equals(0)) {
            quality.setStatus(1);
            qualityMapper.update(quality);
        }
        if (quality == null) {
            quality = QualityEntity.builder()
                    .companyId(SecurityUtils.getCompanyId())
                    .categoryId(categoryId)
                    .name(name)
                    .content(content)
                    .status(1)
                    .build();
            qualityMapper.insert(quality);
        }
        return quality;
    }

    private void initPrice(Long categoryId, BigDecimal sellPrice, BigDecimal recyclePrice, String name, String content) {
        List<GoldPriceEntity> priceList = this.mapper.selectListByQuery((QueryWrapper.create()
                .where(GOLD_PRICE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOLD_PRICE.CATEGORY_ID.eq(categoryId))
                .where(GOLD_PRICE.STATUS.eq(1))));
        if (!CollectionUtils.isEmpty(priceList)) {
            priceList.forEach(e -> {
                e.setSalePrice(sellPrice);
                e.setRecyclePrice(recyclePrice);
                e.setRemark("同步上海黄金交易所价格");
            });
            this.updateBatch(priceList);
            return;
        }
        QualityEntity quality = qualityMapper.selectOneByQuery(QueryWrapper.create()
                .where(QUALITY.STATUS.eq(1))
                .where(QUALITY.CATEGORY_ID.eq(categoryId)));
        if (quality == null) {
            return;
        }
        GoldPriceEntity priceEntity = GoldPriceEntity.builder()
                .companyId(SecurityUtils.getCompanyId())
                .categoryId(categoryId)
                .qualityId(quality.getId())
                .salePrice(sellPrice)
                .recyclePrice(recyclePrice)
                .status(1)
                .activeTime(new Date())
                .remark("同步上海黄金交易所价格")
                .build();
        this.save(priceEntity);
    }

    @Override
    public Boolean saveGoldPrice(GoldPriceDTO goldPriceDTO) {
        Long companyId = SecurityUtils.getCompanyId();
        GoldPriceEntity goldPrice;
        GoldPriceEntity newGoldPrice;
        if (goldPriceDTO.getId() != null) {
            goldPrice = this.mapper.selectOneByQuery(QueryWrapper.create()
                    .where(GOLD_PRICE.COMPANY_ID.eq(companyId))
                    .where(GOLD_PRICE.ID.eq(goldPriceDTO.getId())));
            Assert.notNull(goldPrice, "数据不存在");
            newGoldPrice = BeanUtil.copyProperties(goldPrice, GoldPriceEntity.class);
        }else {
            goldPrice = new GoldPriceEntity();
            newGoldPrice = goldPrice;
            newGoldPrice.setCategoryId(goldPriceDTO.getCategoryId());
            newGoldPrice.setQualityId(goldPriceDTO.getQualityId());
            newGoldPrice.setCompanyId(companyId);

        }
        newGoldPrice.setSalePrice(PriceUtil.yuan2fen(goldPriceDTO.getSalePrice()));
        newGoldPrice.setRecyclePrice(PriceUtil.yuan2fen(goldPriceDTO.getRecyclePrice()));
        newGoldPrice.setRemark(goldPriceDTO.getRemark());
        boolean result = this.saveOrUpdate(newGoldPrice);
        if (goldPriceDTO.getId() == null) {
            OpLogUtils.appendOpLog("金价设置-新增金价", "新增金价", newGoldPrice);
        }else {
            OpLogUtils.appendOpLog("金价设置-修改金价", "修改金价:" + goldPriceDTO.getId(), Map.of("修改前", goldPrice , "修改后", newGoldPrice));
        }
        return result;
    }

    @Override
    public Boolean updateGoldPriceStatus(Long id) {
        Long companyId = SecurityUtils.getCompanyId();
        GoldPriceEntity goldPrice = this.mapper.selectOneByQuery(QueryWrapper.create()
                .where(GOLD_PRICE.COMPANY_ID.eq(companyId))
                .where(GOLD_PRICE.ID.eq(id)));
        Assert.notNull(goldPrice, "数据不存在");
        Integer status;
        if (goldPrice.getStatus().equals(1)) {
            status = -1;
        }else {
            status = 1;
        }

        // 停用
        if (!status.equals(1)) {
            goldPrice.setStatus(-1);
            goldPrice.setExpireTime(new Date());
            this.mapper.update(goldPrice);
            OpLogUtils.appendOpLog("金价设置-停用金价", "停用金价:" + id, null);
            return true;
        }
        // 启用
        long count = this.count(QueryWrapper.create()
                .where(GOLD_PRICE.STATUS.eq(1))
                .where(GOLD_PRICE.CATEGORY_ID.eq(goldPrice.getCategoryId()))
                .where(GOLD_PRICE.QUALITY_ID.eq(goldPrice.getQualityId()))
                .where(GOLD_PRICE.COMPANY_ID.eq(goldPrice.getCompanyId())));
        Assert.isTrue(count == 0, "该大类下的该成色已有启用金价，无法重复启用");
        goldPrice.setStatus(1);
        goldPrice.setActiveTime(new Date());
        goldPrice.setExpireTime(null);
        this.mapper.update(goldPrice, false);
        OpLogUtils.appendOpLog("金价设置-启用金价", "启用金价: id", null);
        return true;
    }

    @Override
    public Boolean changePrice(GoldPriceChangeDTO changeDTO) {
        Assert.notEmpty(changeDTO.getCategoryIds(), "大类id列表不能为空");
        if (changeDTO.getType().equals(1)) {
            Assert.isTrue(changeDTO.getNumber().compareTo(new BigDecimal("-100")) > 0, "降幅不能低于100%");
        }
        List<GoldPriceEntity> entities = this.mapper.selectListByQuery(QueryWrapper.create()
                .where(GOLD_PRICE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                .where(GOLD_PRICE.CATEGORY_ID.in(changeDTO.getCategoryIds()))
                .where(GOLD_PRICE.STATUS.eq(1)));
        for (GoldPriceEntity entity : entities) {
            if (changeDTO.getType().equals(1)) {
                BigDecimal percent = new BigDecimal("1").add(changeDTO.getNumber().divide(new BigDecimal("100"), 6, RoundingMode.HALF_UP));
                entity.setSalePrice(PriceUtil.formatDecimal(entity.getSalePrice().multiply(percent), 0));
                entity.setRecyclePrice(PriceUtil.formatDecimal(entity.getRecyclePrice().multiply(percent), 0));
            }
            if (changeDTO.getType().equals(2)) {
                BigDecimal amount = PriceUtil.yuan2fen(changeDTO.getNumber());
                entity.setSalePrice(PriceUtil.formatDecimal(entity.getSalePrice().add(amount), 0));
                if (entity.getSalePrice().compareTo(BigDecimal.ZERO) < 0) {
                    entity.setSalePrice(BigDecimal.ZERO);
                }
                entity.setRecyclePrice(PriceUtil.formatDecimal(entity.getRecyclePrice().subtract(amount), 0));
                if (entity.getRecyclePrice().compareTo(BigDecimal.ZERO) < 0) {
                    entity.setRecyclePrice(BigDecimal.ZERO);
                }
            }
            entity.setRemark(changeDTO.getRemark());
        }
        this.updateBatch(entities, false);
        return true;
    }

}
