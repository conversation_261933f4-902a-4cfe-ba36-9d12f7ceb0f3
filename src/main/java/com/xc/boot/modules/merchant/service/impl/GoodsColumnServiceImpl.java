package com.xc.boot.modules.merchant.service.impl;

import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.base.IBaseEnum;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.config.interceptor.model.BaseDbTables;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsHasColumnsMapper;
import com.xc.boot.modules.goods.model.entity.GoodsHasColumnsEntity;
import com.xc.boot.modules.income.mapper.GoodsIncomeHasColumnsMapper;
import com.xc.boot.modules.income.model.entity.GoodsIncomeHasColumnsEntity;
import com.xc.boot.modules.merchant.mapper.GoodsColumnMapper;
import com.xc.boot.modules.merchant.mapper.GoodsIncomeTemplateDetailMapper;
import com.xc.boot.modules.merchant.model.dto.GoodsColumnFormDTO;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateDetailEntity;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnCategoryEnum;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnSecretLevelEnum;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnTypeEnum;
import com.xc.boot.modules.merchant.model.query.GoodsColumnQuery;
import com.xc.boot.modules.merchant.model.vo.GoodsColumnVO;
import com.xc.boot.modules.merchant.service.GoodsColumnService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.UUID;

/**
 * 商品字段服务实现类
 */
@Service
@RequiredArgsConstructor
public class GoodsColumnServiceImpl extends ServiceImpl<GoodsColumnMapper, GoodsColumnEntity>
        implements GoodsColumnService {

    private final GoodsHasColumnsMapper goodsHasColumnsMapper;
    private final GoodsIncomeTemplateDetailMapper goodsIncomeTemplateDetailMapper;
    private final GoodsIncomeHasColumnsMapper goodsIncomeHasColumnsMapper;

    @Override
    public List<GoodsColumnVO> getGoodsColumnList(GoodsColumnQuery query) {
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.column(GoodsColumnEntity::getId),
                        QueryMethods.column(GoodsColumnEntity::getCompanyId),
                        QueryMethods.column(GoodsColumnEntity::getName),
                        QueryMethods.column(GoodsColumnEntity::getSign),
                        QueryMethods.column(GoodsColumnEntity::getSecretLevel),
                        QueryMethods.column(GoodsColumnEntity::getCategory),
                        QueryMethods.column(GoodsColumnEntity::getType),
                        QueryMethods.column(GoodsColumnEntity::getIsMultiple),
                        QueryMethods.column(GoodsColumnEntity::getOptions),
                        QueryMethods.column(GoodsColumnEntity::getNumberPrecision),
                        QueryMethods.column(GoodsColumnEntity::getCreatedAt),
                        QueryMethods.column(GoodsColumnEntity::getUpdatedAt))
                .from(GoodsColumnEntity.class)
                // 商户ID条件
                .where(GoodsColumnEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                // 字段名称模糊查询
                .and(GoodsColumnEntity::getName).like(query.getName(), StringUtils.hasText(query.getName()));

        // 执行查询
        List<GoodsColumnEntity> list = this.list(queryWrapper);

        // 如果列表为空，则初始化默认字段
        if (list.isEmpty()) {
            this.initDefaultColumns();
            list = this.list(queryWrapper);
        }

        // 转换为VO
        return list.stream().map(entity -> {
            GoodsColumnVO vo = new GoodsColumnVO();
            BeanUtils.copyProperties(entity, vo);

            // 设置枚举文本
            vo.setSecretLevelLabel(
                    IBaseEnum.getLabelByValue(entity.getSecretLevel(), GoodsColumnSecretLevelEnum.class));
            vo.setCategoryLabel(IBaseEnum.getLabelByValue(entity.getCategory(), GoodsColumnCategoryEnum.class));
            vo.setTypeLabel(IBaseEnum.getLabelByValue(entity.getType(), GoodsColumnTypeEnum.class));

            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveGoodsColumn(GoodsColumnFormDTO form) {
        Long id = form.getId();
        GoodsColumnEntity entity = new GoodsColumnEntity();
        BeanUtils.copyProperties(form, entity);

        // 设置商户ID
        entity.setCompanyId(SecurityUtils.getCompanyId().intValue());

        // 检查字段名称是否重复
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(QueryMethods.column(GoodsColumnEntity::getId))
                .from(GoodsColumnEntity.class)
                .where(GoodsColumnEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .and(GoodsColumnEntity::getName).eq(entity.getName());

        // 如果是编辑，排除当前记录
        if (id != null) {
            queryWrapper.and(GoodsColumnEntity::getId).ne(id);
        }

        // 检查是否存在同名字段
        Long count = this.count(queryWrapper);
        CommonUtils.abortIf(count > 0, "字段名称已存在");

        if (id != null) {
            // 编辑
            GoodsColumnEntity oldEntity = this.getById(id);
            CommonUtils.abortIf(oldEntity == null, "商品字段不存在");

            // 编辑时不允许修改 category 和 sign
            entity.setCategory(oldEntity.getCategory());
            entity.setSign(oldEntity.getSign());

            boolean result = this.updateById(entity);
            if (result) {
                OpLogUtils.appendOpLog("商品字段-编辑", "编辑商品字段", entity);
            }
            return result;
        } else {
            // 新增
            // 设置类目为自定义
            entity.setCategory(GoodsColumnCategoryEnum.CUSTOM.getValue());
            // 生成UUID作为sign
            entity.setSign(UUID.randomUUID().toString());

            boolean result = this.save(entity);
            if (result) {
                OpLogUtils.appendOpLog("商品字段-新增", "新增商品字段", entity);
            }
            return result;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteGoodsColumn(Long id) {
        // 检查是否存在
        GoodsColumnEntity entity = this.getById(id);
        CommonUtils.abortIf(entity == null, "商品字段不存在");

        if (isColumnInUse(id)) {
            CommonUtils.abort("该字段已被使用，不允许删除");
        }

        // 删除模板明细关联数据
        goodsIncomeTemplateDetailMapper.deleteByQuery(QueryWrapper.create()
                .from(GoodsIncomeTemplateDetailEntity.class)
                .where(GoodsIncomeTemplateDetailEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .and(GoodsIncomeTemplateDetailEntity::getSign).eq(entity.getSign()));

        // 删除
        boolean result = this.removeById(id);
        if (result) {
            OpLogUtils.appendOpLog("商品字段-删除", "删除商品字段", entity);
        }

        return result;
    }

    /**
     * 检查字段是否被使用
     * 
     * @param columnId 字段ID
     * @return 是否被使用
     */
    private boolean isColumnInUse(Long columnId) {
        // 获取字段信息
        GoodsColumnEntity column = this.getById(columnId);
        if (column == null) {
            return false;
        }

        // 检查商品表中是否使用了该字段
        Long goodsCount = goodsHasColumnsMapper.selectCountByQuery(
                QueryWrapper.create()
                        .from(GoodsHasColumnsEntity.class)
                        .where(GoodsHasColumnsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                        .and(GoodsHasColumnsEntity::getColumnId).eq(columnId));

        if (goodsCount > 0) {
            return true;
        }

        // 检查入库单中是否使用了该字段
        Long incomeCount = goodsIncomeHasColumnsMapper.selectCountByQuery(
                QueryWrapper.create()
                        .from(GoodsIncomeHasColumnsEntity.class)
                        .where(GoodsIncomeHasColumnsEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                        .and(GoodsIncomeHasColumnsEntity::getColumnId).eq(columnId));

        return incomeCount > 0;
    }

    /**
     * 初始化默认字段
     */
    public void initDefaultColumns() {
        // 检查是否存在默认字段
        Long count = this.count(
                QueryWrapper.create()
                        .from(GoodsColumnEntity.class)
                        .where(GoodsColumnEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                        .where(GoodsColumnEntity::getCategory).eq(GoodsColumnCategoryEnum.COMMON.getValue()));

        if (count > 0) {
            return;
        }

        // 插入默认字段
        List<GoodsColumnEntity> columns = BaseDbTables.BASE_GOODS_COLUMNS.stream()
                .map(column -> {
                    GoodsColumnEntity entity = new GoodsColumnEntity();
                    entity.setCompanyId(SecurityUtils.getCompanyId().intValue());
                    entity.setName(column.get("name").toString());
                    entity.setSign(column.get("sign").toString());
                    entity.setSecretLevel(Integer.parseInt(column.get("secret_level").toString()));
                    entity.setType(Integer.parseInt(column.get("type").toString()));
                    entity.setCategory(GoodsColumnCategoryEnum.COMMON.getValue());
                    entity.setNumberPrecision(Integer.parseInt(column.get("number_precision").toString()));
                    return entity;
                })
                .collect(Collectors.toList());

        // 批量插入
        boolean result = this.saveBatch(columns);
        if (result) {
            OpLogUtils.appendOpLog("商品字段-初始化", "初始化商品默认字段", columns);
        }
    }

    @Override
    public List<GoodsColumnEntity> getGoodsColumnListByCompanyId(Long companyId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .from(GoodsColumnEntity.class)
                .where(GoodsColumnEntity::getCompanyId).eq(companyId);

        List<GoodsColumnEntity> list = this.list(queryWrapper);
        // 如果列表为空，则初始化默认字段
        if (list.isEmpty()) {
            this.initDefaultColumns();
            list = this.list(queryWrapper);
        }
        return list;
    }
}