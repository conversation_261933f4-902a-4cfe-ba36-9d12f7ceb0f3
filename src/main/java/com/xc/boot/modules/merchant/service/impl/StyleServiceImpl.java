package com.xc.boot.modules.merchant.service.impl;

import cn.hutool.core.lang.Assert;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.enums.JoinColumEnum;
import com.xc.boot.common.util.DataCheckUtil;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.merchant.mapper.StyleMapper;
import com.xc.boot.modules.merchant.model.dto.StyleFormDTO;
import com.xc.boot.modules.merchant.model.entity.StyleEntity;
import com.xc.boot.modules.merchant.model.query.StylePageQuery;
import com.xc.boot.modules.merchant.model.vo.StylePageVO;
import com.xc.boot.modules.merchant.service.StyleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * 款式服务实现类
 */
@Service
@RequiredArgsConstructor
public class StyleServiceImpl extends ServiceImpl<StyleMapper, StyleEntity> implements StyleService {

    @Override
    public Page<StylePageVO> getStylePage(StylePageQuery queryParams) {
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.column(StyleEntity::getId),
                        QueryMethods.column(StyleEntity::getName),
                        QueryMethods.column(StyleEntity::getRemark),
                        QueryMethods.column(StyleEntity::getStatus),
                        QueryMethods.column(StyleEntity::getSort),
                        QueryMethods.column(StyleEntity::getCreatedAt),
                        QueryMethods.column(StyleEntity::getUpdatedAt)
                )
                .from(StyleEntity.class)
                // 款式名称模糊查询
                .where(StyleEntity::getName).like(queryParams.getName(), StringUtils.hasText(queryParams.getName()))
                // 状态精确匹配
                .and(StyleEntity::getStatus).eq(queryParams.getStatus(), queryParams.getStatus() != null)
                .orderBy(StyleEntity::getId, false);

        // 添加排序条件
        queryWrapper.orderBy(StyleEntity::getId, false);

        // 执行分页查询并直接映射到VO
        return mapper.paginateAs(queryParams.getPageNum(), queryParams.getPageSize(), queryWrapper, StylePageVO.class);
    }

    @Override
    public boolean saveStyle(StyleFormDTO form) {
        Long styleId = form.getId();
        Long companyId = SecurityUtils.getCompanyId();
        Assert.notNull(companyId, "未获取到商户信息");

        // 编辑款式时，判断款式是否存在
        StyleEntity oldStyle = null;
        if (styleId != null) {
            oldStyle = this.getById(styleId);
            Assert.isTrue(oldStyle != null, "款式不存在");
            Assert.isTrue(oldStyle.getCompanyId().equals(companyId), "无权操作该款式");
        }

        // 检查款式名称是否已存在
        long count = this.count(
                QueryWrapper.create()
                        .from(StyleEntity.class)
                        .ne(StyleEntity::getId, styleId, styleId != null)
                        .and(StyleEntity::getCompanyId).eq(companyId)
                        .and(StyleEntity::getName).eq(form.getName()));
        Assert.isTrue(count == 0, "款式名称已存在");

        // 创建款式实体
        StyleEntity style = new StyleEntity();
        style.setName(form.getName());
        style.setRemark(form.getRemark());
        style.setStatus(form.getStatus());
        style.setSort(form.getSort());
        style.setCompanyId(companyId);

        // 如果是编辑，设置ID
        if (oldStyle != null) {
            style.setId(oldStyle.getId());
        }

        // 记录操作日志
        if (oldStyle == null) {
            OpLogUtils.appendOpLog("款式管理-新增款式", "新增款式: " + style.getName(), style);
        } else {
            OpLogUtils.appendOpLog("款式管理-编辑款式", "编辑款式: " + style.getName(), 
                Map.of("修改前", oldStyle, "修改后", style));
        }

        // 保存款式信息
        return this.saveOrUpdate(style);
    }

    @Override
    public boolean deleteStyle(Long id) {
        StyleEntity style = this.getById(id);
        Assert.isTrue(style != null, "款式不存在");
        Assert.isTrue(style.getCompanyId().equals(SecurityUtils.getCompanyId()), "无权操作该款式");
        Assert.isTrue(DataCheckUtil.checkJoinFieldDelete(id, JoinColumEnum.STYLE_ID), "款式已被使用，请先解除关联关系");
        // 记录操作日志
        OpLogUtils.appendOpLog("款式管理-删除款式", "删除款式: " + style.getName(), style);

        return this.removeById(id);
    }
}