package com.xc.boot.modules.pda.service;

import com.mybatisflex.core.service.IService;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.pda.model.form.RfidBindForm;
import com.xc.boot.modules.pda.model.vo.goodsBindVo;

public interface PdaGoodsService extends IService<GoodsEntity> {

    goodsBindVo selectOne(String goodsSn);

    void bind(RfidBindForm form);

    void unbind(RfidBindForm form);
}
