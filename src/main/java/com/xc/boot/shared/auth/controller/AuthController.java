package com.xc.boot.shared.auth.controller;

import cn.hutool.core.lang.Assert;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.annotation.Log;
import com.xc.boot.common.annotation.RepeatSubmit;
import com.xc.boot.common.annotation.methods.AnonymousPostMapping;
import com.xc.boot.common.enums.LogModuleEnum;
import com.xc.boot.common.enums.SideEnum;
import com.xc.boot.common.result.Result;
import com.xc.boot.common.result.ResultCode;
import com.xc.boot.shared.auth.model.*;
import com.xc.boot.shared.auth.service.AuthService;
import com.xc.boot.system.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.xc.boot.system.model.entity.table.SysUserTableDef.SYS_USER;


/**
 * 认证控制层
 *
 * <AUTHOR>
 * @since 2022/10/16
 */
@Tag(name = "系统-认证中心")
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Slf4j
public class AuthController {

    private final AuthService authService;
    private final UserService userService;

    @Operation(summary = "登录")
    @AnonymousPostMapping("/login")
    @Log(value = "登录", module = LogModuleEnum.LOGIN)
    public Result<AuthTokenResponse> login(@RequestBody @Validated LoginForm loginForm) {
        AuthTokenResponse authTokenResponse = authService.login(loginForm.getUsername(), loginForm.getPassword(), SideEnum.PC.getValue());
        return Result.success(authTokenResponse);
    }

    @Operation(summary = "PC注销")
    @GetMapping("/logout")
    public Result<?> logout() {
        authService.logout(SideEnum.PC.getValue());
        return Result.success();
    }

    @Operation(summary = "发送短信验证码(不需要认证信息)")
    @AnonymousPostMapping(value = "/sendCode")
    @RepeatSubmit
    public Result<?> sendVerificationCode(@RequestBody @Validated VerifyCodeForm form) {
        long exist = userService.count(QueryWrapper.create()
                .where(SYS_USER.USERNAME.eq(form.getUsername()))
                .where(SYS_USER.STATUS.eq(1)));
        // 登录/忘记密码 手机号校验-需要user表存在手机号
        if (form.getType().equals(1) || form.getType().equals(2)) {
            Assert.isTrue(exist != 0, ResultCode.USER_NOT_EXIST.getMsg());
        }
        boolean result = userService.sendVerificationCode(form.getUsername(), form.getType());
        return Result.judge(result);
    }


    @Operation(summary = "验证码登录-只需要用户名和验证码")
    @AnonymousPostMapping("/smsLogin")
    public Result<AuthTokenResponse> smsLogin(@RequestBody LoginForm loginForm) {
        Assert.notNull(loginForm.getCode(), "验证码不能为空");
        loginForm.setSideCode(SideEnum.PC.getValue());
        AuthTokenResponse loginResult = authService.smsLogin(loginForm);
        return Result.success(loginResult);
    }

    @Operation(summary = "申请注册使用")
    @AnonymousPostMapping("/register")
    public Result<?> register(@RequestBody @Validated RegisterForm registerForm) {
        boolean result = authService.register(registerForm);
        return Result.judge(result);
    }

    @Operation(summary = "忘记密码-重置密码")
    @AnonymousPostMapping("/reSetPassword")
    public Result<?> reSetPassword(@RequestBody @Validated PasswordResetForm resetForm) {
        resetForm.setSideCode(SideEnum.PC.getValue());
        boolean result = authService.reSetPassword(resetForm);
        return Result.judge(result);
    }
}
