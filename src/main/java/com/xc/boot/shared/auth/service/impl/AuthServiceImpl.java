package com.xc.boot.shared.auth.service.impl;

import cn.hutool.captcha.AbstractCaptcha;
import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.generator.CodeGenerator;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.constant.RedisConstants;
import com.xc.boot.common.constant.SecurityConstants;
import com.xc.boot.common.enums.SideEnum;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.result.ResultCode;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.config.property.CaptchaProperties;
import com.xc.boot.core.security.extension.SmsAuthenticationToken;
import com.xc.boot.core.security.extension.WechatAuthenticationToken;
import com.xc.boot.core.security.model.SysUserDetails;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.shared.auth.enums.CaptchaTypeEnum;
import com.xc.boot.shared.auth.model.*;
import com.xc.boot.shared.auth.service.AuthService;
import com.xc.boot.shared.auth.service.TokenService;
import com.xc.boot.system.model.entity.CompanyEntity;
import com.xc.boot.system.model.entity.SysUserEntity;
import com.xc.boot.system.service.CompanyService;
import com.xc.boot.system.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.awt.*;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.xc.boot.system.model.entity.table.CompanyTableDef.COMPANY;
import static com.xc.boot.system.model.entity.table.SysUserTableDef.SYS_USER;


/**
 * 认证服务实现类
 *
 * <AUTHOR>
 * @since 2.4.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuthServiceImpl implements AuthService {
    private final PasswordEncoder passwordEncoder;
    private final AuthenticationManager authenticationManager;
    private final RedisTemplate<String, Object> redisTemplate;
    private final CodeGenerator codeGenerator;
    private final Font captchaFont;
    private final CaptchaProperties captchaProperties;
    private final TokenService tokenService;
    private final UserService userService;
    private final CompanyService companyService;
    private final RedissonClient redissonClient;

    /**
     * 用户名密码登录
     *
     * @param username 用户名
     * @param password 密码
     * @return 访问令牌
     */
    @Override
    public AuthTokenResponse login(String username, String password, String sideCode) {
        String blockKey = RedisConstants.MOBILE_VERIFICATION_BLOCK_PREFIX + username;
        // 判断是否被禁止登录
        if (redisTemplate.hasKey(blockKey)) {
            throw new BusinessException(ResultCode.USERNAME_CODE_BLOCK_ERROR);
        }
        // 1. 创建用于密码认证的令牌（未认证）
        UsernamePasswordAuthenticationToken authenticationToken =
                new UsernamePasswordAuthenticationToken(username.trim(), password);
        // 2. 执行认证（认证中）
        try {
            Authentication authentication = authenticationManager.authenticate(authenticationToken);
            SysUserDetails principal = (SysUserDetails) authentication.getPrincipal();
            principal.setSideCode(sideCode);
            // 3. 认证成功后生成 JWT 令牌，并存入 Security 上下文，供登录日志 AOP 使用（已认证）
            AuthTokenResponse authTokenResponse = tokenService.generateToken(authentication);
            SecurityContextHolder.getContext().setAuthentication(authentication);
            OpLogUtils.appendLoginLog("系统功能-账号密码登录", "账号密码登录", sideCode, username);
            return authTokenResponse;
        }catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                String redisKey = RedisConstants.MOBILE_VERIFICATION_BLOCK_COUNT_PREFIX;
                if (sideCode.equals(SideEnum.PDA.getValue())) {
                    redisKey = RedisConstants.PDA_MOBILE_VERIFICATION_BLOCK_COUNT_PREFIX;
                }
                userService.loginPassErrorHandle(username, redisKey);
            }
            throw e;
        }
    }

    /**
     * 微信一键授权登录
     *
     * @param code 微信登录code
     * @return 访问令牌
     */
    @Override
    public AuthTokenResponse wechatLogin(String code) {
        // 1. 创建用户微信认证的令牌（未认证）
        WechatAuthenticationToken authenticationToken = new WechatAuthenticationToken(code);

        // 2. 执行认证（认证中）
        Authentication authentication = authenticationManager.authenticate(authenticationToken);
        // 3. 认证成功后生成 JWT 令牌，并存入 Security 上下文，供登录日志 AOP 使用（已认证）
        AuthTokenResponse authTokenResponse = tokenService.generateToken(authentication);
        SecurityContextHolder.getContext().setAuthentication(authentication);

        return authTokenResponse;
    }

    /**
     * 注销
     */
    @Override
    public void logout(String sideCode) {
        SysUserDetails sysUserDetails = SecurityUtils.getUser().orElse(null);
        if (Objects.isNull(sysUserDetails)) {
            return;
        }
        tokenService.logout(sysUserDetails.getUserId(), sysUserDetails.getJwtId(), sideCode);
        OpLogUtils.appendOpLog("系统功能-注销", "注销", String.format(
                """
                注销登录:{
                    登录终端: %s,
                    登录账号: %s
                }
                """, sideCode, sysUserDetails.getUserId()));
    }

    /**
     * 获取验证码
     *
     * @return 验证码
     */
    @Override
    public CaptchaResponse getCaptcha() {

        String captchaType = captchaProperties.getType();
        int width = captchaProperties.getWidth();
        int height = captchaProperties.getHeight();
        int interfereCount = captchaProperties.getInterfereCount();
        int codeLength = captchaProperties.getCode().getLength();

        AbstractCaptcha captcha;
        if (CaptchaTypeEnum.CIRCLE.name().equalsIgnoreCase(captchaType)) {
            captcha = CaptchaUtil.createCircleCaptcha(width, height, codeLength, interfereCount);
        } else if (CaptchaTypeEnum.GIF.name().equalsIgnoreCase(captchaType)) {
            captcha = CaptchaUtil.createGifCaptcha(width, height, codeLength);
        } else if (CaptchaTypeEnum.LINE.name().equalsIgnoreCase(captchaType)) {
            captcha = CaptchaUtil.createLineCaptcha(width, height, codeLength, interfereCount);
        } else if (CaptchaTypeEnum.SHEAR.name().equalsIgnoreCase(captchaType)) {
            captcha = CaptchaUtil.createShearCaptcha(width, height, codeLength, interfereCount);
        } else {
            throw new IllegalArgumentException("Invalid captcha type: " + captchaType);
        }
        captcha.setGenerator(codeGenerator);
        captcha.setTextAlpha(captchaProperties.getTextAlpha());
        captcha.setFont(captchaFont);

        String captchaCode = captcha.getCode();
        String imageBase64Data = captcha.getImageBase64Data();

        // 验证码文本缓存至Redis，用于登录校验
        String captchaKey = IdUtil.fastSimpleUUID();
        redisTemplate.opsForValue().set(SecurityConstants.CAPTCHA_CODE_PREFIX + captchaKey, captchaCode,
                captchaProperties.getExpireSeconds(), TimeUnit.SECONDS);

        return CaptchaResponse.builder()
                .captchaKey(captchaKey)
                .captchaBase64(imageBase64Data)
                .build();
    }

    @Override
    @Transactional
    public boolean register(RegisterForm registerForm) {
        // 商家名称校验
        long nameCount = companyService.count(QueryWrapper.create()
                .where(COMPANY.NAME.eq(registerForm.getCompanyName())));
        Assert.isTrue(nameCount == 0, "商家名称已被使用");

        String redisKey = SecurityConstants.USERNAME_VERIFY_KEY + registerForm.getUsername();
        RLock lock = redissonClient.getLock(redisKey);
        try {
            lock.lock(3, TimeUnit.SECONDS);
            // 再次查询是否被使用
            long count = userService.count(QueryWrapper.create()
                    .where(SYS_USER.USERNAME.eq(registerForm.getUsername())));
            count += companyService.count(QueryWrapper.create()
                    .where(COMPANY.PHONE.eq(registerForm.getUsername())));
            Assert.isTrue(count == 0, ResultCode.USERNAME_USED.getMsg());
            // 新增一条商家
            CompanyEntity companyEntity = new CompanyEntity()
                    .setStatus(0)
                    .setPhone(registerForm.getUsername())
                    .setName(registerForm.getCompanyName())
                    .setContact(registerForm.getNickname())
                    .setAddress(registerForm.getCompanyAddress());
            companyService.save(companyEntity);
        }finally {
            lock.unlock();
        }
        return true;
    }

    @Override
    public boolean reSetPassword(PasswordResetForm resetForm) {
        Assert.isTrue(resetForm.getNewPassword().equals(resetForm.getConfirmPassword()), "两次密码输入不一致");
        // 校验验证码
        userService.verifyCode(resetForm.getUsername(), resetForm.getCode(), 1, resetForm.getSideCode());
        // 修改密码
        SysUserEntity userEntity = userService.getOne(QueryWrapper.create()
                .where(SYS_USER.USERNAME.eq(resetForm.getUsername())));
        Assert.notNull(userEntity, ResultCode.USER_NOT_EXIST.getMsg());
        boolean equals = passwordEncoder.matches(resetForm.getNewPassword(), userEntity.getPassword());
        Assert.isTrue(!equals, "新密码不能与原密码相同");
        userEntity.setPassword(passwordEncoder.encode(resetForm.getNewPassword()));
        userService.updateById(userEntity);
        // 移除登录状态
        tokenService.blacklistToken(userEntity.getId());
        OpLogUtils.appendLoginLog("系统功能-忘记密码", "忘记密码", resetForm.getSideCode(), resetForm.getUsername());
        return true;
    }

    @Override
    public AuthTokenResponse smsLogin(LoginForm loginForm) {
        // 1. 创建用户微信认证的令牌（未认证）
        SmsAuthenticationToken authenticationToken = new SmsAuthenticationToken(loginForm);

        // 2. 执行认证（认证中）
        Authentication authentication = authenticationManager.authenticate(authenticationToken);
        SysUserDetails principal = (SysUserDetails) authentication.getPrincipal();
        principal.setSideCode(loginForm.getSideCode());
        // 3. 认证成功后生成 JWT 令牌，并存入 Security 上下文，供登录日志 AOP 使用（已认证）
        AuthTokenResponse authTokenResponse = tokenService.generateToken(authentication);
        SecurityContextHolder.getContext().setAuthentication(authentication);
        OpLogUtils.appendLoginLog("系统功能-验证码登录", "验证码登录", loginForm.getSideCode(), loginForm.getUsername());
        return authTokenResponse;
    }
}
