package com.xc.boot.shared.auth.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTPayload;
import cn.hutool.jwt.JWTUtil;
import com.xc.boot.common.constant.JwtClaimConstants;
import com.xc.boot.common.constant.SecurityConstants;
import com.xc.boot.common.enums.SideEnum;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.result.ResultCode;
import com.xc.boot.common.util.ContextUtils;
import com.xc.boot.common.util.ResponseUtils;
import com.xc.boot.config.property.SecurityProperties;
import com.xc.boot.core.security.model.SysUserDetails;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.shared.auth.model.AuthTokenResponse;
import com.xc.boot.shared.auth.service.TokenService;
import com.xc.boot.system.model.entity.CompanyEntity;
import com.xc.boot.system.service.CompanyService;
import com.xc.boot.system.service.UserMerchantService;
import com.xc.boot.system.service.UserRoleService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.xc.boot.common.result.ResultCode.TOKEN_INVALID;

/**
 * JWT 令牌服务实现
 *
 * <AUTHOR>
 * @since 2024/11/15
 */
@Service
public class RedisTokenService implements TokenService {
    private final byte[] secretKey;
    private final RedisTemplate<String, Object> redisTemplate;
    private final SecurityProperties securityProperties;
    private final UserRoleService userRoleService;
    private final UserMerchantService userMerchantService;
    private final CompanyService companyService;

    public RedisTokenService(SecurityProperties securityProperties, RedisTemplate<String, Object> redisTemplate, UserRoleService userRoleService, UserMerchantService userMerchantService, CompanyService companyService) {
        this.secretKey = securityProperties.getJwt().getKey().getBytes();
        this.redisTemplate = redisTemplate;
        this.securityProperties = securityProperties;
        this.userRoleService = userRoleService;
        this.userMerchantService = userMerchantService;
        this.companyService = companyService;
    }


    /**
     * 生成令牌
     *
     * @param authentication 用户认证信息
     * @return
     */
    @Override
    public AuthTokenResponse generateToken(Authentication authentication) {
        String jwtId = IdUtil.simpleUUID();
        SysUserDetails userDetails = (SysUserDetails) authentication.getPrincipal();
        String tokenStr = generateToken(authentication, jwtId);
        Set<String> jwtIdSet = new HashSet<>();
        jwtIdSet.add(jwtId);
        userDetails.setJwtIdSet(jwtIdSet);
        // 如果已经有当前用户的登录缓存，则更新jwtKey
        setJwtIds(userDetails);
        redisTemplate.opsForValue().set(SecurityConstants.REDIS_TOKEN_PREFIX + userDetails.getSideCode() + ":" + userDetails.getUserId(), JSONUtil.toJsonStr(userDetails), securityProperties.getJwt().getAccessTokenTimeToLive(), TimeUnit.SECONDS);
        return AuthTokenResponse.builder()
                .accessToken(tokenStr)
                .tokenType("Bearer")
                .build();
    }

    /**
     * 解析令牌
     *
     * @param token JWT Token
     * @return
     */
    @Override
    public Authentication parseToken(String token) {
        JWT jwt = JWTUtil.parseToken(token);
        JSONObject payloads = jwt.getPayloads();
        String jwtId = payloads.getStr(JWTPayload.JWT_ID);
        String userId = payloads.getStr(JwtClaimConstants.USER_ID);
        String sideCode = payloads.getStr(JwtClaimConstants.SIDE_CODE);
        SysUserDetails userDetails = getTokenById(userId, sideCode);
        Assert.notNull(userDetails, TOKEN_INVALID.getMsg());

        Set<String> jwtIdSet = userDetails.getJwtIdSet();
        if (!jwtIdSet.contains(jwtId)) {
            throw new BusinessException(TOKEN_INVALID);
        }
        CompanyEntity company = companyService.getById(userDetails.getCompanyId());
        // 商家状态
        if (company == null || company.getStatus() != 1) {
            throw new BusinessException(ResultCode.COMPANY_STATUS_INVALID);
        }
        // 商家过期校验
        LocalDateTime expirationDate = company.getExpirationDate();
        if (Objects.nonNull(expirationDate)) {
            if (expirationDate.isBefore(LocalDateTime.now())) {
                throw new BusinessException(ResultCode.COMPANY_EXPIRED_INVALID);
            }
        }
        userDetails.setJwtId(jwtId);
        // 用户角色列表
        Set<String> userRoles = userRoleService.getUserRoles(userDetails.getUserId());
        userDetails.setRoles(userRoles);
        // 用户门店列表
        Set<Long> userMerchants = userMerchantService.getUserMerchants(userDetails.getUserId(), userDetails.getIsMain());
        userDetails.setMerchantIds(userMerchants);
        return new UsernamePasswordAuthenticationToken(userDetails, "", userDetails.getAuthorities());
    }

    /**
     * 验证令牌
     * 每次验证令牌之后，延长redis缓存有效期
     * @param token JWT Token
     * @return
     */
    @Override
    public boolean validateToken(String token) {
        JWT jwt = JWTUtil.parseToken(token);
        // 检查 Token 是否有效(验签 + 是否过期)
        boolean isValid = jwt.setKey(secretKey).validate(0);
        if (isValid) {
            // 检查 Token 是否已被加入黑名单(注销、修改密码等场景)
            JSONObject payloads = jwt.getPayloads();
            String jti = payloads.getStr(JWTPayload.JWT_ID);
            String userId = payloads.getStr(JwtClaimConstants.USER_ID);
            String sideCode = payloads.getStr(JwtClaimConstants.SIDE_CODE);
            // 判断是否在黑名单中，如果在，则返回false 标识Token无效
            if (Boolean.TRUE.equals(redisTemplate.hasKey(SecurityConstants.BLACKLIST_TOKEN_PREFIX + jti))) {
                return false;
            }
            // 判断是否过期
            Long expire = redisTemplate.getExpire(SecurityConstants.REDIS_TOKEN_PREFIX + sideCode + ":" + userId);
            isValid = expire > 0;
            // 刷新令牌时间
            if (isValid) {
                redisTemplate.expire(SecurityConstants.REDIS_TOKEN_PREFIX + sideCode + ":" + userId, securityProperties.getJwt().getAccessTokenTimeToLive(), TimeUnit.SECONDS);
            }
        }
        return isValid;
    }

    /**
     * 刷新令牌,替换登录信息
     * @param userId redis缓存key
     * @return null
     */
    @Override
    public void refreshToken(String userId, SysUserDetails userDetails) {
        setJwtIds(userDetails);
        redisTemplate.opsForValue().set(SecurityConstants.REDIS_TOKEN_PREFIX + userDetails.getSideCode() + ":" + userId, JSONUtil.toJsonStr(userDetails), securityProperties.getJwt().getAccessTokenTimeToLive(), TimeUnit.SECONDS);
    }

    /**
     * 禁用用户时，移除所有端的登录状态
     * @param userId
     */
    @Override
    public void blacklistToken(Long userId) {
        for (SideEnum value : SideEnum.values()) {
            redisTemplate.delete(SecurityConstants.REDIS_TOKEN_PREFIX + value.getValue() + ":" + userId);
        }
    }

    @Override
    public void logout(Long userId, String jwtId, String sideCode) {
        // 生产环境直接删除缓存
        if (ContextUtils.isProd()) {
            redisTemplate.delete(SecurityConstants.REDIS_TOKEN_PREFIX + sideCode + ":" + userId);
            return;
        }
        SysUserDetails cacheDetail = getTokenById(userId.toString(), sideCode);
        if (Objects.nonNull(cacheDetail)) {
            cacheDetail.getJwtIdSet().remove(jwtId);
            redisTemplate.opsForValue().set(SecurityConstants.REDIS_TOKEN_PREFIX + sideCode + ":" + userId, JSONUtil.toJsonStr(cacheDetail), securityProperties.getJwt().getAccessTokenTimeToLive(), TimeUnit.SECONDS);
        }
    }

    @Override
    public SysUserDetails getTokenById(String userId, String sideCode) {
        String jsonStr = (String) redisTemplate.opsForValue().get(SecurityConstants.REDIS_TOKEN_PREFIX + sideCode + ":" + userId);
        if (StringUtils.isNotBlank(jsonStr)) {
            return JSONUtil.parseObj(jsonStr).toBean(SysUserDetails.class);
        }
        return null;
    }

    /**
     * 生成 JWT Token
     * @param authentication 认证信息
     * @param uuid           uuid
     * @return
     */
    private String generateToken(Authentication authentication, String uuid) {
        SysUserDetails userDetails = (SysUserDetails) authentication.getPrincipal();
        Map<String, Object> payload = new HashMap<>();
        payload.put(JwtClaimConstants.USER_ID, userDetails.getUserId()); // 用户ID
        payload.put(JwtClaimConstants.COMPANY_ID, userDetails.getCompanyId()); // 公司ID
        payload.put(JwtClaimConstants.SIDE_CODE, userDetails.getSideCode()); // 登录平台
        Date now = new Date();
        payload.put(JWTPayload.ISSUED_AT, now);
        payload.put(JWTPayload.SUBJECT, authentication.getName());
        payload.put(JWTPayload.JWT_ID, uuid);
        return JWTUtil.createToken(payload, secretKey);
    }

    private void setJwtIds(SysUserDetails userDetails) {
        // 生产环境 一个端只允许一个设备登录
        if (ContextUtils.isProd()) {
            return;
        }
        SysUserDetails cacheDetail = getTokenById(userDetails.getUserId().toString(), userDetails.getSideCode());
        if (Objects.nonNull(cacheDetail)) {
            Set<String> jwtIds = Optional.ofNullable(cacheDetail.getJwtIdSet()).orElse(new HashSet<>());
            userDetails.getJwtIdSet().addAll(jwtIds);
        }
    }
}
