package com.xc.boot.shared.common.model.bo;

import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.LambdaGetter;
import com.xc.boot.shared.common.model.query.OptionQuery;
import lombok.Builder;
import lombok.Data;

import java.util.function.BiFunction;

/**
 * 选项查询业务对象
 */
@Data
@Builder
public class OptionQueryBO<T> {
    /**
     * 查询参数
     */
    private OptionQuery query;

    /**
     * 数据访问对象
     */
    private BaseMapper<T> mapper;

    /**
     * 实体类
     */
    private Class<T> entityClass;

    /**
     * ID获取器
     */
    private LambdaGetter<T> idGetter;

    /**
     * 名称获取器
     */
    private LambdaGetter<T> nameGetter;

    /**
     * 标签获取器
     */
    private LambdaGetter<T> tagGetter;

    /**
     * 父ID获取器
     */
    private LambdaGetter<T> parentIdGetter;

    /**
     * 额外的查询条件
     */
    private BiFunction<QueryWrapper, OptionQuery, QueryWrapper> additionalConditions;
} 