package com.xc.boot.shared.common.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema
public class OptionQuery {

    @Schema(description = "查询关键字")
    private String keyword;

    @Schema(description = "选中的值(多个值用逗号分隔)")
    private String echo;

    @Schema(description = "是否显示禁用的数据(1-显示;0-不显示)")
    private Integer showDisabled;

    @Schema(description = "是否显示已删除的数据(1-显示;0-不显示)")
    private Integer showDeleted;

    @Schema(description = "分类ID")
    private String categoryIds;

    @Schema(description = "门店ID")
    private String merchantIds;
}
