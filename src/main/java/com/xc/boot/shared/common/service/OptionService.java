package com.xc.boot.shared.common.service;

import com.mybatisflex.core.service.IService;
import com.xc.boot.common.model.Option;
import com.xc.boot.shared.common.model.query.OptionQuery;
import com.xc.boot.system.model.entity.SysUserEntity;

import java.util.List;

public interface OptionService extends IService<SysUserEntity> {

    /**
     * 用户选项
     *
     * @param query 查询参数
     * @return 选项列表
     */
    List<Option<Long>> users(OptionQuery query);

    /**
     * 商户选项
     *
     * @param query 查询参数
     * @return 选项列表
     */
    List<Option<Long>> company(OptionQuery query);

    /**
     * 门店选项
     *
     * @param query 查询参数
     * @return 选项列表
     */
    List<Option<Long>> merchant(OptionQuery query);

    /**
     * 角色选项
     *
     * @param query 查询参数
     * @return 选项列表
     */
    List<Option<Long>> role(OptionQuery query);

    /**
     * 小类选项
     *
     * @param query 查询参数
     * @return 选项列表
     */
    List<Option<Long>> subclass(OptionQuery query);

    /**
     * 柜台选项
     *
     * @param query 查询参数
     * @return 选项列表
     */
    List<Option<Long>> counter(OptionQuery query);

    /**
     * 销售柜台选项
     *
     * @param query 查询参数
     * @return 选项列表
     */
    List<Option<Long>> salesCounter(OptionQuery query);

    /**
     * 成色选项
     *
     * @param query 查询参数
     * @return 选项列表
     */
    List<Option<Long>> quality(OptionQuery query);

    /**
     * 款式选项
     *
     * @param query 查询参数
     * @return 选项列表
     */
    List<Option<Long>> style(OptionQuery query);

    /**
     * 品牌选项
     *
     * @param query 查询参数
     * @return 选项列表
     */
    List<Option<Long>> brand(OptionQuery query);

    /**
     * 珠石选项
     *
     * @param query 查询参数
     * @return 选项列表
     */
    List<Option<Long>> jewelry(OptionQuery query);

    /**
     * 工艺选项
     *
     * @param query 查询参数
     * @return 选项列表
     */
    List<Option<Long>> technology(OptionQuery query);

    /**
     * 供应商选项
     *
     * @param query 查询参数
     * @return 选项列表
     */
    List<Option<Long>> supplier(OptionQuery query);

    /**
     * 入库模板选项
     *
     * @param query 查询参数
     * @return 选项列表
     */
    List<Option<Long>> goodsIncomeTemplate(OptionQuery query);

    /**
     * 下载列表
     * @param query
     * @return
     */
    List<Option<String>> sign(OptionQuery query);
}
