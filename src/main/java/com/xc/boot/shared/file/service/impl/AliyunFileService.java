package com.xc.boot.shared.file.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.xc.boot.shared.file.service.FileService;
import com.xc.boot.system.mapper.FileMapper;
import com.xc.boot.system.model.entity.FileEntity;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.shared.file.model.FileInfo;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * Aliyun 对象存储服务类
 *
 * <AUTHOR>
 * @since 2.3.0
 */
@Component
@ConditionalOnProperty(value = "oss.type", havingValue = "aliyun")
@ConfigurationProperties(prefix = "oss.aliyun")
@RequiredArgsConstructor
@Data
public class AliyunFileService implements FileService {
    /**
     * 服务Endpoint
     */
    private String endpoint;
    /**
     * 访问凭据
     */
    private String accessKeyId;
    /**
     * 凭据密钥
     */
    private String accessKeySecret;
    /**
     * 存储桶名称
     */
    private String bucketName;
    /**
     * 外部访问地址
     */
    private String externalUrl;

    private OSS aliyunOssClient;

    private final FileMapper fileMapper;

    /**
     * 支持的文件后缀
     */
    private Set<String> suffixes = Set.of(
        "jpg", "jpeg", "png", "gif", "bmp", "webp", "xlsx", "xls", "apk", "zip", "ipa"
        // ...
    );

    @PostConstruct
    public void init() {
        aliyunOssClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }

    @Override
    @SneakyThrows
    public FileInfo uploadFile(MultipartFile file, String description) {
        // 生成文件名(日期文件夹)
        String suffix = FileUtil.getSuffix(file.getOriginalFilename());
        // 获取文件名
        String originalFilename = FileUtil.getName(file.getOriginalFilename());

        if (!suffixes.contains(suffix)) {
            throw new BusinessException("文件后缀不支持");
        }

        String uuid = IdUtil.simpleUUID();
        String fileName = "gold_upload/" + DateUtil.format(LocalDateTime.now(), "yyyyMMdd") + "/" + uuid + "." + suffix;

        // try-with-resource 语法糖自动释放流
        try (InputStream inputStream = file.getInputStream()) {
            // 设置上传文件的元信息，例如Content-Type
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(file.getContentType());
            // 创建PutObjectRequest对象，指定Bucket名称、对象名称和输入流
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileName, inputStream, metadata);
            // 上传文件
            aliyunOssClient.putObject(putObjectRequest);
        } catch (Exception e) {
            throw new RuntimeException("文件上传失败");
        }
        // 获取文件访问路径
        // String fileUrl = "https://" + bucketName + "." + endpoint + "/" + fileName;
        String fileUrl = externalUrl + "/" + fileName;

        // 记录文件信息
        FileEntity fileEntity = new FileEntity();
        fileEntity.setCompanyId(SecurityUtils.getCompanyId());
        fileEntity.setName(originalFilename);
        fileEntity.setExtension(suffix);
        fileEntity.setUrl(fileUrl);
        fileEntity.setSize(file.getSize());
        fileEntity.setExtension(suffix);
        fileEntity.setDescription(StrUtil.isNotBlank(description) ? description : "");
        fileEntity.setStatus(0);
        fileEntity.setCreatedBy(SecurityUtils.getUserId());
        fileMapper.insert(fileEntity);

        FileInfo fileInfo = new FileInfo();
        fileInfo.setId(fileEntity.getId());
        fileInfo.setName(fileName);
        fileInfo.setUrl(fileUrl);

        return fileInfo;
    }


    public FileEntity uploadFileByStream(InputStream inputStream, String originName, String contentType, Long size, String description) {
        // 生成文件名(日期文件夹)
        String suffix = FileUtil.getSuffix(originName);
        String uuid = IdUtil.simpleUUID();
        String fileName = "gold_upload/" + DateUtil.format(LocalDateTime.now(), "yyyyMMdd") + "/" + uuid + "." + suffix;
        try {
            // 设置上传文件的元信息，例如Content-Type
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(contentType);
            // 创建PutObjectRequest对象，指定Bucket名称、对象名称和输入流
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileName, inputStream, metadata);
            // 上传文件
            aliyunOssClient.putObject(putObjectRequest);
        } catch (Exception e) {
            throw new RuntimeException("文件上传失败");
        }
        // 获取文件访问路径
        String fileUrl = externalUrl + "/" + fileName;

        // 记录文件信息
        FileEntity fileEntity = new FileEntity();
        fileEntity.setCompanyId(SecurityUtils.getCompanyId());
        fileEntity.setName(originName);
        fileEntity.setExtension(suffix);
        fileEntity.setUrl(fileUrl);
        fileEntity.setSize(size);
        fileEntity.setDescription(StrUtil.isNotBlank(description) ? description : "");
        fileEntity.setStatus(0);
        fileEntity.setCreatedBy(SecurityUtils.getUserId());
        fileMapper.insert(fileEntity);
        return fileEntity;
    }

    @Override
    public boolean deleteFile(String filePath) {
        Assert.notBlank(filePath, "删除文件路径不能为空");
        String fileHost = "https://" + bucketName + "." + endpoint; // 文件主机域名
        String fileName = filePath.substring(fileHost.length() + 1); // +1 是/占一个字符，截断左闭右开
        aliyunOssClient.deleteObject(bucketName, fileName);
        return true;
    }
}
