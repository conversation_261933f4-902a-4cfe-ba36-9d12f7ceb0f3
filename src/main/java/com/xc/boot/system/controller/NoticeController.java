package com.xc.boot.system.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.system.model.dto.NoticeIdDTO;
import com.xc.boot.system.model.dto.NoticeIdsDTO;
import com.xc.boot.system.model.entity.SysNoticeEntity;
import com.xc.boot.system.model.form.NoticeForm;
import com.xc.boot.system.model.query.NoticePageQuery;
import com.xc.boot.system.model.vo.NoticeDetailVO;
import com.xc.boot.system.model.vo.NoticePageVO;
import com.xc.boot.system.model.vo.UserNoticePageVO;
import com.xc.boot.system.service.NoticeService;
import com.xc.boot.system.service.UserNoticeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 通知公告前端控制层
 *
 * <AUTHOR>
 * @since 2024-08-27 10:31
 */
@Tag(name = "超管-通知公告接口")
@RestController
@RequestMapping("/api/notices")
@RequiredArgsConstructor
public class NoticeController {

    private final NoticeService noticeService;

    private final UserNoticeService userNoticeService;

    @Operation(summary = "通知公告分页列表")
    @PostMapping("/page")
    public PageResult<NoticePageVO> getNoticePage(@RequestBody NoticePageQuery queryParams) {
        Page<NoticePageVO> result = noticeService.getNoticePage(queryParams);
        return PageResult.success(result);
    }

    @Operation(summary = "新增通知公告")
    @PostMapping
    public Result<?> saveNotice(@RequestBody @Valid NoticeForm formData) {
        boolean result = noticeService.saveNotice(formData);
        return Result.judge(result);
    }

    @Operation(summary = "获取通知公告表单数据")
    @GetMapping("/form")
    public Result<SysNoticeEntity> getNoticeForm(@Valid NoticeIdDTO dto) {
        SysNoticeEntity entity = noticeService.getNoticeFormData(dto.getId());
        return Result.success(entity);
    }

    @Operation(summary = "阅读获取通知公告详情")
    @GetMapping("/detail")
    public Result<NoticeDetailVO> getNoticeDetail(@Valid NoticeIdDTO dto) {
        NoticeDetailVO detail = noticeService.getNoticeDetail(dto.getId());
        return Result.success(detail);
    }

    @Operation(summary = "修改通知公告")
    @PutMapping
    public Result<Void> updateNotice(@RequestBody @Validated NoticeForm formData) {
        boolean result = noticeService.updateNotice(formData.getId(), formData);
        return Result.judge(result);
    }

    @Operation(summary = "发布通知公告")
    @PutMapping("/publish")
    public Result<Void> publishNotice(@RequestBody @Valid NoticeIdDTO dto) {
        boolean result = noticeService.publishNotice(dto.getId());
        return Result.judge(result);
    }

    @Operation(summary = "删除通知公告")
    @DeleteMapping
    public Result<Void> deleteNotices(@RequestBody @Valid NoticeIdsDTO dto) {
        boolean result = noticeService.deleteNotices(dto.getIds());
        return Result.judge(result);
    }

    @Operation(summary = "全部已读")
    @PutMapping("/read-all")
    public Result<Void> readAll() {
        boolean result = userNoticeService.readAll();
        return Result.judge(result);
    }

    @Operation(summary = "获取我的通知公告分页列表")
    @GetMapping("/my-page")
    public PageResult<UserNoticePageVO> getMyNoticePage(NoticePageQuery queryParams) {
        Page<UserNoticePageVO> result = userNoticeService.getMyNoticePage(queryParams);
        return PageResult.success(result);
    }
}
