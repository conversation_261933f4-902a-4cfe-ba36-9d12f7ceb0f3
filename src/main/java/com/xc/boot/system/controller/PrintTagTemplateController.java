package com.xc.boot.system.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.result.Result;
import com.xc.boot.system.model.dto.PrintTagTemplateConfigDTO;
import com.xc.boot.system.model.dto.PrintTagTemplateFormDTO;
import com.xc.boot.system.model.query.PrintTagTemplatePageQuery;
import com.xc.boot.system.model.vo.PrintTagListVO;
import com.xc.boot.system.model.vo.PrintTagTemplatePageVO;
import com.xc.boot.system.service.PrintTagTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 打印标签模板控制器
 */
@Tag(name = "系统配置-打印标签模板管理")
@RestController
@RequestMapping("/api/print-template")
@RequiredArgsConstructor
public class PrintTagTemplateController {

    private final PrintTagTemplateService printTagTemplateService;

    @Operation(summary = "获取打印标签模板分页列表")
    @GetMapping("/page")
    public Result<Page<PrintTagTemplatePageVO>> getPrintTagTemplatePage(PrintTagTemplatePageQuery queryParams) {
        Page<PrintTagTemplatePageVO> page = printTagTemplateService.getPrintTagTemplatePage(queryParams);
        return Result.success(page);
    }

    @Operation(summary = "保存打印标签模板")
    @PostMapping
    public Result<Boolean> savePrintTagTemplate(@RequestBody @Valid PrintTagTemplateFormDTO form) {
        boolean result = printTagTemplateService.savePrintTagTemplate(form);
        return Result.success(result);
    }

    @Operation(summary = "删除打印标签模板")
    @DeleteMapping
    public Result<Boolean> deletePrintTagTemplate(@RequestBody @Valid DeleteRequest request) {
        boolean result = printTagTemplateService.deletePrintTagTemplate(request.getId());
        return Result.success(result);
    }

    @Operation(summary = "配置打印标签模板")
    @PutMapping("/config")
    public Result<Boolean> configPrintTagTemplate(@RequestBody @Valid PrintTagTemplateConfigDTO config) {
        boolean result = printTagTemplateService.configPrintTagTemplate(config);
        return Result.success(result);
    }

    @Operation(summary = "获取打印标签列表")
    @GetMapping("/tags")
    public Result<List<PrintTagListVO>> getPrintTagList() {
        List<PrintTagListVO> list = printTagTemplateService.getPrintTagList();
        return Result.success(list);
    }
} 