package com.xc.boot.system.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Update;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.system.model.form.PdaPackageForm;
import com.xc.boot.system.model.query.PdaPackagePageQuery;
import com.xc.boot.system.model.vo.PdaPackageVo;
import com.xc.boot.system.service.PdaPackageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * PDA包管理控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "系统管理-PDA包管理")
@RequestMapping("/api/system/pdaPackage")
public class SysPdaPackageController {
    private final PdaPackageService pdaPackageService;

    @Operation(summary = "PDA包分页列表")
    @PostMapping("/page")
    public PageResult<PdaPackageVo> page(@Validated @RequestBody PdaPackagePageQuery query) {
        Page<PdaPackageVo> page = pdaPackageService.page(query);
        return PageResult.success(page);
    }

    @Operation(summary = "创建PDA包")
    @PostMapping
    public Result<?> create(@Validated(Create.class) @RequestBody PdaPackageForm form) {
        pdaPackageService.create(form);
        return Result.success();
    }

    @Operation(summary = "更新PDA包")
    @PutMapping
    public Result<?> update(@Validated(Update.class) @RequestBody PdaPackageForm form) {
        pdaPackageService.update(form);
        return Result.success();
    }

    @Operation(summary = "删除PDA包")
    @DeleteMapping
    public Result<?> delete(@RequestBody @Validated DeleteRequest form) {
        pdaPackageService.delete(form.getId());
        return Result.success();
    }
}