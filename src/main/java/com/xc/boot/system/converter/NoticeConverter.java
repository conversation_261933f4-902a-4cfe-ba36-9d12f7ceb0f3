package com.xc.boot.system.converter;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.system.model.bo.NoticeBO;
import com.xc.boot.system.model.entity.SysNoticeEntity;
import com.xc.boot.system.model.form.NoticeForm;
import com.xc.boot.system.model.vo.NoticeDetailVO;
import com.xc.boot.system.model.vo.NoticePageVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 通知公告对象转换器
 *
 * <AUTHOR>
 * @since 2024-08-27 10:31
 */
@Mapper(componentModel = "spring")
public interface NoticeConverter {

    NoticeForm toForm(SysNoticeEntity entity);

    SysNoticeEntity toEntity(NoticeForm formData);

    NoticePageVO toPageVo(NoticeBO bo);

    Page<NoticePageVO> toPageVo(Page<NoticeBO> noticePage);

    NoticeDetailVO toDetailVO(NoticeBO noticeBO);

    NoticeBO toBO(SysNoticeEntity entity);
}
