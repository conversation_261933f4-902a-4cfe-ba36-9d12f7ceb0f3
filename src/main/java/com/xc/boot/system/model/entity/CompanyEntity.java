package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 商家实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "company")
public class CompanyEntity extends BaseEntity {
    /**
     * 商家名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 商家账号
     */
    @Column(value = "phone")
    private String phone;

    /**
     * 联系人
     */
    @Column(value = "contact")
    private String contact;

    /**
     * 商家地址
     */
    @Column(value = "address")
    private String address;

    /**
     * 是否允许多门店
     */
    @Column(value = "is_multiple")
    private Boolean isMultiple;

    /**
     * 允许门店数
     */
    @Column(value = "max_number")
    private Integer maxNumber;

    /**
     * 商家状态(0:禁用|1:启用)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 过期时间(指定日期的23:59:59过期)
     */
    @Column(value = "expiration_date")
    private LocalDateTime expirationDate;

    /**
     * 备注
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 数据库名称
     */
    @Column(value = "db_name")
    private String dbName;

    /**
     * 数据库连接信息
     */
    @Column(value = "db_config")
    private String dbConfig;
}
