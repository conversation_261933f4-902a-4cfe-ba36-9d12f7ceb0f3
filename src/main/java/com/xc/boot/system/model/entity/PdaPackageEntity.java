package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * PDA包管理实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "pda_package")
public class PdaPackageEntity extends BaseEntity {
    /**
     * 包名称
     */
    @Column(value = "name")
    private String name;

    /**
     * 设备类型
     */
    @Column(value = "type")
    private String type;

    /**
     * 版本号
     */
    @Column(value = "version")
    private String version;

    /**
     * 设备图片
     */
    @Column(value = "image")
    private String image;

    /**
     * 下载地址
     */
    @Column(value = "url")
    private String url;

    /**
     * 备注
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 删除时间
     */
    @Column(value = "deleted_at", isLogicDelete = true)
    private Date deletedAt;

    /**
     * 生效时间
     */
    @Column(value = "active_at")
    private Date activeAt;
} 