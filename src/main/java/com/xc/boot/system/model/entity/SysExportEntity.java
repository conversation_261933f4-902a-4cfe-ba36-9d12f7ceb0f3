package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 下载中心实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "sys_export")
public class SysExportEntity extends BaseEntity {
    /**
     * 公司ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 用户ID
     */
    @Column(value = "user_id")
    private Long userId;

    /**
     * 导出状态(2:进行中|1:导出成功|-1:导出失败)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 文件类型(1:excel|2:pdf|3:word)
     */
    @Column(value = "type")
    private Integer type;

    /**
     * 文件名
     */
    @Column(value = "name")
    private String name;

    /**
     * 下载地址
     */
    @Column(value = "url")
    private String url;

    /**
     * 导出的sign
     */
    @Column(value = "sign")
    private String sign;

    /**
     * 备注
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 总行数
     */
    @Column(value = "line_count")
    private Long lineCount;

    /**
     * 文件大小(字节)
     */
    @Column(value = "size")
    private Long size;

    /**
     * 下载次数
     */
    @Column(value = "download_count")
    private Integer downloadCount;

}