package com.xc.boot.system.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.xc.boot.common.base.BaseEntity;
import com.xc.boot.common.listener.CreatedByListenerFlag;
import com.xc.boot.common.listener.UpdatedByListenerFlag;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 用户实体
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "sys_user")
public class SysUserEntity extends BaseEntity {
    /**
     * 用户名/手机号
     */
    @Column(value = "username")
    private String username;

    /**
     * 昵称
     */
    @Column(value = "nickname")
    private String nickname;

    /**
     * 密码
     */
    @Column(value = "password")
    private String password;

    /**
     * 商家ID
     */
    @Column(value = "company_id")
    private Long companyId;

    /**
     * 用户头像
     */
    @Column(value = "avatar")
    private String avatar;

    /**
     * 用户头像id
     */
    @Column(value = "avatar_id")
    private Long avatarId;

    /**
     * 性别
     */
    @Column(value = "gender")
    private Integer gender;

    /**
     * 查看机密字段(1-正常 0-禁用)
     */
    @Column(value = "secret")
    private Integer secret;

    /**
     * 状态(1-正常 0-禁用)
     */
    @Column(value = "status")
    private Integer status;

    /**
     * 是否主账号(0:否|1:是)
     */
    @Column(value = "main_flag")
    private Integer mainFlag;

    /**
     * 逻辑删除
     */
    @Column(value = "deleted_at", isLogicDelete = true)
    private Date deletedAt;
}