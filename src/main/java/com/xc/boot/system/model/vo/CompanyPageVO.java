package com.xc.boot.system.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 商家分页列表VO
 */
@Data
@Schema(description = "商家分页列表VO")
public class CompanyPageVO {
    
    @Schema(description = "商家ID")
    private Integer id;
    
    @Schema(description = "商家名称")
    private String name;
    
    @Schema(description = "商家账号")
    private String phone;
    
    @Schema(description = "联系人")
    private String contact;
    
    @Schema(description = "商家地址")
    private String address;
    
    @Schema(description = "是否允许多门店")
    private Boolean isMultiple;
    
    @Schema(description = "允许门店数")
    private Integer maxNumber;
    
    @Schema(description = "已创建门店数")
    private Integer merchantCount;
    
    @Schema(description = "商家状态(0:禁用|1:启用)")
    private Integer status;
    
    @Schema(description = "过期时间")
    private Date expirationDate;
    
    @Schema(description = "过期时间(YYYY-MM-DD)")
    private String expirationDateStr;
    
    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
        if (expirationDate != null) {
            this.expirationDateStr = new SimpleDateFormat("yyyy-MM-dd").format(expirationDate);
        }
    }
    
    @Schema(description = "备注")
    private String remark;
    
    @Schema(description = "创建时间")
    private Date createdAt;
    
    @Schema(description = "更新时间")
    private Date updatedAt;
}