package com.xc.boot.system.model.vo;

import com.xc.boot.system.model.entity.MerchantEntity;
import com.xc.boot.system.model.entity.SysRoleEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 用户登录视图对象
 *
 * <AUTHOR>
 * @since 2022/1/14
 */
@Schema(description ="当前登录用户视图对象")
@Data
public class UserInfoVO {

    @Schema(description="用户ID")
    private Long userId;

    @Schema(description="用户名")
    private String username;

    @Schema(description="用户昵称")
    private String nickname;

    @Schema(description="头像地址")
    private String avatar;

    @Schema(description="是否主账号")
    private Boolean isMain;

    @Schema(description="公司ID")
    private Long companyId;

    @Schema(description="性别")
    private Integer gender;

    @Schema(description="用户角色编码集合")
    private Set<String> roles;

    @Schema(description="用户权限标识集合")
    private Set<String> permissions;

    @Schema(description="用户角色")
    private List<RolePageVO> roleVos;

    @Schema(description="用户门店")
    private List<MerchantEntity> merchants;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "是否查看机密")
    private Boolean showSecret;

}
