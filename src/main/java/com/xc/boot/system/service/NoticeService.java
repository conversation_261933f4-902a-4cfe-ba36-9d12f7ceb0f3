package com.xc.boot.system.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.system.model.entity.SysNoticeEntity;
import com.xc.boot.system.model.form.NoticeForm;
import com.xc.boot.system.model.query.NoticePageQuery;
import com.xc.boot.system.model.vo.NoticeDetailVO;
import com.xc.boot.system.model.vo.NoticePageVO;
import com.xc.boot.system.model.vo.UserNoticePageVO;

/**
 * 通知公告服务接口
 */
public interface NoticeService extends IService<SysNoticeEntity> {

    /**
     * 获取通知公告分页列表
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    Page<NoticePageVO> getNoticePage(NoticePageQuery queryParams);

    /**
     * 获取通知公告表单数据
     *
     * @param id 通知公告ID
     * @return 表单数据
     */
    SysNoticeEntity getNoticeFormData(Long id);

    /**
     * 保存通知公告
     *
     * @param formData 表单数据
     * @return 是否成功
     */
    boolean saveNotice(NoticeForm formData);

    /**
     * 修改通知公告
     *
     * @param id 通知公告ID
     * @param formData 表单数据
     * @return 是否成功
     */
    boolean updateNotice(Long id, NoticeForm formData);

    /**
     * 删除通知公告
     *
     * @param ids 通知公告ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    boolean deleteNotices(String ids);

    /**
     * 发布通知公告
     *
     * @param id 通知公告ID
     * @return 是否成功
     */
    boolean publishNotice(Long id);

    /**
     * 获取通知公告详情
     *
     * @param id 通知公告ID
     * @return 通知公告详情
     */
    NoticeDetailVO getNoticeDetail(Long id);

    /**
     * 获取我的通知公告分页列表
     *
     * @param queryParams 查询参数
     * @return 通知公告分页列表
     */
    Page<UserNoticePageVO> getMyNoticePage(NoticePageQuery queryParams);
}
