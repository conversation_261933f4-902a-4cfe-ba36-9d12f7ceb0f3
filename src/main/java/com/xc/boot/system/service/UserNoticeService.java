package com.xc.boot.system.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.system.model.entity.SysUserNoticeEntity;
import com.xc.boot.system.model.query.NoticePageQuery;
import com.xc.boot.system.model.vo.NoticePageVO;
import com.xc.boot.system.model.vo.UserNoticePageVO;

/**
 * 用户公告状态服务类
 *
 * <AUTHOR>
 * @since 2024-08-28 16:56
 */
public interface UserNoticeService extends IService<SysUserNoticeEntity> {

    /**
     * 全部标记为已读
     *
     * @return 是否成功
     */
    boolean readAll();

    /**
     * 分页获取我的通知公告
     *
     * @param queryParams 查询参数
     * @return 我的通知公告分页列表
     */
    Page<UserNoticePageVO> getMyNoticePage(NoticePageQuery queryParams);
}
