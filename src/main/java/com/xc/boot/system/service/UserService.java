package com.xc.boot.system.service;


import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.common.model.Option;
import com.xc.boot.system.enums.ContactType;
import com.xc.boot.system.model.dto.UserAuthInfo;
import com.xc.boot.system.model.dto.UserExportDTO;
import com.xc.boot.system.model.entity.SysUserEntity;
import com.xc.boot.system.model.form.*;
import com.xc.boot.system.model.query.UserPageQuery;
import com.xc.boot.system.model.vo.UserInfoVO;
import com.xc.boot.system.model.vo.UserPageVO;
import com.xc.boot.system.model.vo.UserProfileVO;

import java.util.List;

/**
 * 用户业务接口
 *
 * <AUTHOR>
 * @since 2022/1/14
 */
public interface UserService extends IService<SysUserEntity> {

    /**
     * 用户分页列表
     *
     * @return
     */
    Page<UserPageVO> getUserPage(UserPageQuery queryParams);

    /**
     * 新增用户
     * @param form 用户表单对象
     * @return
     */
    boolean saveUser(UserUpdateForm form);

    /**
     * 修改用户
     *
     * @param userForm 用户表单对象
     * @return
     */
    boolean updateMe(UserForm userForm);


    /**
     * 删除用户
     *
     * @param idsStr 用户ID，多个以英文逗号(,)分割
     * @return
     */
    boolean deleteUsers(String idsStr);


    /**
     * 根据用户名获取认证信息
     *
     * @param username 用户名
     * @return {@link UserAuthInfo}
     */

    UserAuthInfo getUserAuthInfo(String username);


    /**
     * 获取导出用户列表
     *
     * @param queryParams 查询参数
     * @return
     */
    List<UserExportDTO> listExportUsers(UserPageQuery queryParams);


    /**
     * 获取登录用户信息
     *
     * @return
     */
    UserInfoVO getCurrentUserInfo();

    /**
     * 获取个人中心用户信息
     *
     * @return
     */
    UserProfileVO getUserProfile(Long userId);

    /**
     * 修改个人中心用户信息
     *
     * @param formData 表单数据
     * @return
     */
    boolean updateUserProfile(UserProfileForm formData);

    /**
     * 修改用户密码
     *
     * @param userId 用户ID
     * @param data   修改密码表单数据
     * @return
     */
    boolean changePassword(Long userId, PasswordChangeForm data);

    /**
     * 重置用户密码
     *
     * @return
     */
    boolean resetPassword(PasswordChangeForm data);

    /**
     * 发送验证码
     *
     * @param contact 联系方式
     * @param type    联系方式类型
     * @return
     */
    boolean sendVerificationCode(String contact, Integer type);

    /**
     * 修改当前用户手机号
     * @return
     */
    boolean bindMobile(String username, String code, String sideCode);

    /**
     * 获取用户选项列表
     *
     * @return {@link List<Option<String>>} 用户选项列表
     */
    List<Option<String>> listUserOptions();

    /**
     * 根据 openid 获取用户认证信息
     *
     * @param username 用户名
     * @return {@link UserAuthInfo}
     */

    UserAuthInfo getUserAuthInfoByOpenId(String username);

    /**
     * 修改用户信息
     * @param updateForm
     * @return
     */
    boolean update(UserUpdateForm updateForm);

    /**
     * 修改手机号-验证登录密码
     * @param password
     * @return
     */
    boolean verifyPassword(String password);

    /**
     * 验证验证码
     * @param mobile
     * @param code
     * @return
     */
    boolean verifyCode(String mobile, String code, Integer isLogin, String sideCode);

    void loginPassErrorHandle(String username, String prefix);
}
