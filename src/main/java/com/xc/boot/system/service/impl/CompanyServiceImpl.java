package com.xc.boot.system.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.enums.EnvEnum;
import com.xc.boot.common.util.AuthUtils;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.modules.merchant.service.CounterService;
import com.xc.boot.modules.merchant.service.GoodsColumnService;
import com.xc.boot.system.mapper.CompanyMapper;
import com.xc.boot.system.mapper.MerchantMapper;
import com.xc.boot.system.mapper.UserMapper;
import com.xc.boot.system.model.dto.CompanyFormDTO;
import com.xc.boot.system.model.entity.CompanyEntity;
import com.xc.boot.system.model.entity.MerchantEntity;
import com.xc.boot.system.model.entity.SysUserEntity;
import com.xc.boot.system.model.query.CompanyPageQuery;
import com.xc.boot.system.model.vo.CompanyPageVO;
import com.xc.boot.system.service.CompanyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商家服务实现类
 */
@Slf4j
@Service
public class CompanyServiceImpl extends ServiceImpl<CompanyMapper, CompanyEntity> implements CompanyService {

    @Autowired
    private Environment environment;

    @Value("${mybatis-flex.datasource.root.url}")
    private String dbUrl;

    @Value("${mybatis-flex.datasource.root.username}")
    private String dbUsername;

    @Value("${mybatis-flex.datasource.root.password}")
    private String dbPassword;

    @Autowired
    private MerchantMapper merchantMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private CounterService counterService;

    @Autowired
    private GoodsColumnService goodsColumnService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public Page<CompanyPageVO> getCompanyPage(CompanyPageQuery queryParams) {
        // 构建查询条件
        QueryWrapper queryWrapper = query()
                .select(
                        QueryMethods.column(CompanyEntity::getId),
                        QueryMethods.column(CompanyEntity::getName),
                        QueryMethods.column(CompanyEntity::getPhone),
                        QueryMethods.column(CompanyEntity::getContact),
                        QueryMethods.column(CompanyEntity::getAddress),
                        QueryMethods.column(CompanyEntity::getIsMultiple),
                        QueryMethods.column(CompanyEntity::getMaxNumber),
                        QueryMethods.column(CompanyEntity::getStatus),
                        QueryMethods.column(CompanyEntity::getExpirationDate),
                        QueryMethods.column(CompanyEntity::getRemark),
                        QueryMethods.column(CompanyEntity::getCreatedAt),
                        QueryMethods.column(CompanyEntity::getUpdatedAt),
                        QueryMethods.count(MerchantEntity::getId).as("merchant_count"))
                .from(CompanyEntity.class)
                .leftJoin(MerchantEntity.class).on(q -> q.and(CompanyEntity::getId).eq(MerchantEntity::getCompanyId))
                // 商家名称模糊查询
                .where(CompanyEntity::getName)
                .like(queryParams.getName(), StringUtils.hasText(queryParams.getName()))
                // 联系电话模糊查询
                .and(CompanyEntity::getPhone)
                .like(queryParams.getPhone(), StringUtils.hasText(queryParams.getPhone()))
                // 联系人模糊查询
                .and(CompanyEntity::getContact)
                .like(queryParams.getContact(), StringUtils.hasText(queryParams.getContact()))
                // 商家地址模糊查询
                .and(CompanyEntity::getAddress)
                .like(queryParams.getAddress(), StringUtils.hasText(queryParams.getAddress()))
                // 状态精确匹配
                .and(CompanyEntity::getStatus)
                .eq(queryParams.getStatus(), queryParams.getStatus() != null)
                // 是否多商户精确匹配
                .and(CompanyEntity::getIsMultiple)
                .eq(queryParams.getIsMultiple(), queryParams.getIsMultiple() != null);

        // 创建时间范围查询
        if (queryParams.getCreateTimeRange() != null) {
            queryWrapper.between(CompanyEntity::getCreatedAt, queryParams.getCreateTimeRange()[0],
                    queryParams.getCreateTimeRange()[1]);
        }

        // 分组
        queryWrapper.groupBy(CompanyEntity::getId);

        // 添加排序条件
        queryWrapper.orderBy(CompanyEntity::getUpdatedAt, false);

        // 执行分页查询并直接映射到VO
        return mapper.paginateAs(queryParams.getPageNum(), queryParams.getPageSize(), queryWrapper,
                CompanyPageVO.class);
    }

    @Override
    public boolean saveCompany(CompanyFormDTO form) {
        Long companyId = form.getId();
        // 当前商户数量
        Long currentMerchantCount = 1L;
        if (form.getMaxNumber() != null) {
            currentMerchantCount = form.getMaxNumber().longValue();
        }
        Integer maxNumber = form.getMaxNumber();
        if (!form.getIsMultiple()) {
            maxNumber = 1;
        }

        // 编辑商家时，判断商家是否存在
        CompanyEntity oldCompany = null;
        if (companyId != null) {
            oldCompany = this.getById(companyId);
            Assert.isTrue(oldCompany != null, "商家不存在");

            // 获取当前商户数量
            currentMerchantCount = this.count(
                    QueryWrapper.create()
                            .from(MerchantEntity.class)
                            .eq(MerchantEntity::getCompanyId, companyId));
        }

        // 检查商家账号/名称是否已存在
        long count = this.count(
                QueryWrapper.create()
                        .from(CompanyEntity.class)
                        .ne(CompanyEntity::getId, companyId, companyId != null)
                        .and(
                                QueryMethods.column(CompanyEntity::getPhone)
                                        .eq(form.getPhone())
                                        .or(
                                                QueryMethods.column(CompanyEntity::getName)
                                                        .eq(form.getName()))));
        Assert.isTrue(count == 0, "商家账号已存在，请修改后重试！");

        // 检查用户名是否已存在
        long userCount = userMapper.selectCountByQuery(
                QueryWrapper.create()
                        .from(SysUserEntity.class)
                        .and(SysUserEntity::getCompanyId).ne(companyId, companyId != null)
                        .and(SysUserEntity::getUsername).eq(form.getPhone()));
        Assert.isTrue(userCount == 0, "商家账号已存在，请修改后重试！");

        // 创建商家实体
        CompanyEntity company = new CompanyEntity();
        company.setName(form.getName());
        company.setPhone(form.getPhone());
        company.setContact(form.getContact());
        company.setIsMultiple(form.getIsMultiple());
        company.setMaxNumber(maxNumber);
        company.setExpirationDate(DateUtil.parse(form.getExpirationDate(), "yyyy-MM-dd").toLocalDateTime().withHour(23)
                .withMinute(59).withSecond(59));
        company.setRemark(form.getRemark());
        company.setAddress(form.getAddress());

        // 如果是编辑，设置ID和状态
        if (oldCompany != null) {
            company.setId(oldCompany.getId());
            company.setStatus(oldCompany.getStatus());

            // 如果当前商户门店数量大于允许门店数，则不允许修改
            CommonUtils.abortIf(currentMerchantCount > 1 && !company.getIsMultiple(), "该商家已创建多个门店，无法关闭多门店");
            CommonUtils.abortIf(currentMerchantCount > maxNumber.longValue() && company.getIsMultiple(),
                    "当前商户门店数大于允许门店数，无法修改");
        } else {
            // 新增时默认禁用状态
            company.setStatus(0);
        }

        // 保存商家信息
        Boolean result = this.saveOrUpdate(company);

        Assert.isTrue(result, "保存商家信息失败");

        if (company.getStatus() == 1) {
            Assert.isTrue(this.initCompany(this.getById(companyId)), "初始化商户失败");
        }

        // 记录操作日志
        if (companyId != null) {
            OpLogUtils.appendOpLog("商家管理-编辑商家", "编辑商家: " + form.getName(),
                    Map.of("修改前", oldCompany, "修改后", form));
        } else {
            OpLogUtils.appendOpLog("商家管理-新增商家", "新增商家: " + form.getName(), form);
        }

        return result;
    }

    @Override
    public boolean updateCompanyStatus(Long id, Integer status) {
        CompanyEntity company = this.getById(id);
        Assert.isTrue(company != null, "商家不存在");

        company.setStatus(status);

        if (status == 1) {
            Assert.isTrue(this.initCompany(company), "初始化商户失败");
        }

        boolean result = this.updateById(company);

        // 记录操作日志
        if (result) {
            OpLogUtils.appendOpLog("商家管理-修改状态", "修改商家状态: " + company.getName(),
                    Map.of("商家", company, "新状态", status));
        }

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean initCompany(CompanyEntity company) {
        // * 如果数据库名称为空，则初始化数据库
        if (!StringUtils.hasText(company.getDbName())) {
            Assert.isTrue(this.initDb(company), "初始化业务库失败");
        }

        // * 初始化门店信息
        Assert.isTrue(this.initMerchant(company), "初始化门店信息失败");

        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean initMerchant(CompanyEntity company) {
        MerchantEntity merchant = merchantMapper.selectOneByQuery(
                QueryWrapper.create()
                        .from(MerchantEntity.class)
                        .where(MerchantEntity::getCompanyId).eq(company.getId()));

        // * 如果门店不存在，则创建门店
        if (merchant == null) {
            merchant = new MerchantEntity();
            merchant.setCompanyId(company.getId().intValue());
            merchant.setName(company.getName());
            merchant.setAddress(company.getAddress());
            merchant.setPhone(company.getPhone());
            merchant.setContact(company.getContact());
            merchant.setContactPhone(company.getPhone());
            merchant.setStatus(1);
            merchant.setStatus(1);
            merchant.setRemark("");
            merchantMapper.insert(merchant);
        }

        // * 创建默认用户
        SysUserEntity user = userMapper.selectOneByQuery(
                QueryWrapper.create()
                        .from(SysUserEntity.class)
                        .where(SysUserEntity::getCompanyId).eq(company.getId()));
        if (user == null) {
            user = new SysUserEntity();
            user.setCompanyId(company.getId());
            user.setAvatarId(0L);
            user.setGender(0);
            user.setUsername(company.getPhone());
            user.setNickname(company.getContact());
            // 默认密码规则: 手机号后四位 + 日期(月日)
            user.setPassword(
                    passwordEncoder.encode(
                            company.getPhone().substring(company.getPhone().length() - 4)
                                    + LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMdd"))));
            // 可查看机密字段
            user.setSecret(1);
            user.setStatus(1);
            user.setMainFlag(1);
            userMapper.insert(user);
        }

        // * 初始化商户端数据
        Integer merchantId = merchant.getId().intValue();
        Integer companyId = company.getId().intValue();
        CommonUtils.asyncExecute(() -> {
            AuthUtils.mockUserByCompanyId(companyId);

            // * 创建默认柜台
            counterService.createDefaultCounters(merchantId);

            // * 初始化默认字段
            goodsColumnService.initDefaultColumns();
        });

        return true;
    }

    /**
     * 初始化业务库
     * 1. 创建新的数据库
     * 2. 创建数据库用户并授权
     * 3. 复制表结构到新数据库
     * 4. 更新公司实体中的数据库信息
     *
     * @param company 公司实体
     * @return 是否初始化成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean initDb(CompanyEntity company) {
        try {
            // 公共账号名称
            String commUsername = "fb_gold_comm";

            // 获取数据库连接信息
            String host = extractHostFromUrl(dbUrl);
            String port = extractPortFromUrl(dbUrl);
            String baseDbName = extractDbNameFromUrl(dbUrl);

            // 生成新的数据库名称
            String newDbName = baseDbName + "_" + company.getId();

            // 使用 root 账号连接
            JdbcTemplate rootJdbcTemplate = new JdbcTemplate();
            rootJdbcTemplate.setDataSource(new DriverManagerDataSource(dbUrl, dbUsername, dbPassword));

            // 创建新数据库
            String createDbSql = "CREATE DATABASE IF NOT EXISTS " + newDbName + " CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci";
            log.info("Executing SQL: {}", createDbSql);
            rootJdbcTemplate.execute(createDbSql);

            // 生成随机用户名和密码
            String username = "fb_gold_" + RandomUtil.randomString(8) + "_" + company.getId();
            String password = RandomUtil.randomString(16);

            // 创建新用户并授权
            rootJdbcTemplate.execute("CREATE USER '" + username + "'@'%' IDENTIFIED BY '" + password + "'");
            rootJdbcTemplate.execute("GRANT ALL PRIVILEGES ON " + newDbName + ".* TO '" + username + "'@'%'");
            log.info("Created user: {} for database: {}", username, newDbName);

            // 连接新数据库
            String newDbUrl = "jdbc:mysql://" + host + ":" + port + "/" + newDbName;
            JdbcTemplate newJdbcTemplate = new JdbcTemplate();
            newJdbcTemplate.setDataSource(new DriverManagerDataSource(newDbUrl, username, password));

            // 给公共账号授权
            if (isTestEnvironment()) {
                // 测试环境给公共账号授权
                rootJdbcTemplate.execute("GRANT ALL PRIVILEGES ON " + newDbName + ".* TO '" + commUsername + "'@'%'");
            } else {
                // 生产环境给公共账号只读权限
                rootJdbcTemplate.execute("GRANT SELECT ON " + newDbName + ".* TO '" + commUsername + "'@'%'");
            }

            // 复制表结构 (以 1 库为模板)
            String templateDbName = baseDbName + "_1";

            List<Map<String, Object>> tables = rootJdbcTemplate.queryForList("SHOW TABLES FROM " + templateDbName);
            // 复制表结构
            for (Map<String, Object> table : tables) {
                String tableName = table.values().iterator().next().toString();
                Map<String, Object> createTableResult = rootJdbcTemplate
                        .queryForMap("SHOW CREATE TABLE " + templateDbName + "." + tableName);
                String createTableSql = (String) createTableResult.get("Create Table");

                // 修改建表语句
                createTableSql = createTableSql.replace("CREATE TABLE", "CREATE TABLE IF NOT EXISTS")
                        .replaceAll("AUTO_INCREMENT=\\d+", "");

                // 在新数据库中创建表
                newJdbcTemplate.execute(createTableSql);
                log.info("Created table: {} in database: {}", tableName, newDbName);
            }

            // 更新公司实体中的数据库信息
            company.setDbName(newDbName);

            // 创建数据库配置信息
            Map<String, String> dbConfig = new HashMap<>();
            dbConfig.put("host", host);
            dbConfig.put("port", port);
            dbConfig.put("database", newDbName);
            dbConfig.put("username", username);
            dbConfig.put("password", password);
            company.setDbConfig(JSONUtil.toJsonStr(dbConfig));

            return this.updateById(company);
        } catch (Exception e) {
            log.error("Failed to initialize database for company: " + company.getId(), e);
            return false;
        }
    }

    /**
     * 从数据库URL中提取主机名
     */
    private String extractHostFromUrl(String url) {
        // 格式: *******************************
        return url.split("//")[1].split(":")[0];
    }

    /**
     * 从数据库URL中提取端口号
     */
    private String extractPortFromUrl(String url) {
        // 格式: *******************************
        return url.split("//")[1].split(":")[1].split("/")[0];
    }

    /**
     * 从数据库URL中提取数据库名
     */
    private String extractDbNameFromUrl(String url) {
        // 格式: *******************************
        return url.split("//")[1].split("/")[1].split("\\?")[0];
    }

    /**
     * 判断是否为测试环境
     */
    private boolean isTestEnvironment() {
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if (!profile.equals(EnvEnum.PROD.getValue())) {
                return true;
            }
        }
        return false;
    }
}