package com.xc.boot.system.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.system.mapper.CompanySettingsMapper;
import com.xc.boot.system.model.bo.ImageSizeBO;
import com.xc.boot.system.model.entity.CompanySettingsEntity;
import com.xc.boot.system.model.enums.ImageSizeCtrlEnum;
import com.xc.boot.system.model.vo.CompanySettingsVO;
import com.xc.boot.system.service.CompanySettingsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.TimeUnit;

/**
 * 商户设置服务实现类
 */
@Slf4j
@Service
public class CompanySettingsServiceImpl extends ServiceImpl<CompanySettingsMapper, CompanySettingsEntity> implements CompanySettingsService {

    private static final String CACHE_KEY_PREFIX = "company:settings:";
    private static final long CACHE_EXPIRE_DAYS = 7;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public CompanySettingsVO getSettings() {
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return null;
        }

        String cacheKey = CACHE_KEY_PREFIX + companyId;
        
        // 尝试从缓存获取
        CompanySettingsVO settings = (CompanySettingsVO) redisTemplate.opsForValue().get(cacheKey);
        if (settings != null) {
            return settings;
        }

        // 缓存未命中，从数据库获取
        CompanySettingsEntity entity = this.getOne(
                QueryWrapper.create()
                        .from(CompanySettingsEntity.class)
                        .where(CompanySettingsEntity::getCompanyId).eq(companyId.intValue()));

        // 如果数据库中没有设置，创建默认设置
        if (entity == null) {
            entity = createDefaultSettings(companyId.intValue());
            try {
                this.save(entity);
                OpLogUtils.appendOpLog("商户设置-初始化", "初始化商户默认设置", null);
            } catch (Exception e) {
                log.error("保存默认商户设置失败", e);
                return null;
            }
        }

        // 转换为VO并缓存
        settings = CompanySettingsVO.fromEntity(entity);
        redisTemplate.opsForValue().set(cacheKey, settings, CACHE_EXPIRE_DAYS, TimeUnit.DAYS);
        
        return settings;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveSettings(CompanySettingsVO settings) {
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            return false;
        }

        CompanySettingsEntity entity = this.getOne(
                QueryWrapper.create()
                        .from(CompanySettingsEntity.class)
                        .where(CompanySettingsEntity::getCompanyId).eq(companyId.intValue()));

        boolean isUpdate = entity != null;
        if (!isUpdate) {
            entity = new CompanySettingsEntity();
            entity.setCompanyId(companyId.intValue());
        }

        // 更新实体
        entity.setImagePrintSize(settings.getImagePrintSize().toString());
        entity.setImageExportSize(settings.getImageExportSize().toString());
        entity.setIncomeAuditEnabled(settings.getIncomeAuditEnabled());
        entity.setTransferAuditEnabled(settings.getTransferAuditEnabled());
        entity.setReturnAuditEnabled(settings.getReturnAuditEnabled());
        entity.setGiftIncomeAuditEnabled(settings.getGiftIncomeAuditEnabled());
        entity.setGiftTransferAuditEnabled(settings.getGiftTransferAuditEnabled());
        entity.setZeroStockTakeEnabled(settings.getZeroStockTakeEnabled());

        // 保存到数据库
        boolean result = isUpdate ? this.updateById(entity) : this.save(entity);
        if (result) {
            // 更新缓存
            String cacheKey = CACHE_KEY_PREFIX + companyId;
            redisTemplate.opsForValue().set(cacheKey, settings, CACHE_EXPIRE_DAYS, TimeUnit.DAYS);

            // 记录操作日志
            String comment = isUpdate ? "商户设置-更新" : "商户设置-新增";
            String content = isUpdate ? "更新商户设置" : "新增商户设置";
            OpLogUtils.appendOpLog(comment, content, settings);
        }

        return result;
    }

    @Override
    public Long getCompanyId() {
        return SecurityUtils.getCompanyId();
    }

    /**
     * 创建默认商户设置
     *
     * @param companyId 商户ID
     * @return 默认商户设置实体
     */
    private CompanySettingsEntity createDefaultSettings(Integer companyId) {
        CompanySettingsEntity settings = new CompanySettingsEntity();
        settings.setCompanyId(companyId);

        // 设置默认图片打印尺寸
        ImageSizeBO defaultPrintSize = new ImageSizeBO();
        defaultPrintSize.setCtrl(ImageSizeCtrlEnum.FULL);
        defaultPrintSize.setWidth(200);
        defaultPrintSize.setHeight(200);
        settings.setImagePrintSize(defaultPrintSize.toString());

        // 设置默认图片导出尺寸
        ImageSizeBO defaultExportSize = new ImageSizeBO();
        defaultExportSize.setCtrl(ImageSizeCtrlEnum.FULL);
        defaultExportSize.setWidth(200);
        defaultExportSize.setHeight(200);
        settings.setImageExportSize(defaultExportSize.toString());

        // 设置默认审核开关
        settings.setIncomeAuditEnabled(true);
        settings.setTransferAuditEnabled(true);
        settings.setReturnAuditEnabled(true);
        settings.setGiftIncomeAuditEnabled(true);
        settings.setGiftTransferAuditEnabled(true);
        settings.setZeroStockTakeEnabled(false);

        return settings;
    }
} 