package com.xc.boot.system.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.enums.CategoryEnum;
import com.xc.boot.common.model.Option;
import com.xc.boot.common.util.ColumnEncryptUtil;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.core.security.model.SysUserDetails;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.merchant.mapper.GoodsColumnMapper;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateEntity;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnTypeEnum;
import com.xc.boot.modules.merchant.model.vo.GoodsIncomeTemplateDetailVO;
import com.xc.boot.modules.merchant.service.GoodsIncomeTemplateService;
import com.xc.boot.shared.common.model.query.OptionQuery;
import com.xc.boot.shared.common.service.OptionService;
import com.xc.boot.system.mapper.HeadingSignsMapper;
import com.xc.boot.system.mapper.HeadingUserCacheMapper;
import com.xc.boot.system.model.entity.HeadingSignsEntity;
import com.xc.boot.system.model.entity.HeadingUserCacheEntity;
import com.xc.boot.system.model.vo.HeadingColumns;
import com.xc.boot.system.model.vo.TableHeading;
import com.xc.boot.system.service.HeadingSignsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.xc.boot.system.model.entity.table.HeadingSignsTableDef.HEADING_SIGNS;
import static com.xc.boot.system.model.entity.table.HeadingUserCacheTableDef.HEADING_USER_CACHE;

/**
 * <AUTHOR>
 * @ClassName TableHeadingSignsServiceImpl
 * @Date: 2025/6/4 13:46
 * @Description: 动态表头配置实现类
 */
@Service
@RequiredArgsConstructor
public class HeadingSignsServiceImpl extends ServiceImpl<HeadingSignsMapper, HeadingSignsEntity>
        implements HeadingSignsService {
    private final HeadingUserCacheMapper headingUserCacheMapper;
    private final GoodsColumnMapper goodsColumnMapper;
    private final GoodsIncomeTemplateService goodsIncomeTemplateService;
    private final OptionService optionService;

    @Override
    @Transactional
    public TableHeading getTableHeading(String sign, Integer templateId) {
        SysUserDetails userDetails = SecurityUtils.getUser().orElse(new SysUserDetails());
        TableHeading vo = new TableHeading().setSign(sign);
        // 获取基础字段
        HeadingSignsEntity signsEntity = this.mapper.selectOneByQuery(QueryWrapper.create()
                .where(HEADING_SIGNS.SIGN.eq(sign)));
        Assert.notNull(signsEntity, "表头配置不存在");

        List<HeadingColumns> columns = JSONUtil.toList(signsEntity.getColumns(), HeadingColumns.class);

        // 是否需要自定义字段
        if (signsEntity.getNeedCustom().equals("1")) {
            List<GoodsColumnEntity> customer = goodsColumnMapper.selectListByQuery(QueryWrapper.create()
                    .where(GoodsColumnEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .where(GoodsColumnEntity::getCategory).eq(2));
            for (GoodsColumnEntity entity : customer) {
                columns.add(new HeadingColumns()
                        .setProp(entity.getSign())
                        .setLabel(entity.getName())
                        .setIsImg(entity.getType().equals(6))
                        .setShow(true));
            }
        }
        // 获取用户配置
        HeadingUserCacheEntity cacheEntity = headingUserCacheMapper.selectOneByQuery(QueryWrapper.create()
                .where(HEADING_USER_CACHE.SIGN.eq(sign))
                .where(HEADING_USER_CACHE.USER_ID.eq(userDetails.getUserId()))
                .where(HEADING_USER_CACHE.COMPANY_ID.eq(userDetails.getCompanyId())));

        // 没有用户缓存时,新增用户缓存并直接返回
        if (Objects.isNull(cacheEntity)) {
            cacheEntity = new HeadingUserCacheEntity()
                    .setUserId(userDetails.getUserId())
                    .setCompanyId(userDetails.getCompanyId())
                    .setSign(sign)
                    .setColumns(JSONUtil.toJsonStr(columns));
            headingUserCacheMapper.insert(cacheEntity);
            vo.setColumns(columns);
            return vo;
        }

        Set<String> props = columns.stream().map(HeadingColumns::getProp).collect(Collectors.toSet());
        List<HeadingColumns> cacheColumns = JSONUtil.toList(cacheEntity.getColumns(), HeadingColumns.class);
        // 检查字段是否一致
        boolean needUpdate = props.size() != cacheColumns.size();
        for (HeadingColumns column : cacheColumns) {
            if (!props.contains(column.getProp())) {
                needUpdate = true;
                break;
            }
        }
        // 更新用户字段
        if (needUpdate) {
            cacheEntity.setColumns(JSONUtil.toJsonStr(columns));
            headingUserCacheMapper.update(cacheEntity);
            vo.setColumns(columns);
            return vo;
        }

        // 处理自定义字段名称更新的问题
        for (HeadingColumns column : cacheColumns) {
            // 非自定义字段不处理
            if (!column.getProp().contains("-")) {
                continue;
            }

            GoodsColumnEntity goodsColumn = CommonUtils.getGoodsColumnsBySign(column.getProp());
            if (goodsColumn != null) {
                // 设置最新的字段名称
                column.setLabel(goodsColumn.getName());
            }
        }

        // 无变更,返回缓存字段
        vo.setColumns(cacheColumns);

        // 加工数据
        process(vo, templateId);

        return vo;
    }

    @Override
    public boolean saveTableHeadingCache(TableHeading tableHeading) {
        SysUserDetails userDetails = SecurityUtils.getUser().orElse(new SysUserDetails());

        // 获取修改前的缓存配置
        HeadingUserCacheEntity oldCache = headingUserCacheMapper.selectOneByQuery(QueryWrapper.create()
                .where(HEADING_USER_CACHE.USER_ID.eq(userDetails.getUserId()))
                .where(HEADING_USER_CACHE.COMPANY_ID.eq(userDetails.getCompanyId()))
                .where(HEADING_USER_CACHE.SIGN.eq(tableHeading.getSign())));

        HeadingUserCacheEntity cacheEntity = new HeadingUserCacheEntity()
                .setColumns(JSONUtil.toJsonStr(tableHeading.getColumns()));
        boolean result = headingUserCacheMapper.updateByQuery(cacheEntity, QueryWrapper.create()
                .where(HEADING_USER_CACHE.USER_ID.eq(userDetails.getUserId()))
                .where(HEADING_USER_CACHE.COMPANY_ID.eq(userDetails.getCompanyId()))
                .where(HEADING_USER_CACHE.SIGN.eq(tableHeading.getSign()))) > 0;

        if (result) {
            // 记录操作日志
            OpLogUtils.appendOpLog("表头配置-保存配置", "保存表头配置: " + tableHeading.getSign(),
                    Map.of("修改前", oldCache, "修改后", tableHeading));
        }

        return result;
    }

    private void process(TableHeading vo, Integer templateId) {
        // 入库单创建页面
        if (vo.getSign().equals("income_create")) {
            processIncomeCreate(vo, templateId);
        }
    }

    private void processIncomeCreate(TableHeading vo, Integer templateId) {
        if (templateId == null) {
            return;
        }

        // 用户是否可查看机密字段
        boolean canViewSecret = SecurityUtils.isRoot() || SecurityUtils.isMain() || SecurityUtils.showSecret();

        GoodsIncomeTemplateEntity template = goodsIncomeTemplateService.getTemplateById(templateId.longValue());

        // valueMap
        OptionQuery blankQuery = new OptionQuery();
        // sub_stone_id
        // main_stone_id
        Map<Long, String> jewelryMap = optionService.jewelry(blankQuery).stream()
                .collect(Collectors.toMap(Option::getValue, Option::getLabel));
        // brand_id
        Map<Long, String> brandMap = optionService.brand(blankQuery).stream()
                .collect(Collectors.toMap(Option::getValue, Option::getLabel));
        // style_id
        Map<Long, String> styleMap = optionService.style(blankQuery).stream()
                .collect(Collectors.toMap(Option::getValue, Option::getLabel));
        // quality_id
        Map<Long, String> qualityMap = optionService.quality(blankQuery).stream()
                .collect(Collectors.toMap(Option::getValue, Option::getLabel));
        // subclass_id
        Map<Long, String> subclassMap = optionService.subclass(blankQuery).stream()
                .collect(Collectors.toMap(Option::getValue, Option::getLabel));
        // supplier_id
        Map<Long, String> supplierMap = optionService.supplier(blankQuery).stream()
                .collect(Collectors.toMap(Option::getValue, Option::getLabel));
        // technology_id
        Map<Long, String> technologyMap = optionService.technology(blankQuery).stream()
                .collect(Collectors.toMap(Option::getValue, Option::getLabel));
        // counter_id
        Map<Long, String> counterMap = optionService.counter(blankQuery).stream()
                .collect(Collectors.toMap(Option::getValue, Option::getLabel));
        // merchant_id
        Map<Long, String> merchantMap = optionService.merchant(blankQuery).stream()
                .collect(Collectors.toMap(Option::getValue, Option::getLabel));
        // category_id
        Map<Long, String> categoryMap = CategoryEnum.toOptions(blankQuery).stream()
                .collect(Collectors.toMap(Option::getValue, Option::getLabel));

        // 获取模板
        List<GoodsIncomeTemplateDetailVO> templateDetails = goodsIncomeTemplateService
                .getTemplateDetailsWithColumn(templateId.longValue(), true);
        // 模板 map
        Map<String, GoodsIncomeTemplateDetailVO> templateMap = templateDetails.stream()
                .collect(Collectors.toMap(GoodsIncomeTemplateDetailVO::getSign, v -> v));

        List<HeadingColumns> columns = vo.getColumns();
        for (HeadingColumns column : columns) {
            Map<String, Object> extra = new HashMap<>();
            extra.put("valueMap", new HashMap<>());

            String columnSign = column.getProp();

            // 驼峰转下划线
            columnSign = StrUtil.toUnderlineCase(columnSign);
            if (columnSign.equals("category_name")) {
                columnSign = "category_id";
            }

            // 基础字段信息
            GoodsColumnEntity columnValue = CommonUtils.getGoodsColumnsBySign(columnSign);
            Map<String, Object> columnMap = new HashMap<>();

            columnMap.put("id", columnValue.getId().intValue());
            columnMap.put("name", columnValue.getName());
            columnMap.put("sign", columnValue.getSign());
            columnMap.put("secretLevel", columnValue.getSecretLevel());
            columnMap.put("category", columnValue.getCategory());
            columnMap.put("type", columnValue.getType());
            columnMap.put("isMultiple", columnValue.getIsMultiple());
            columnMap.put("numberPrecision", columnValue.getNumberPrecision());

            columnMap.put("options", new ArrayList<>());

            // valueMap
            switch (columnSign) {
                case "sub_stone_id":
                    extra.put("valueMap", jewelryMap);
                    break;
                case "main_stone_id":
                    extra.put("valueMap", jewelryMap);
                    break;
                case "brand_id":
                    extra.put("valueMap", brandMap);
                    break;
                case "style_id":
                    extra.put("valueMap", styleMap);
                    break;
                case "quality_id":
                    extra.put("valueMap", qualityMap);
                    break;
                case "subclass_id":
                    extra.put("valueMap", subclassMap);
                    break;
                case "category_id":
                    extra.put("valueMap", categoryMap);
                    break;
                case "supplier_id":
                    extra.put("valueMap", supplierMap);
                    break;
                case "technology_id":
                    extra.put("valueMap", technologyMap);
                    break;
                case "counter_id":
                    extra.put("valueMap", counterMap);
                    break;
                case "merchant_id":
                    extra.put("valueMap", merchantMap);
                    break;
                case "sales_type":
                    extra.put("valueMap", Map.of(1, "按重量", 2, "按数量"));
                    break;
                default:
                    break;
            }
            if (columnValue.getType().equals(GoodsColumnTypeEnum.SELECT.getValue())
                    && columnValue.getOptions() != null) {
                JSONArray options = JSONUtil.parseArray(columnValue.getOptions());
                columnMap.put("options", options);

                if (options != null && options.size() > 0) {
                    Map<String, String> optionsMap = new HashMap<>();

                    for (Object option : options) {
                        JSONObject optionObj = JSONUtil.parseObj(option);
                        optionsMap.put(optionObj.getStr("value"), optionObj.getStr("label"));
                    }

                    extra.put("valueMap", optionsMap);
                }
            }

            extra.put("column", columnMap);
            extra.put("columnSign", columnSign);
            extra.put("isEnabled", 0);

            // 模板信息
            Map<String, Object> templateItem = new HashMap<>();
            GoodsIncomeTemplateDetailVO templateDetail = templateMap.get(columnSign);
            if (templateDetail != null) {
                extra.put("isEnabled", templateDetail.getEnabled());

                templateItem.put("requiredFlag", templateDetail.getRequiredFlag());
                templateItem.put("imageId", templateDetail.getImageId());
                templateItem.put("enabled", templateDetail.getEnabled());

                // 下拉的默认值
                if (columnValue.getType().equals(GoodsColumnTypeEnum.SELECT.getValue())) {
                    // 获取现有的 options 的值
                    List<String> optionValues = new ArrayList<>();
                    if(extra.get("valueMap") != null){
                        ((Map<?, ?>) extra.get("valueMap")).forEach((key, value) -> {
                            optionValues.add(String.valueOf(key));
                        });
                    }

                    // 多选
                    if(columnValue.getIsMultiple().equals(1)){
                        String[] defaultValues = templateDetail.getDefaultValue().replace("[", "").replace("]", "")
                                .split(",");
                        templateItem.put("defaultValue", Arrays.asList(defaultValues).stream()
                                .filter(v -> !StrUtil.isBlank(v))
                                .filter(v -> optionValues.contains(v))
                                .map(String::trim)
                                .collect(Collectors.toList()));
                    }else{
                        templateItem.put("defaultValue", optionValues.contains(templateDetail.getDefaultValue()) ? templateDetail.getDefaultValue() : "");
                    }
                } else {
                    // 图片
                    if (columnValue.getType().equals(GoodsColumnTypeEnum.IMAGE.getValue())) {
                        if (StrUtil.isNotBlank(templateDetail.getDefaultValue())) {
                            Map<String, Object> defaultValue = new HashMap<>();
                            defaultValue.put("id", templateDetail.getImageId());
                            defaultValue.put("imageId", templateDetail.getImageId());
                            defaultValue.put("url", templateDetail.getDefaultValue());

                            templateItem.put("defaultValue", Arrays.asList(defaultValue));
                        } else {
                            // 处理空图片
                            templateItem.put("defaultValue", new ArrayList<>());
                        }
                    } else {
                        templateItem.put("defaultValue", templateDetail.getDefaultValue());
                    }
                }
                if (columnValue.getSecretLevel() != null && columnValue.getSecretLevel() > 1 && !canViewSecret) {
                    templateItem.put("defaultValue",
                            ColumnEncryptUtil.encryptFieldValue(columnSign, templateItem.get("defaultValue")));
                }

                extra.put("defaultValue", templateItem.get("defaultValue"));
            }
            // 默认值
            if (columnSign.equals("category_id")) {
                extra.put("defaultValue", template.getCategoryId());
            }

            extra.put("template", templateItem);
            extra.put("isCanViewSecret", canViewSecret ? 1 : 0);
            extra.put("isCanEdit", 0);

            // 字段启用 且 可查看机密字段 或 字段不加密 == 可编辑
            if ((Integer) extra.get("isEnabled") == 1
                    && ((Integer) extra.get("isCanViewSecret") == 1 || columnValue.getSecretLevel() == 1)) {
                extra.put("isCanEdit", 1);
            }

            column.setExtra(extra);
        }
    }
}
