package com.xc.boot.system.service.impl;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.system.mapper.PrintTagMapper;
import com.xc.boot.system.model.entity.PrintTagEntity;
import com.xc.boot.system.model.query.PrintTagPageQuery;
import com.xc.boot.system.model.vo.PrintTagPageVO;
import com.xc.boot.system.service.PrintTagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.xc.boot.system.model.dto.PrintTagFormDTO;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.xc.boot.system.model.dto.PrintTagConfigDTO;

import static com.mybatisflex.core.query.QueryMethods.*;

/**
 * 打印标签服务实现类
 */
@Slf4j
@Service
public class PrintTagServiceImpl extends ServiceImpl<PrintTagMapper, PrintTagEntity> implements PrintTagService {

    @Override
    public Page<PrintTagPageVO> getPrintTagPage(PrintTagPageQuery queryParams) {
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        column(PrintTagEntity::getId),
                        column(PrintTagEntity::getName),
                        column(PrintTagEntity::getType),
                        column(PrintTagEntity::getChipType),
                        column(PrintTagEntity::getMinDistance),
                        column(PrintTagEntity::getMaxDistance),
                        column(PrintTagEntity::getMinPower),
                        column(PrintTagEntity::getMaxPower),
                        column(PrintTagEntity::getWidth),
                        column(PrintTagEntity::getHeight),
                        column(PrintTagEntity::getPrintWidth),
                        column(PrintTagEntity::getPrintHeight),
                        column(PrintTagEntity::getImage),
                        column(PrintTagEntity::getImageId),
                        column(PrintTagEntity::getCover),
                        column(PrintTagEntity::getCoverId),
                        column(PrintTagEntity::getArgs),
                        column(PrintTagEntity::getEnabled),
                        column(PrintTagEntity::getRemark),
                        column(PrintTagEntity::getContent)
                )
                .from(PrintTagEntity.class)
                // 标签名称模糊查询
                .where(column(PrintTagEntity::getName).like(queryParams.getName(), StringUtils.hasText(queryParams.getName())))
                // 标签类型精确匹配
                .and(column(PrintTagEntity::getType).eq(queryParams.getType(), queryParams.getType() != null))
                // 芯片类型模糊查询
                .and(column(PrintTagEntity::getChipType).like(queryParams.getChipType(), StringUtils.hasText(queryParams.getChipType())))
                // 显示状态精确匹配
                .and(column(PrintTagEntity::getEnabled).eq(queryParams.getEnabled(), queryParams.getEnabled() != null))
                // 默认按ID降序排序
                .orderBy(column(PrintTagEntity::getId).desc());

        // 执行分页查询并直接映射到VO
        return mapper.paginateAs(queryParams.getPageNum(), queryParams.getPageSize(), queryWrapper, PrintTagPageVO.class);
    }

    @Override
    public boolean savePrintTag(PrintTagFormDTO form) {
        Long id = form.getId();
        PrintTagEntity oldEntity = null;
        if (id != null) {
            oldEntity = this.getById(id);
            Assert.isTrue(oldEntity != null, "标签不存在");
        }
        PrintTagEntity entity = new PrintTagEntity();
        BeanUtil.copyProperties(form, entity);
        boolean result = this.saveOrUpdate(entity);
        Assert.isTrue(result, "保存失败");

        // 更新文件使用状态
        if(form.getImageId() != null){
            CommonUtils.updateFileStatus(form.getImageId().longValue(), 1);
        }
        if(form.getCoverId() != null){
            CommonUtils.updateFileStatus(form.getCoverId().longValue(), 1);
        }

        if (id == null) {
            OpLogUtils.appendOpLog("打印标签-新增标签", "新增标签: " + form.getName(), form);
        } else {
            OpLogUtils.appendOpLog("打印标签-编辑标签", "编辑标签: " + form.getName(), java.util.Map.of("修改前", oldEntity, "修改后", form));
        }
        return result;
    }

    @Override
    public boolean deletePrintTag(Long id) {
        PrintTagEntity entity = this.getById(id);
        Assert.isTrue(entity != null, "标签不存在");
        
        // 更新图片使用状态为未使用
        if(entity.getImageId() != null){
            CommonUtils.updateFileStatus(entity.getImageId().longValue(), 0);
        }
        
        boolean result = this.removeById(id);
        Assert.isTrue(result, "删除失败");
        OpLogUtils.appendOpLog("打印标签-删除标签", "删除标签: " + entity.getName(), entity);
        return result;
    }

    @Override
    public boolean configPrintTag(PrintTagConfigDTO form) {
        // 获取原标签信息
        PrintTagEntity oldEntity = this.getById(form.getId());
        Assert.isTrue(oldEntity != null, "标签不存在");
        
        // 更新原图片状态为未使用
        if(oldEntity.getImageId() != null){
            CommonUtils.updateFileStatus(oldEntity.getImageId().longValue(), 0);
        }
        
        // 更新新图片状态为已使用
        if(form.getImageId() != null){
            CommonUtils.updateFileStatus(form.getImageId().longValue(), 1);
        }
        
        // 更新打印标签配置
        PrintTagEntity entity = new PrintTagEntity();
        entity.setId(form.getId());
        entity.setImage(form.getImage());
        entity.setImageId(form.getImageId());
        entity.setContent(form.getContent());
        
        boolean result = this.updateById(entity);
        
        // 记录操作日志
        if (result) {
            OpLogUtils.appendOpLog("打印标签-配置", "配置打印标签", java.util.Map.of("修改前", oldEntity, "修改后", form));
        }
        
        return result;
    }
} 