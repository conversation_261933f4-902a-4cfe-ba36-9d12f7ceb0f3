package com.xc.boot.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.constant.SecurityConstants;
import com.xc.boot.system.mapper.RoleMapper;
import com.xc.boot.system.mapper.RoleMenuMapper;
import com.xc.boot.system.model.bo.RolePermsBO;
import com.xc.boot.system.model.entity.SysMenuEntity;
import com.xc.boot.system.model.entity.SysRoleEntity;
import com.xc.boot.system.model.entity.SysRoleMenuEntity;
import com.xc.boot.system.service.RoleMenuService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * 角色菜单业务实现
 *
 * <AUTHOR>
 * @since 2.5.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RoleMenuServiceImpl extends ServiceImpl<RoleMenuMapper, SysRoleMenuEntity> implements RoleMenuService {

    private final RoleMapper roleMapper;
    @SuppressWarnings("unused")
    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 初始化权限缓存
     */
//    @PostConstruct
    public void initRolePermsCache() {
        log.info("初始化权限缓存... ");
        // 由于移除了权限字段，暂时不初始化权限缓存
    }

    /**
     * 刷新权限缓存
     */
    @Override
    public void refreshRolePermsCache() {
        // 由于移除了权限字段，暂时不刷新权限缓存
    }

    /**
     * 获取权限和拥有权限的角色列表
     */
    @Override
    public List<RolePermsBO> getRolePermsList(String roleCode) {
        return roleMapper.selectListWithRelationsByQueryAs(
                QueryWrapper.create().from(SysRoleEntity.class)
                        .select(
                                QueryMethods.column(SysRoleEntity::getCode).as(RolePermsBO::getRoleCode),
                                QueryMethods.column(SysRoleEntity::getId).as(RolePermsBO::getId)
                        )
                        .and(SysRoleEntity::getCode).eq(roleCode, StrUtil.isNotBlank(roleCode))
                        .and(SysRoleEntity::getStatus).eq(1),
                RolePermsBO.class
        );
    }

    /**
     * 刷新权限缓存
     */
    @Override
    public void refreshRolePermsCache(String roleCode) {
        // 由于移除了权限字段，暂时不刷新权限缓存
    }

    /**
     * 刷新权限缓存 (角色编码变更时调用)
     */
    @Override
    public void refreshRolePermsCache(String oldRoleCode, String newRoleCode) {
        // 由于移除了权限字段，暂时不刷新权限缓存
    }

    /**
     * 获取角色权限集合
     *
     * @param roles 角色编码集合
     * @return 权限集合
     */
    @Override
    public Set<String> getRolePermsByRoleCodes(Set<String> roles) {
        // 由于移除了权限字段，暂时返回空集合
        return new HashSet<>();
    }

    /**
     * 获取角色拥有的菜单ID集合
     *
     * @param roleId 角色ID
     * @return 菜单ID集合
     */
    @Override
    public List<Long> listMenuIdsByRoleId(Long roleId) {
        return this.listAs(
                QueryWrapper.create().from(SysRoleMenuEntity.class)
                        .select(QueryMethods.column(SysRoleMenuEntity::getMenuId))
                        .leftJoin(SysMenuEntity.class).on(q -> q.and(SysMenuEntity::getId).eq(SysRoleMenuEntity::getMenuId))
                        .where(SysRoleMenuEntity::getRoleId).eq(roleId),
                Long.class
        );
    }

}
