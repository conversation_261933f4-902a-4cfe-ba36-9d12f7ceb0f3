package com.xc.boot.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.constant.SystemConstants;
import com.xc.boot.common.model.Option;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.core.security.model.SysUserDetails;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.system.converter.RoleConverter;
import com.xc.boot.system.enums.PermissionSceneEnum;
import com.xc.boot.system.mapper.RoleMapper;
import com.xc.boot.system.mapper.RolePermissionMapper;
import com.xc.boot.system.mapper.UserRoleMapper;
import com.xc.boot.system.model.entity.SysPermissionEntity;
import com.xc.boot.system.model.entity.SysRoleEntity;
import com.xc.boot.system.model.entity.SysRolePermissionEntity;
import com.xc.boot.system.model.form.RoleForm;
import com.xc.boot.system.model.query.RolePageQuery;
import com.xc.boot.system.model.vo.PermissionVO;
import com.xc.boot.system.model.vo.RolePageVO;
import com.xc.boot.system.model.vo.RolePermissionVo;
import com.xc.boot.system.service.PermissionService;
import com.xc.boot.system.service.RoleService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.xc.boot.system.model.entity.table.SysPermissionTableDef.SYS_PERMISSION;
import static com.xc.boot.system.model.entity.table.SysRolePermissionTableDef.SYS_ROLE_PERMISSION;
import static com.xc.boot.system.model.entity.table.SysRoleTableDef.SYS_ROLE;
import static com.xc.boot.system.model.entity.table.SysUserRoleTableDef.SYS_USER_ROLE;


/**
 * 角色业务实现类
 *
 * <AUTHOR>
 * @since 2022/6/3
 */
@Service
@RequiredArgsConstructor
public class RoleServiceImpl extends ServiceImpl<RoleMapper, SysRoleEntity> implements RoleService {

    private final RoleConverter roleConverter;
    private final PermissionService permissionService;
    private final RolePermissionMapper rolePermissionMapper;
    private final UserRoleMapper userRoleMapper;

    /**
     * 角色分页列表
     * @param queryParams 角色查询参数
     * @return {@link Page< RolePageVO >} – 角色分页列表
     */
    @Override
    public Page<RolePageVO> getRolePage(RolePageQuery queryParams) {
        Long companyId = SecurityUtils.getCompanyId();
        // 查询参数
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(SYS_ROLE.COMPANY_ID.eq(companyId))
                .where(SYS_ROLE.STATUS.eq(queryParams.getStatus(), queryParams.getStatus() != null))
                .where(SYS_ROLE.NAME.like(queryParams.getName(), StringUtils.isNotBlank(queryParams.getName())));
        if (!SecurityUtils.isMain()) {
            queryWrapper.where(QueryMethods.exists(QueryWrapper.create().from(SYS_USER_ROLE)
                    .where(SYS_USER_ROLE.ROLE_ID.eq(SYS_ROLE.ID))
                    .where(SYS_USER_ROLE.USER_ID.eq(SecurityUtils.getUserId()))));
        }
        if (Objects.nonNull(queryParams.getTimeRange())) {
            queryWrapper.where(SYS_ROLE.CREATED_AT.ge(queryParams.getTimeRange()[0]));
            queryWrapper.where(SYS_ROLE.CREATED_AT.le(queryParams.getTimeRange()[1]));
        }
        queryWrapper.orderBy(SYS_ROLE.ID.desc());
        Page<SysRoleEntity> rolePage = this.page(new Page<>(pageNum, pageSize), queryWrapper);
        // 实体转换
        Page<RolePageVO> pageVo = roleConverter.toPageVo(rolePage);
        // 填充
        fillRolePermissionTree(pageVo.getRecords());
        return pageVo;
    }

    /**
     * 填充角色权限树
     * @param list 角色列表
     */
    public void fillRolePermissionTree(List<RolePageVO> list) {
        if (list.isEmpty()) {
            return;
        }
        // 如果有超级管理员，给超管设置所有权限
        RolePageVO root = list.stream().filter(e -> e.getCode().equals(SystemConstants.ROOT_ROLE_CODE)).findFirst().orElse(null);
        boolean hasRoot = Objects.nonNull(root);
        Set<Long> roleIds = list.stream().map(RolePageVO::getId).collect(Collectors.toSet());
        List<SysRolePermissionEntity> rolePermissions = rolePermissionMapper.selectListByQuery(QueryWrapper.create()
                .where(SYS_ROLE_PERMISSION.ROLE_ID.in(roleIds)));
        List<String> permissionSigns = rolePermissions.stream().map(SysRolePermissionEntity::getPermissionSign).toList();
        if (permissionSigns.isEmpty()) {
            return;
        }
        Map<String, SysPermissionEntity> permissionMap = permissionService.list(QueryWrapper.create()
                        .where(SYS_PERMISSION.SIGN.in(permissionSigns, !hasRoot)))
                .stream().collect(Collectors.toMap(SysPermissionEntity::getSign, e -> e));
        Map<Long, List<SysRolePermissionEntity>> roleIdPermissions = rolePermissions.stream().collect(Collectors.groupingBy(SysRolePermissionEntity::getRoleId));

        for (RolePageVO vo : list) {
            Long roleId = vo.getId();
            List<SysPermissionEntity> permissions;
            if (vo.getCode().equals(SystemConstants.ROOT_ROLE_CODE)) {
                permissions = permissionMap.values().stream().toList();
            }else {
                permissions = roleIdPermissions.getOrDefault(roleId, Collections.emptyList())
                        .stream().map(rolePermission -> permissionMap.get(rolePermission.getPermissionSign()))
                        .filter(Objects::nonNull)
                        .toList();
            }
            RolePermissionVo permissionVo = buildMultiTree(permissions);
            vo.setRolePermission(permissionVo);
        }
    }

    /**
     * 角色下拉列表
     *
     * @return {@link List<Option>} – 角色下拉列表
     */
    @Override
    public List<Option<Long>> listRoleOptions() {
        List<Long> curRoleIds = this.mapper.selectListByQuery(QueryWrapper.create()
                        .leftJoin(SYS_USER_ROLE).on(SYS_USER_ROLE.ROLE_ID.eq(SYS_ROLE.ID))
                        .where(SYS_ROLE.COMPANY_ID.eq(SecurityUtils.getCompanyId()))
                        .where(SYS_ROLE.STATUS.eq(1))
                        .where(SYS_USER_ROLE.USER_ID.eq(SecurityUtils.getUserId())))
                .stream().map(SysRoleEntity::getId).toList();
        if (curRoleIds.isEmpty() && !SecurityUtils.isMain()) {
            return new ArrayList<>();
        }
        // 查询数据
        List<SysRoleEntity> roleList = this.list(
                QueryWrapper.create().from(SysRoleEntity.class)
                        .select(QueryMethods.column(SysRoleEntity::getId), QueryMethods.column(SysRoleEntity::getName))
                        .where(SysRoleEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                        .where(SysRoleEntity::getId).in(curRoleIds, !SecurityUtils.isMain())
                        .ne(SysRoleEntity::getCode, SystemConstants.ROOT_ROLE_CODE, !SecurityUtils.isRoot())
        );

        // 实体转换
        return roleConverter.entities2Options(roleList);
    }

    /**
     * 保存角色
     *
     * @param roleForm 角色表单数据
     * @return {@link Boolean}
     */
    @Override
    @Transactional
    public boolean saveRole(RoleForm roleForm) {
        Long companyId = SecurityUtils.getCompanyId();
        long count = this.count(QueryWrapper.create()
                .where(SYS_ROLE.NAME.eq(roleForm.getName()))
                .where(SYS_ROLE.COMPANY_ID.eq(companyId)));
        Assert.isTrue(count == 0, "角色已存在");

        // 实体转换
        SysRoleEntity role = roleConverter.toEntity(roleForm);
        role.setCompanyId(companyId);
        role.setCode(IdUtil.nanoId(8));
        boolean result = this.save(role);
        role.setCode(SystemConstants.ROLE_CODE_PRE + role.getId());
        this.updateById(role);

        saveRolePermission(role.getId(), roleForm.getPermissions());

        // 记录操作日志
        OpLogUtils.appendOpLog("角色管理-新增角色", "新增角色: " + roleForm.getName(), roleForm);

        return result;
    }

    private void saveRolePermission(Long roleId, List<String> permissions) {
        if (Objects.isNull(roleId)) {
            return;
        }
        rolePermissionMapper.deleteByQuery(QueryWrapper.create()
                .where(SYS_ROLE_PERMISSION.ROLE_ID.eq(roleId)));
        if (Objects.isNull(permissions) || permissions.isEmpty()) {
            return;
        }
        List<SysRolePermissionEntity> rolePermissions = permissions.stream().map(permission -> {
            SysRolePermissionEntity rolePermission = new SysRolePermissionEntity();
            rolePermission.setRoleId(roleId);
            rolePermission.setPermissionSign(permission);
            return rolePermission;
        }).toList();
        rolePermissionMapper.insertBatch(rolePermissions);
    }

    /**
     * 批量删除角色
     *
     * @param ids 角色ID，多个使用英文逗号(,)分割
     * @return {@link Boolean}
     */
    @Override
    @Transactional
    public boolean deleteRoles(String ids) {
        Assert.isTrue(StrUtil.isNotBlank(ids), "删除的角色ID不能为空");
        List<Long> roleIds = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .toList();
        Assert.isTrue(!roleIds.isEmpty(), "删除的角色ID不能为空");
        for (Long roleId : roleIds) {
            this.removeById(roleId);
        }
        rolePermissionMapper.deleteByQuery(QueryWrapper.create()
                .where(SYS_ROLE_PERMISSION.ROLE_ID.in(roleIds)));
        userRoleMapper.deleteByQuery(QueryWrapper.create()
                .where(SYS_USER_ROLE.ROLE_ID.in(roleIds)));

        // 记录操作日志
        OpLogUtils.appendOpLog("角色管理-删除角色", "删除角色IDs: " + ids, 
            Map.of("删除的角色", this.listByIds(roleIds)));

        return true;
    }

    @Override
    @Transactional
    public boolean updateRole(RoleForm roleForm) {
        SysRoleEntity role = this.getById(roleForm.getId());
        Assert.notNull(role, "角色不存在");
        BeanUtil.copyProperties(roleForm, role);
        this.updateById(role);
        saveRolePermission(role.getId(), roleForm.getPermissions());

        // 记录操作日志
        OpLogUtils.appendOpLog("角色管理-编辑角色", "编辑角色: " + role.getName(), 
            Map.of("修改前", role, "修改后", roleForm));

        return true;
    }

    @Override
    public RolePermissionVo getCurUserPermTree() {
        List<SysPermissionEntity> curUserPermList = getCurUserPermList();
        return buildMultiTree(curUserPermList);
    }

    public List<SysPermissionEntity> getCurUserPermList() {
        SysUserDetails userDetails = SecurityUtils.getUser().orElse(new SysUserDetails());
        List<SysPermissionEntity> permissionEntities;
        if (SecurityUtils.isRoot()) {
            permissionEntities = permissionService.list(QueryWrapper.create()
                    .where(SYS_PERMISSION.SCENE.eq(PermissionSceneEnum.PC_ADMIN.getValue())));
        }else if (SecurityUtils.isMain()) {
            permissionEntities = permissionService.list(QueryWrapper.create()
                    .where(SYS_PERMISSION.SCENE.ne(PermissionSceneEnum.PC_ADMIN.getValue())));
        } else {
            permissionEntities = userRoleMapper.selectListByQueryAs(QueryWrapper.create()
                    .leftJoin(SYS_ROLE_PERMISSION).on(SYS_ROLE_PERMISSION.ROLE_ID.eq(SYS_USER_ROLE.ROLE_ID))
                    .leftJoin(SYS_PERMISSION).on(SYS_ROLE_PERMISSION.PERMISSION_SIGN.eq(SYS_PERMISSION.SIGN))
                    .leftJoin(SYS_ROLE).on(SYS_ROLE.ID.eq(SYS_USER_ROLE.ROLE_ID))
                    .where(SYS_USER_ROLE.USER_ID.eq(userDetails.getUserId()))
                    .where(SYS_ROLE.STATUS.eq(1))
                    .where(SYS_PERMISSION.ID.isNotNull())
                    .select(SYS_PERMISSION.ALL_COLUMNS), SysPermissionEntity.class);
        }
        return permissionEntities;
    }

    private RolePermissionVo buildMultiTree(List<SysPermissionEntity> permissionEntities) {
        Set<SysPermissionEntity> items = new HashSet<>(permissionEntities);
        List<PermissionVO> rootTree = new ArrayList<>();
        List<PermissionVO> pcTree = new ArrayList<>();
        List<PermissionVO> miniProgramTree = new ArrayList<>();
        List<PermissionVO> pdaTree = new ArrayList<>();
        items.forEach(entity -> {
            PermissionVO e = permissionService.convertToVO(entity);
            if (PermissionSceneEnum.PC_MERCHANT.getValue().equals(e.getScene())) {
                pcTree.add(e);
            }
            if (PermissionSceneEnum.MINI_PROGRAM.getValue().equals(e.getScene())) {
                miniProgramTree.add(e);
            }
            if (PermissionSceneEnum.PDA.getValue().equals(e.getScene())) {
                pdaTree.add(e);
            }
            if (PermissionSceneEnum.PC_ADMIN.getValue().equals(e.getScene())) {
                rootTree.add(e);
            }
        });
        RolePermissionVo permissionVo = new RolePermissionVo();
        permissionVo.setMiniProgramTree(permissionService.buildTree(miniProgramTree));
        permissionVo.setPdaTree(permissionService.buildTree(pdaTree));
        permissionVo.setRootTree(permissionService.buildTree(rootTree));
        permissionVo.setPcTree(permissionService.buildTree(pcTree));
        return permissionVo;
    }

}
