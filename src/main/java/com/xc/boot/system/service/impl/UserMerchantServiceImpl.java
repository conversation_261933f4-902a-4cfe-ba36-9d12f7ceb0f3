package com.xc.boot.system.service.impl;

import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.system.mapper.MerchantMapper;
import com.xc.boot.system.mapper.UserMerchantMapper;
import com.xc.boot.system.model.entity.MerchantEntity;
import com.xc.boot.system.model.entity.SysUserMerchantEntity;
import com.xc.boot.system.service.UserMerchantService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName UserMerchantServiceImpl
 * @Date: 2025/6/4 11:53
 * @Description: 用户关联门店实现类
 */
@Service
@RequiredArgsConstructor
public class UserMerchantServiceImpl extends ServiceImpl<UserMerchantMapper, SysUserMerchantEntity> implements UserMerchantService {
    private final MerchantMapper merchantMapper;

    @Override
    public Set<Long> getUserMerchants(Long userId, Boolean isMain) {
        // 主账号获取所有门店
        if (isMain) {
            return merchantMapper.selectListByQuery(QueryWrapper.create()).stream().map(MerchantEntity::getId).collect(Collectors.toSet());
        }
        // 非主账号获取用户门店
        List<Long> merchants = this.mapper.selectListByQueryAs(QueryWrapper.create()
                .where(SysUserMerchantEntity::getUserId).eq(userId)
                .select(QueryMethods.column(SysUserMerchantEntity::getMerchantId)), Long.class);
        return new HashSet<>(merchants);
    }
}
