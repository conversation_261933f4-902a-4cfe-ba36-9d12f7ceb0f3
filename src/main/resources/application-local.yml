server:
  port: 8989
spring:
  jackson:
    ## 默认序列化时间格式
    date-format: yyyy-MM-dd HH:mm:ss
    ## 默认序列化时区
    time-zone: GMT+8
  data:
    redis:
      database: 24
      host: r-bp1gz7itguey5qadqd.redis.rds.aliyuncs.com
      port: 6379
      # 如果Redis 服务未设置密码，需要将password删掉或注释，而不是设置为空字符串
      password: java_coder1:Zr1HALuAwhFfqg9qcqD
      timeout: 10s
      lettuce:
        pool:
          # 连接池最大连接数 默认8 ，负数表示没有限制
          max-active: 8
          # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认-1
          max-wait: -1
          # 连接池中的最大空闲连接 默认8
          max-idle: 8
          # 连接池中的最小空闲连接 默认0
          min-idle: 0
  cache:
    enabled: false
    # 缓存类型 redis、none(不使用缓存)
    type: redis
    # 缓存时间(单位：ms)
    redis:
      time-to-live: 3600000
      # 缓存null值，防止缓存穿透
      cache-null-values: true
  # 邮件配置
  mail:
    host: smtp.youlai.tech
    port: 587
    username: <EMAIL>
    password: 123456
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
    # 邮件发送者
    from: <EMAIL>
mybatis-flex:
  datasource:
    base:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ****************************************************************************************************************************************************************************************************************************
      username: fb_gold_java
      password: mkbWf1nmFH9j8TxBY1jcaE
    root:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ****************************************************************************************************************************************************************************************************************************
      username: root
      password: 2dkriT3UbnY84bmzmTWmeV$cBrx$tF

# 安全配置
security:
  jwt:
    # JWT 秘钥
    key: SecretKey012345678901234567890123456789012345678901234567890123456789
    # 访问令牌 有效期(单位：秒)，默认 三天
    access-token-time-to-live: 259200
  # 白名单列表
  ignore-urls:
    - /v3/api-docs/**
    - /doc.html
    - /swagger-resources/**
    - /webjars/**
    - /swagger-ui/**
    - /api/v1/auth/captcha
    - /api/v1/auth/refresh-token
    - /ws/**
    - /health

# 文件存储配置
oss:
  # OSS 类型 (目前支持aliyun、minio)
  type: aliyun
  # 阿里云OSS对象存储服务
  aliyun:
    # 服务Endpoint
    endpoint: oss-cn-hangzhou.aliyuncs.com
    # 访问凭据
    access-key-id: LTAI5tJNovHru1JendRQ5d1W
    # 凭据密钥
    access-key-secret: ******************************
    # 存储桶名称
    bucket-name: fb-gold-test
    # 外部访问地址
    external-url: https://goldimg-test.fbzs.net

# 短信配置
msg:
  url: https://smssh1.253.com/msg/variable/json
  app-key: N4026272
  app-secret: 4YzOxwLT6Sf41a
  template: 【飞彪助手】您的验证码为：{$var}，3分钟内输入有效，请勿泄露

# 金价接口
gold:
  url-gold: https://tsgold2.market.alicloudapi.com/shgold
  url-silver: https://tssilver.market.alicloudapi.com/silver/shgold
  app-code-gold: b101158e6c274033aa30b709ad6b0e3d
  app-code-silver: b101158e6c274033aa30b709ad6b0e3d

# knife4j 接口文档配置
knife4j:
  # 是否开启 Knife4j 增强功能
  enable: true  # 设置为 true 表示开启增强功能
  # 生产环境配置
  production: false  # 设置为 true 表示在生产环境中不显示文档，为 false 表示显示文档（通常在开发环境中使用）
  setting:
    language: zh_cn
  basic:
    enable: true
    # Basic认证用户名
    username: xc.api
    # Basic认证密码
    password: WIxvxomRlgN4Y8Q

# xxl-job 定时任务配置
xxl:
  job:
    # 定时任务开关
    enabled: false
    admin:
      # 多个地址使用,分割
      addresses: http://127.0.0.1:8080/xxl-job-admin
    accessToken: default_token
    executor:
      appname: xxl-job-executor-${spring.application.name}
      address:
      ip:
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30

# 验证码配置
captcha:
    # 验证码类型 circle-圆圈干扰验证码|gif-Gif验证码|line-干扰线验证码|shear-扭曲干扰验证码
    type: circle
    # 验证码宽度
    width: 120
    # 验证码高度
    height: 40
    # 验证码干扰元素个数
    interfere-count: 2
    # 文本透明度(0.0-1.0)
    text-alpha: 0.8
    # 验证码字符配置
    code:
      # 验证码字符类型 math-算术|random-随机字符
      type: math
      # 验证码字符长度，type=算术时，表示运算位数(1:个位数运算 2:十位数运算)；type=随机字符时，表示字符个数
      length: 1
    # 验证码字体
    font:
      # 字体名称 Dialog|DialogInput|Monospaced|Serif|SansSerif
      name: SansSerif
      # 字体样式 0-普通|1-粗体|2-斜体
      weight: 1
      # 字体大小
      size: 24
    # 验证码有效期(秒)
    expire-seconds: 120

# 微信小程配置
wx:
  miniapp:
    app-id: xxxxxx
    app-secret: xxxxxx