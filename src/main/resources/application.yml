spring:
  application:
    name: youlai-boot-flex
  profiles:
    active: local
  config:
    import: classpath:codegen.yml
  servlet:
    multipart:
      # 单个文件大小限制
      max-file-size: 10MB
      # 总上传数据大小限制
      max-request-size: 10MB

# 在 banner.txt 中显示项目版本，使用 @project.version@ 从 pom.xml 获取
project:
  version: @project.version@
logging:
  level:
    com:
      youlai: debug
    org:
      springframework: warn

mybatis-flex:
  mapper-locations: classpath*:/mapper/**/*.xml
  global-config:
    print-banner: false
    logic-delete-column: deletedAt
  configuration:
    auto-mapping-unknown-column-behavior: none
    default-executor-type: simple
    # 驼峰下划线转换
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    auto-mapping-behavior: full

# springdoc配置： https://springdoc.org/properties.html
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: alpha
    tags-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'Gold'
      paths-to-match: "/**"
      packages-to-scan:
        - com.xc.boot.system.controller
        - com.xc.boot.shared.auth.controller
        - com.xc.boot.shared.file.controller
        - com.xc.boot.shared.heading.controller
        - com.xc.boot.shared.common.controller
        - com.xc.boot.modules.merchant.controller
        - com.xc.boot.modules.income.controller
        - com.xc.boot.modules.pda.controller
        - com.xc.boot.modules.outcome.controller
        - com.xc.boot.modules.goods.controller
        - com.xc.boot.modules.gift.controller
        # - com.xc.boot.shared.codegen.controller
  default-flat-param-object: true